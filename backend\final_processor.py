#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終處理模組
處理最終分析結果並儲存更新
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from collections import defaultdict

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class FinalProcessor:
    """最終數據處理器"""
    
    def __init__(self, base_dir="backend"):
        self.base_dir = base_dir
        self.crawler_dir = os.path.join(base_dir, "crawler")
        self.processed_dir = os.path.join(self.crawler_dir, "processed")
        self.final_data_dir = os.path.join(self.processed_dir, "final_data")
        self.user_data_dir = os.path.join(self.processed_dir, "user_data")
        self.alldata_dir = os.path.join(self.processed_dir, "alldata")
        
        # 確保目錄存在
        os.makedirs(self.final_data_dir, exist_ok=True)
        os.makedirs(self.user_data_dir, exist_ok=True)
        os.makedirs(self.alldata_dir, exist_ok=True)
        
        logger.info(f"🔧 最終處理器初始化完成")
        logger.info(f"  - 基礎目錄: {self.base_dir}")
        logger.info(f"  - 最終數據目錄: {self.final_data_dir}")
        logger.info(f"  - 用戶數據目錄: {self.user_data_dir}")
        logger.info(f"  - 全部數據目錄: {self.alldata_dir}")
    
    def process_final_results(self, legislator_name: str) -> Dict[str, Any]:
        """
        處理最終分析結果
        
        Args:
            legislator_name: 立委姓名
            
        Returns:
            Dict: 處理後的結果
        """
        logger.info(f"🔄 開始處理 {legislator_name} 的最終分析結果...")
        
        # 載入最終分析結果
        final_data_file = os.path.join(self.final_data_dir, f"{legislator_name}_使用者分析.json")
        if not os.path.exists(final_data_file):
            logger.warning(f"⚠️ 最終分析結果文件不存在: {final_data_file}")
            return {}
        
        try:
            with open(final_data_file, 'r', encoding='utf-8') as f:
                final_data = json.load(f)
            
            logger.info(f"📊 載入 {len(final_data)} 筆最終分析結果")
            
            # 處理結果
            processed_results = self.enhance_final_data(final_data, legislator_name)
            
            # 保存處理後的結果
            enhanced_file = os.path.join(self.final_data_dir, f"{legislator_name}_enhanced_analysis.json")
            with open(enhanced_file, 'w', encoding='utf-8') as f:
                json.dump(processed_results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 增強結果已保存: {enhanced_file}")
            
            return processed_results
            
        except Exception as e:
            logger.error(f"❌ 處理最終結果失敗: {e}")
            return {}
    
    def enhance_final_data(self, final_data: List[Dict], legislator_name: str) -> List[Dict]:
        """增強最終數據"""
        enhanced_data = []
        
        for item in final_data:
            enhanced_item = item.copy()
            
            # 添加處理時間
            enhanced_item['processed_at'] = datetime.now().isoformat()
            
            # 添加立委信息
            enhanced_item['legislator'] = legislator_name
            
            # 標準化平台名稱
            platform = enhanced_item.get('平台', 'unknown')
            if platform.lower() in ['youtube', 'yt']:
                enhanced_item['平台'] = 'youtube'
            elif platform.lower() in ['ptt']:
                enhanced_item['平台'] = 'ptt'
            elif platform.lower() in ['threads', 'thread']:
                enhanced_item['平台'] = 'threads'
            elif platform.lower() in ['facebook', 'fb']:
                enhanced_item['平台'] = 'facebook'
            
            # 添加情感強度（基於情緒類型）
            emotion = enhanced_item.get('情緒', 'unknown')
            emotion_intensity = self.calculate_emotion_intensity(emotion)
            enhanced_item['情感強度'] = emotion_intensity
            
            # 添加立場強度（基於情感標籤）
            label = enhanced_item.get('情感標籤', 'unknown')
            label_intensity = self.calculate_label_intensity(label)
            enhanced_item['立場強度'] = label_intensity
            
            enhanced_data.append(enhanced_item)
        
        return enhanced_data
    
    def calculate_emotion_intensity(self, emotion: str) -> float:
        """計算情感強度"""
        emotion_intensities = {
            'anger': 0.9,
            'disgust': 0.8,
            'fear': 0.7,
            'surprise': 0.6,
            'sadness': 0.7,
            'joy': 0.8,
            'trust': 0.6,
            'anticipation': 0.5
        }
        return emotion_intensities.get(emotion.lower(), 0.5)
    
    def calculate_label_intensity(self, label: str) -> float:
        """計算立場強度"""
        if label == 'POSITIVE':
            return 0.8
        elif label == 'NEGATIVE':
            return 0.8
        else:
            return 0.5
    
    def generate_statistics_report(self, legislator_name: str) -> Dict[str, Any]:
        """生成統計報告"""
        logger.info(f"📊 開始生成 {legislator_name} 的統計報告...")
        
        # 載入最終數據
        final_data_file = os.path.join(self.final_data_dir, f"{legislator_name}_使用者分析.json")
        if not os.path.exists(final_data_file):
            logger.warning(f"⚠️ 最終數據文件不存在: {final_data_file}")
            return {}
        
        try:
            with open(final_data_file, 'r', encoding='utf-8') as f:
                final_data = json.load(f)
            
            # 計算統計數據
            total_users = len(final_data)
            
            # 平台分布
            platform_stats = defaultdict(int)
            for item in final_data:
                platform = item.get('平台', 'unknown')
                platform_stats[platform] += 1
            
            # 情感標籤分布
            label_stats = defaultdict(int)
            for item in final_data:
                label = item.get('情感標籤', 'unknown')
                label_stats[label] += 1
            
            # 情緒分布
            emotion_stats = defaultdict(int)
            for item in final_data:
                emotion = item.get('情緒', 'unknown')
                emotion_stats[emotion] += 1
            
            # 成功率
            success_count = sum(1 for item in final_data if item.get('情感標籤') not in ['Failed', 'Unknown'])
            success_rate = (success_count / total_users * 100) if total_users > 0 else 0
            
            # 構建統計報告
            statistics = {
                'legislator_name': legislator_name,
                'total_users': total_users,
                'success_count': success_count,
                'success_rate': round(success_rate, 2),
                'platform_distribution': dict(platform_stats),
                'label_distribution': dict(label_stats),
                'emotion_distribution': dict(emotion_stats),
                'generated_at': datetime.now().isoformat()
            }
            
            # 保存統計報告
            stats_file = os.path.join(self.final_data_dir, f"{legislator_name}_statistics.json")
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(statistics, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📊 統計報告已生成: {stats_file}")
            logger.info(f"  - 總用戶數: {total_users}")
            logger.info(f"  - 成功率: {success_rate}%")
            logger.info(f"  - 平台分布: {dict(platform_stats)}")
            
            return statistics
            
        except Exception as e:
            logger.error(f"❌ 生成統計報告失敗: {e}")
            return {}
    
    def merge_all_data(self, legislator_name: str) -> Dict[str, Any]:
        """合併所有數據"""
        logger.info(f"🔄 開始合併 {legislator_name} 的所有數據...")
        
        merged_data = {
            'legislator_name': legislator_name,
            'merged_at': datetime.now().isoformat(),
            'data_sources': {},
            'summary': {}
        }
        
        try:
            # 1. 載入用戶數據
            user_data_file = os.path.join(self.user_data_dir, f"{legislator_name}_gemini_format.json")
            if os.path.exists(user_data_file):
                with open(user_data_file, 'r', encoding='utf-8') as f:
                    user_data = json.load(f)
                merged_data['data_sources']['user_data'] = {
                    'file': user_data_file,
                    'count': len(user_data),
                    'loaded_at': datetime.now().isoformat()
                }
                merged_data['summary']['total_users'] = len(user_data)
                merged_data['summary']['total_comments'] = sum(
                    user_info.get('comment_count', 0) for user_info in user_data.values()
                )
            
            # 2. 載入最終分析結果
            final_data_file = os.path.join(self.final_data_dir, f"{legislator_name}_使用者分析.json")
            if os.path.exists(final_data_file):
                with open(final_data_file, 'r', encoding='utf-8') as f:
                    final_data = json.load(f)
                merged_data['data_sources']['final_analysis'] = {
                    'file': final_data_file,
                    'count': len(final_data),
                    'loaded_at': datetime.now().isoformat()
                }
                merged_data['summary']['analyzed_users'] = len(final_data)
            
            # 3. 載入統計報告
            stats_file = os.path.join(self.final_data_dir, f"{legislator_name}_statistics.json")
            if os.path.exists(stats_file):
                with open(stats_file, 'r', encoding='utf-8') as f:
                    stats_data = json.load(f)
                merged_data['data_sources']['statistics'] = {
                    'file': stats_file,
                    'loaded_at': datetime.now().isoformat()
                }
                merged_data['summary']['statistics'] = stats_data
            
            # 保存合併數據
            merged_file = os.path.join(self.alldata_dir, f"{legislator_name}_all_data.json")
            with open(merged_file, 'w', encoding='utf-8') as f:
                json.dump(merged_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 數據合併完成: {merged_file}")
            logger.info(f"📊 合併摘要:")
            logger.info(f"  - 用戶數據: {merged_data['summary'].get('total_users', 0)} 位")
            logger.info(f"  - 分析結果: {merged_data['summary'].get('analyzed_users', 0)} 位")
            logger.info(f"  - 總留言數: {merged_data['summary'].get('total_comments', 0)} 條")
            
            return merged_data
            
        except Exception as e:
            logger.error(f"❌ 數據合併失敗: {e}")
            return {}
    
    def validate_data_integrity(self, legislator_name: str) -> Dict[str, Any]:
        """驗證數據完整性"""
        logger.info(f"🔍 開始驗證 {legislator_name} 的數據完整性...")
        
        validation_results = {
            'legislator_name': legislator_name,
            'validated_at': datetime.now().isoformat(),
            'checks': {},
            'overall_status': 'unknown'
        }
        
        try:
            # 檢查用戶數據文件
            user_data_file = os.path.join(self.user_data_dir, f"{legislator_name}_gemini_format.json")
            if os.path.exists(user_data_file):
                with open(user_data_file, 'r', encoding='utf-8') as f:
                    user_data = json.load(f)
                
                validation_results['checks']['user_data'] = {
                    'exists': True,
                    'file_size': os.path.getsize(user_data_file),
                    'user_count': len(user_data),
                    'is_valid_json': True
                }
            else:
                validation_results['checks']['user_data'] = {
                    'exists': False,
                    'error': 'File not found'
                }
            
            # 檢查最終分析文件
            final_data_file = os.path.join(self.final_data_dir, f"{legislator_name}_使用者分析.json")
            if os.path.exists(final_data_file):
                with open(final_data_file, 'r', encoding='utf-8') as f:
                    final_data = json.load(f)
                
                validation_results['checks']['final_analysis'] = {
                    'exists': True,
                    'file_size': os.path.getsize(final_data_file),
                    'analysis_count': len(final_data),
                    'is_valid_json': True
                }
            else:
                validation_results['checks']['final_analysis'] = {
                    'exists': False,
                    'error': 'File not found'
                }
            
            # 檢查統計文件
            stats_file = os.path.join(self.final_data_dir, f"{legislator_name}_statistics.json")
            if os.path.exists(stats_file):
                with open(stats_file, 'r', encoding='utf-8') as f:
                    stats_data = json.load(f)
                
                validation_results['checks']['statistics'] = {
                    'exists': True,
                    'file_size': os.path.getsize(stats_file),
                    'is_valid_json': True
                }
            else:
                validation_results['checks']['statistics'] = {
                    'exists': False,
                    'error': 'File not found'
                }
            
            # 評估整體狀態
            all_checks = validation_results['checks'].values()
            if all(check.get('exists', False) for check in all_checks):
                validation_results['overall_status'] = 'complete'
            elif any(check.get('exists', False) for check in all_checks):
                validation_results['overall_status'] = 'partial'
            else:
                validation_results['overall_status'] = 'missing'
            
            # 保存驗證結果
            validation_file = os.path.join(self.final_data_dir, f"{legislator_name}_validation.json")
            with open(validation_file, 'w', encoding='utf-8') as f:
                json.dump(validation_results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"🔍 數據完整性驗證完成: {validation_file}")
            logger.info(f"  - 整體狀態: {validation_results['overall_status']}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ 數據完整性驗證失敗: {e}")
            return {}
    
    def cleanup_temp_files(self, legislator_name: str) -> bool:
        """清理臨時文件"""
        try:
            safe_name = legislator_name.replace(" ", "_")
            temp_dir = os.path.join(self.base_dir, "temp", safe_name)
            
            if os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir)
                logger.info(f"🧹 已清理臨時目錄: {temp_dir}")
                return True
            else:
                logger.info(f"📁 臨時目錄不存在: {temp_dir}")
                return True
                
        except Exception as e:
            logger.error(f"❌ 清理臨時文件失敗: {e}")
            return False

def process_legislator_final_data(legislator_name: str, base_dir: str = "backend") -> bool:
    """處理特定立委的最終數據"""
    processor = FinalProcessor(base_dir)
    
    try:
        # 1. 處理最終分析結果
        final_results = processor.process_final_results(legislator_name)
        if not final_results:
            logger.warning(f"⚠️ {legislator_name} 沒有最終分析結果需要處理")
            return False
        
        # 2. 生成統計報告
        statistics = processor.generate_statistics_report(legislator_name)
        if not statistics:
            logger.warning(f"⚠️ {legislator_name} 統計報告生成失敗")
        
        # 3. 合併所有數據
        merged_data = processor.merge_all_data(legislator_name)
        if not merged_data:
            logger.warning(f"⚠️ {legislator_name} 數據合併失敗")
        
        # 4. 驗證數據完整性
        validation = processor.validate_data_integrity(legislator_name)
        if not validation:
            logger.warning(f"⚠️ {legislator_name} 數據完整性驗證失敗")
        
        # 5. 清理臨時文件
        processor.cleanup_temp_files(legislator_name)
        
        logger.info(f"✅ {legislator_name} 最終數據處理完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ {legislator_name} 最終數據處理失敗: {e}")
        return False

def process_all_legislators_final_data(base_dir: str = "backend") -> bool:
    """處理所有立委的最終數據"""
    processor = FinalProcessor(base_dir)
    
    try:
        # 獲取所有立委列表
        if os.path.exists(processor.final_data_dir):
            final_files = [f for f in os.listdir(processor.final_data_dir) if f.endswith('_使用者分析.json')]
            legislators = [f.replace('_使用者分析.json', '') for f in final_files]
        else:
            logger.warning(f"⚠️ 最終數據目錄不存在: {processor.final_data_dir}")
            return False
        
        if not legislators:
            logger.warning("⚠️ 沒有找到任何立委的最終分析結果")
            return False
        
        logger.info(f"🔄 開始處理 {len(legislators)} 位立委的最終數據...")
        
        success_count = 0
        for legislator in legislators:
            if process_legislator_final_data(legislator, base_dir):
                success_count += 1
        
        logger.info(f"🎉 最終數據處理完成！成功處理 {success_count}/{len(legislators)} 位立委")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"❌ 處理所有立委最終數據失敗: {e}")
        return False

if __name__ == "__main__":
    # 測試最終處理器
    print("🧪 測試最終處理器...")
    
    try:
        # 測試處理器初始化
        processor = FinalProcessor()
        print(f"✅ 處理器初始化成功")
        
        # 測試情感強度計算
        intensity = processor.calculate_emotion_intensity('anger')
        print(f"✅ 情感強度計算成功: anger = {intensity}")
        
        # 測試立場強度計算
        label_intensity = processor.calculate_label_intensity('POSITIVE')
        print(f"✅ 立場強度計算成功: POSITIVE = {label_intensity}")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}") 