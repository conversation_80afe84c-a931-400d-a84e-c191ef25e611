import os
import json
import time
import threading
import re
import random
from datetime import datetime, timed<PERSON>ta
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.action_chains import ActionChains
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from json_utils import append_json_data, read_json_file, append_href_data, append_crawler_data, append_href_data_with_metadata

# 导入代理管理器
try:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from proxy_manager import get_proxy_for_selenium, mark_proxy_failed, force_change_ip_for_thread, get_proxy_stats, get_proxy_manager
    PROXY_AVAILABLE = True
except ImportError:
    print("⚠️ 代理管理器不可用，将使用直连模式")
    PROXY_AVAILABLE = False

def set_proxy_disabled(disabled: bool = True):
    """设置代理禁用状态"""
    global PROXY_AVAILABLE
    if disabled:
        PROXY_AVAILABLE = False
        print("🚫 代理功能已禁用")
        # 如果代理管理器已存在，设置为禁用状态
        try:
            manager = get_proxy_manager(disabled=True)
            print("✅ 代理管理器设置为禁用模式")
        except:
            pass
    else:
        PROXY_AVAILABLE = True
        print("🌐 代理功能已启用")

# 🚨 新增：YouTube优化器功能
class YouTubeOptimizer:
    """YouTube爬取优化器 - 集成到yt_crawler.py中"""
    
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
        
        self.delays = {
            'page_load': (2, 5),
            'scroll': (1, 3),
            'click': (0.5, 2),
            'comment_load': (1, 4)
        }
    
    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        return random.choice(self.user_agents)
    
    def random_delay(self, delay_type: str = 'default'):
        """随机延迟"""
        if delay_type in self.delays:
            min_delay, max_delay = self.delays[delay_type]
            delay = random.uniform(min_delay, max_delay)
        else:
            delay = random.uniform(1, 3)
        
        time.sleep(delay)
    
    def optimize_driver(self, driver):
        """优化WebDriver设置"""
        try:
            # 设置随机User-Agent
            user_agent = self.get_random_user_agent()
            driver.execute_script(f"Object.defineProperty(navigator, 'userAgent', {{get: function () {{return '{user_agent}';}}}});")
            
            # 禁用WebDriver检测
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 设置窗口大小
            driver.set_window_size(random.randint(1200, 1920), random.randint(800, 1080))
            
            # 添加随机鼠标移动
            driver.execute_script("""
                var event = new MouseEvent('mousemove', {
                    'view': window,
                    'bubbles': true,
                    'cancelable': true,
                    'clientX': arguments[0],
                    'clientY': arguments[1]
                });
                document.dispatchEvent(event);
            """, random.randint(100, 800), random.randint(100, 600))
            
            print("✅ WebDriver优化完成")
            
        except Exception as e:
            print(f"⚠️ WebDriver优化失败: {e}")
    
    def smart_scroll(self, driver, target_comments: int = 10000):
        """智能滚动，减少反爬虫检测"""
        try:
            current_comments = 0
            scroll_count = 0
            max_scrolls = 40
            no_change_count = 0
            
            print(f"🔄 开始智能滚动，目标评论数: {target_comments}")
            
            while current_comments < target_comments and scroll_count < max_scrolls:
                # 获取当前评论数
                try:
                    comments = driver.find_elements(By.CSS_SELECTOR, "#content-text")
                    new_count = len(comments)
                except:
                    new_count = 0
                
                if new_count == current_comments:
                    no_change_count += 1
                else:
                    no_change_count = 0
                    current_comments = new_count
                
                # 如果连续3次没有变化，尝试点击"显示更多"
                if no_change_count >= 3:
                    try:
                        show_more_buttons = driver.find_elements(By.CSS_SELECTOR, "ytd-button-renderer#more-replies")
                        if show_more_buttons:
                            for button in show_more_buttons[:20]:  # 最多点击3个
                                try:
                                    driver.execute_script("arguments[0].click();", button)
                                    self.random_delay('click')
                                except:
                                    continue
                            no_change_count = 0
                    except:
                        pass
                
                # 智能滚动
                scroll_height = random.randint(300, 800)
                driver.execute_script(f"window.scrollBy(0, {scroll_height});")
                
                # 随机延迟
                self.random_delay('scroll')
                
                scroll_count += 1
                
                # 每5次滚动检查一次
                if scroll_count % 5 == 0:
                    print(f"📈 滚动进度: {scroll_count}/{max_scrolls}, 评论数: {current_comments}")
                
                # 如果连续5次没有变化，退出
                if no_change_count >= 7:
                    print("⏹️ 连续无变化，停止滚动")
                    break
            
            print(f"✅ 智能滚动完成，最终评论数: {current_comments}")
            return current_comments
            
        except Exception as e:
            print(f"❌ 智能滚动失败: {e}")
            return 0
    
    def extract_comments_smart(self, driver) -> list:
        """智能提取评论，减少被检测"""
        try:
            comments = []
            
            # 等待评论加载
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "#content-text"))
            )
            
            # 获取评论元素
            comment_elements = driver.find_elements(By.CSS_SELECTOR, "ytd-comment-thread-renderer")
            
            for element in comment_elements:
                try:
                    # 随机延迟，模拟人类行为
                    self.random_delay('comment_load')
                    
                    # 提取评论内容
                    content_element = element.find_element(By.CSS_SELECTOR, "#content-text")
                    content = content_element.text.strip()
                    
                    if not content:
                        continue
                    
                    # 提取用户名
                    try:
                        author_element = element.find_element(By.CSS_SELECTOR, "#author-text")
                        author = author_element.text.strip()
                    except:
                        author = "匿名用户"
                    
                    # 提取时间
                    try:
                        time_element = element.find_element(By.CSS_SELECTOR, "yt-formatted-string.time")
                        time_text = time_element.text.strip()
                    except:
                        time_text = ""
                    
                    # 提取点赞数
                    try:
                        like_element = element.find_element(By.CSS_SELECTOR, "#vote-count-middle")
                        likes = like_element.text.strip()
                    except:
                        likes = "0"
                    
                    comment_data = {
                        'author': author,
                        'content': content,
                        'time': time_text,
                        'likes': likes,
                        'platform': 'youtube'
                    }
                    
                    comments.append(comment_data)
                    
                except Exception as e:
                    print(f"⚠️ 提取评论失败: {e}")
                    continue
            
            print(f"✅ 智能提取完成，共提取 {len(comments)} 条评论")
            return comments
            
        except Exception as e:
            print(f"❌ 智能提取评论失败: {e}")
            return []
    
    def handle_captcha(self, driver) -> bool:
        """处理验证码"""
        try:
            # 检查是否有验证码
            captcha_elements = driver.find_elements(By.CSS_SELECTOR, "iframe[src*='recaptcha']")
            if captcha_elements:
                print("⚠️ 检测到验证码，等待手动处理...")
                
                # 等待用户手动处理
                WebDriverWait(driver, 60).until(
                    lambda d: len(d.find_elements(By.CSS_SELECTOR, "iframe[src*='recaptcha']")) == 0
                )
                
                print("✅ 验证码处理完成")
                return True
            
            return False
            
        except TimeoutException:
            print("❌ 验证码处理超时")
            return False
        except Exception as e:
            print(f"❌ 验证码处理失败: {e}")
            return False
    
    def rotate_proxy_if_needed(self, driver, proxy_manager):
        """如果需要，轮换代理"""
        try:
            # 检查是否被限制
            restricted_elements = driver.find_elements(By.CSS_SELECTOR, "div[class*='error']")
            if restricted_elements:
                print("⚠️ 检测到访问限制，尝试轮换代理...")
                
                # 获取新代理
                new_proxy = proxy_manager.get_random_proxy()
                if new_proxy:
                    # 这里需要重新创建driver实例
                    print("🔄 代理轮换完成")
                    return True
            
            return False
            
        except Exception as e:
            print(f"❌ 代理轮换失败: {e}")
            return False

# 全局优化器实例
youtube_optimizer = YouTubeOptimizer()

def optimize_youtube_crawling(driver, target_comments: int = 10000):
    """优化YouTube爬取"""
    # 优化WebDriver
    youtube_optimizer.optimize_driver(driver)
    
    # 智能滚动
    comment_count = youtube_optimizer.smart_scroll(driver, target_comments)
    
    # 智能提取评论
    comments = youtube_optimizer.extract_comments_smart(driver)
    
    return comments

def handle_youtube_errors(driver, proxy_manager=None):
    """处理YouTube错误"""
    # 处理验证码
    if youtube_optimizer.handle_captcha(driver):
        return True
    
    # 轮换代理
    if proxy_manager and youtube_optimizer.rotate_proxy_if_needed(driver, proxy_manager):
        return True
    
    return False

def change_ip_for_thread(thread_id):
    """
    为指定线程更换IP地址
    
    参数:
        thread_id: 线程ID
    """
    if PROXY_AVAILABLE:
        try:
            new_proxy = force_change_ip_for_thread(thread_id)
            if new_proxy:
                print(f"🔄 线程 {thread_id}: 已强制更换IP地址为 {new_proxy}")
                return new_proxy
            else:
                print(f"⚠️ 线程 {thread_id}: 无法获取新IP地址")
                return None
        except Exception as e:
            print(f"❌ 线程 {thread_id}: 更换IP时出错: {e}")
            return None
    else:
        print(f"⚠️ 线程 {thread_id}: 代理管理器不可用，无法更换IP")
        return None

def get_proxy_with_retry(max_retries=3):
    """
    获取代理，带重试机制
    
    参数:
        max_retries: 最大重试次数
        
    返回:
        str: 代理地址，如果获取失败则返回None
    """
    if not PROXY_AVAILABLE:
        return None
        
    for attempt in range(max_retries):
        try:
            proxy = get_proxy_for_selenium()
            if proxy:
                print(f"✅ 成功获取代理: {proxy}")
                return proxy
            else:
                print(f"⚠️ 第 {attempt + 1} 次尝试获取代理失败")
        except Exception as e:
            print(f"❌ 第 {attempt + 1} 次获取代理出错: {e}")
        
        if attempt < max_retries - 1:
            print(f"⏳ 等待 2 秒后重试...")
            time.sleep(2)
    
    print("❌ 所有代理获取尝试都失败")
    return None

def check_proxy_health():
    """
    检查代理池健康状态
    
    返回:
        bool: 代理池是否健康
    """
    if not PROXY_AVAILABLE:
        return False
        
    try:
        stats = get_proxy_stats()
        print(f"📊 代理池状态: 可用={stats['working']}, 失败={stats['failed']}, 目标={stats['target']}")
        return stats['working'] >= stats['min_required']
    except Exception as e:
        print(f"❌ 检查代理池状态失败: {e}")
        return False

def initialize_driver(headless=True, retries=3, use_proxy=True, thread_id=None):
    """
    建立Chrome瀏覽器驅動，支援headless模式、代理與自動重試 - 集成优化器功能
    
    參數:
        headless: 是否使用無頭模式 (預設為True)
        retries: 重試次數 (預設為3次)
        use_proxy: 是否使用代理 (預設為True)
        thread_id: 線程ID，用於代理輪換
        
    返回:
        webdriver: Chrome瀏覽器驅動實例
    """
    # 隨機User-Agent列表，避免被識別為機器人
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
    ]
    
    for attempt in range(retries):
        try:
            chrome_options = Options()
            
            # 設定 headless 模式
            if headless:
                chrome_options.add_argument("--headless=new")  # 使用新版 headless 模式
                print("使用headless模式")
            else:
                print("使用非headless模式 (可見瀏覽器)")
            
            # 🔇 强化静音设置
            chrome_options.add_argument("--mute-audio")              # 静音所有音频
            chrome_options.add_argument("--disable-audio")          # 禁用音频系统
            chrome_options.add_argument("--disable-audio-output")   # 禁用音频输出
            chrome_options.add_argument("--disable-sound")          # 禁用声音
            chrome_options.add_argument("--autoplay-policy=no-user-gesture-required")  # 禁用自动播放
            
            # 基本設定
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")  # 避免 GPU 相關問題
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-infobars")
            chrome_options.add_argument("--disable-notifications")
            chrome_options.add_argument("--disable-background-timer-throttling")
            
            # 新增：反爬蟲設定
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--disable-features=site-per-process")
            chrome_options.add_argument("--disable-site-isolation-trials")
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            chrome_options.add_argument("--disable-features=TranslateUI")
            chrome_options.add_argument("--disable-features=BlinkGenPropertyTrees")
            chrome_options.add_argument("--disable-features=SkiaRenderer")
            chrome_options.add_argument("--disable-features=UseChromeOSDirectVideoDecoder")
            chrome_options.add_argument("--disable-features=VaapiVideoDecoder")
            chrome_options.add_argument("--disable-features=VaapiVideoEncoder")
            chrome_options.add_argument("--disable-features=VaapiVideoDecode")
            chrome_options.add_argument("--disable-features=VaapiVideoEncode")
            
            # 新增：解決渲染器問題
            chrome_options.add_argument("--disable-renderer-backgrounding")
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            
            # 新增：記憶體管理
            chrome_options.add_argument("--memory-pressure-off")
            chrome_options.add_argument("--max_old_space_size=2048")  # 減少記憶體使用
            chrome_options.add_argument("--js-flags=--max-old-space-size=2048")
            
            # 新增：網路設定
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--allow-running-insecure-content")
            
            # 🚨 增强代理設定
            current_proxy = None
            if use_proxy and PROXY_AVAILABLE:
                try:
                    # 检查代理池健康状态
                    if not check_proxy_health():
                        print("⚠️ 代理池不健康，尝试更新代理池...")
                        # 这里可以触发代理池更新
                    
                    # 获取代理，带重试机制
                    current_proxy = get_proxy_with_retry(max_retries=3)
                    if current_proxy:
                        chrome_options.add_argument(f'--proxy-server={current_proxy}')
                        print(f"✅ 使用代理: {current_proxy}")
                        
                        # 如果是特定线程，记录代理使用情况
                        if thread_id is not None:
                            print(f"🧵 线程 {thread_id} 使用代理: {current_proxy}")
                    else:
                        print("⚠️ 没有可用代理，使用直连")
                except Exception as e:
                    print(f"⚠️ 代理获取失败: {e}，使用直连")
            else:
                print("ℹ️ 未启用代理功能")
            
            # 🚨 集成优化器：使用优化器的User-Agent
            user_agent = youtube_optimizer.get_random_user_agent()
            chrome_options.add_argument(f'--user-agent={user_agent}')
            
            # 設定視窗大小 - 使用固定分辨率
            window_width = 1920
            window_height = 1080
            chrome_options.add_argument(f"--window-size={window_width},{window_height}")
            
            # 建立 WebDriver
            driver = webdriver.Chrome(options=chrome_options)
            
            # 設定頁面載入超時
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)
            
            # 🚨 集成优化器：应用WebDriver优化
            youtube_optimizer.optimize_driver(driver)
            
            print(f"✅ WebDriver 建立成功 (嘗試 {attempt + 1}/{retries})")
            
            # 如果使用了代理，测试代理连接
            if current_proxy:
                try:
                    driver.get("https://httpbin.org/ip")
                    time.sleep(2)
                    print(f"✅ 代理连接测试成功: {current_proxy}")
                except Exception as e:
                    print(f"⚠️ 代理连接测试失败: {e}")
                    # 标记代理失败
                    if PROXY_AVAILABLE:
                        mark_proxy_failed({'http': f'http://{current_proxy}', 'https': f'http://{current_proxy}'})
            
            return driver
            
        except Exception as e:
            print(f"❌ 嘗試 {attempt + 1} 失敗: {e}")
            
            # 如果是代理问题，标记代理失败并尝试更换
            if use_proxy and PROXY_AVAILABLE and current_proxy:
                try:
                    mark_proxy_failed({'http': f'http://{current_proxy}', 'https': f'http://{current_proxy}'})
                    print(f"⚠️ 代理 {current_proxy} 已标记为失败")
                    
                    # 如果是特定线程，尝试强制更换IP
                    if thread_id is not None:
                        new_proxy = change_ip_for_thread(thread_id)
                        if new_proxy:
                            print(f"🔄 线程 {thread_id} 已更换IP为: {new_proxy}")
                except:
                    pass
            
            if attempt == retries - 1:
                print("❌ 所有重試都失敗，無法建立 WebDriver")
                raise
            else:
                print(f"⏳ 等待 2 秒後重試...")
                time.sleep(2)

def search_youtube_videos(name, output_dir=None, headless=True, cutoff_date=None):
    """
    搜尋 YouTube 影片並收集 URL，支援日期篩選
    
    參數:
        name: 搜尋關鍵字
        output_dir: 輸出目錄
        headless: 是否使用無頭模式
        cutoff_date: 截止日期，只收集此日期之後的影片
    """
    # 如果沒有指定輸出目錄，使用標準路徑
    if output_dir is None:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(current_dir, 'href', 'youtube')
    
    os.makedirs(output_dir, exist_ok=True)
    driver = initialize_driver(headless)
    
    # 強制設置視窗大小
    if not headless:
        driver.maximize_window()
        driver.set_window_size(1920, 1080)
        print("🖥️  已設定瀏覽器視窗大小為 1920x1080")
    
    driver.get("https://www.youtube.com/")
    time.sleep(3)
    
    # 搜尋
    search_box = driver.find_element(By.NAME, "search_query")
    search_box.clear()
    search_box.send_keys(name)
    search_box.send_keys(Keys.RETURN)
    time.sleep(5)
    
    # 處理日期篩選 - 嚴格按照指定日期
    if cutoff_date:
        if isinstance(cutoff_date, str):
            try:
                cutoff_date = datetime.strptime(cutoff_date, '%Y-%m-%d')
                print(f"📅 嚴格篩選日期: {cutoff_date.strftime('%Y-%m-%d')} 之後的影片")
            except:
                cutoff_date = None
                print("⚠️ 日期格式錯誤，不進行日期篩選")
        print(f"📅 將篩選 {cutoff_date.strftime('%Y-%m-%d') if cutoff_date else '無'} 之後的影片")
    else:
        print("📝 不進行日期篩選，收集所有影片")
    
    video_data = []
    processed_urls = set()
    last_new_url_time = time.time()
    max_scrolls = 500  # 🔥 優化：300→500 大幅增加滾動次數以收集更多影片
    max_wait_time = 60  # 🔥 優化：45→60 增加等待時間，確保載入完整
    print(f"⚡ YouTube超速搜尋設置: 最大等待時間={max_wait_time}秒, 最大滾動次數={max_scrolls}")
    print(f"📏 每次下滑1200像素，總計最多下滑 {max_scrolls * 1200} 像素")
    print(f"🎯 目標：盡可能收集完整的影片列表")
    counter = 0
    
    # 準備即時寫入href檔案
    filename = f"{output_dir}/{name}.json"  # 統一命名為立委.json
    
    print(f"📁 href檔案路徑: {filename}")
    
    # 超时控制
    timeout_start = time.time()
    max_total_time = 600  # 🔥 超級優化：300→600秒 (10分鐘超時)
    
    print(f"🔍 開始收集 {name} 的YouTube影片URL...")
    print(f"⏰ URL收集超時設置: {max_total_time}秒（{max_total_time//60}分鐘）")
    
    while counter <= max_scrolls:
        # 檢查總時間超時
        if time.time() - timeout_start > max_total_time:
            print(f"⏰ YouTube搜尋超時（{max_total_time}秒），結束搜尋")
            break
            
        try:
            # 滾動頁面載入更多影片 - 🔥 超级优化
            driver.execute_script("window.scrollBy(0, 1200)")  # 增加滚动距离：3000→1200
            time.sleep(1)  # 减少等待：2→1
            
            # 檢查是否有「沒有更多結果」的提示
            no_more_results = driver.find_elements(By.XPATH, "//span[contains(text(), '沒有更多結果') or contains(text(), 'No more results')]")
            if no_more_results:
                print("📄 YouTube: 已到達搜尋結果底部")
                break
            
            video_containers = driver.find_elements(By.CSS_SELECTOR, "ytd-video-renderer")
            new_url_found = False
            
            print(f"🔍 YouTube: 第{counter+1}次滾動，找到 {len(video_containers)} 個影片容器")
            
            for container in video_containers:
                try:
                    thumbnail = container.find_element(By.CSS_SELECTOR, "a#thumbnail")
                    url = thumbnail.get_attribute('href')
                    
                    # 獲取影片發布時間（重要：這裡要正確獲取）
                    try:
                        # 嘗試多種時間元素選擇器 - 修復版本
                        time_elements = [
                            ".//span[contains(@class, 'style-scope ytd-video-meta-block')][2]",
                            ".//span[contains(@class, 'ytd-video-meta-block')][position()=last()]",
                            ".//div[@id='metadata-line']//span[2]",
                            ".//div[@id='metadata-line']//span[contains(text(), '前')]",
                            ".//span[contains(@class, 'style-scope') and contains(@class, 'ytd-video-meta-block')]",
                            ".//div[contains(@class, 'metadata-line')]//span[contains(text(), '前')]",
                            ".//yt-formatted-string[contains(text(), '前')]",
                            ".//span[contains(text(), '天前') or contains(text(), '小時前') or contains(text(), '分鐘前') or contains(text(), '個月前') or contains(text(), '年前')]"
                        ]
                        
                        upload_time = None
                        for i, selector in enumerate(time_elements):
                            try:
                                time_element = container.find_element(By.XPATH, selector)
                                potential_time = time_element.text.strip()

                                # 清理時間文字中的多餘空格（修復"16 小時前"問題）
                                potential_time = ' '.join(potential_time.split())

                                # 驗證是否為時間格式
                                if any(keyword in potential_time for keyword in ['前', '天', '小時', '分鐘', '秒', '個月', '年', '週', '星期']):
                                    upload_time = potential_time
                                    break
                            except Exception as e:
                                # 只在嘗試所有選擇器後才輸出錯誤
                                if i == len(time_elements) - 1:
                                    print(f"⚠️  所有時間元素選擇器都失敗")
                                continue
                        
                        if not upload_time:
                            upload_time = "Unknown"
                            actual_date = datetime.now().strftime('%Y-%m-%d')
                            # 只在找不到時間時才輸出警告
                            if counter % 10 == 0:  # 每10次才輸出一次，避免日誌過多
                                print(f"⚠️  未找到時間信息，使用當前日期: {actual_date}")
                        else:
                            actual_date = convert_relative_time(upload_time)
                            
                    except Exception as e:
                        upload_time = "Unknown"
                        actual_date = datetime.now().strftime('%Y-%m-%d')
                        if counter % 10 == 0:  # 每10次才輸出一次，避免日誌過多
                            print(f"❌ 獲取時間失敗: {e}，使用當前日期: {actual_date}")
                    
                    if url and url not in processed_urls:
                        # 過濾掉 YouTube Shorts
                        if '/shorts/' in url:
                            continue
                        
                        # 日期篩選（在URL收集階段）- 修復邏輯，使用精確時間比較
                        if cutoff_date and upload_time != "Unknown":
                            try:
                                # 獲取影片的精確時間（包含小時）
                                video_datetime = convert_relative_time_to_datetime(upload_time)
                                cutoff_date_only = cutoff_date.replace(hour=0, minute=0, second=0, microsecond=0)

                                # 修復：使用精確時間比較，收集 >= cutoff_date 的影片
                                if video_datetime >= cutoff_date_only:
                                    print(f"✅ 收集新影片: {upload_time} ({actual_date}) - 精確時間: {video_datetime.strftime('%Y-%m-%d %H:%M')}")
                                else:
                                    continue

                            except Exception as date_err:
                                # 日期解析失敗，保留此影片
                                print(f"⚠️ 日期解析失敗: {actual_date} -> {date_err}，將保留此影片")
                                pass
                            
                        processed_urls.add(url)
                        data_entry = {
                            'url': url,
                            'time': upload_time,
                            'date': actual_date,
                            'post_time': actual_date,  # 新增：用於快速篩選的標準化時間
                            'added_time': datetime.now().strftime('%Y-%m-%d')
                        }
                        video_data.append(data_entry)
                        new_url_found = True
                        last_new_url_time = time.time()
                        
                        # 即時寫入href檔案（每收集5個URL就append一次）
                        if len(video_data) % 5 == 0:
                            # 使用append模式寫入新收集的完整數據結構
                            current_batch = video_data[-5:] if len(video_data) >= 5 else video_data
                            append_href_data_with_metadata("youtube", name, current_batch)
                            print(f"📝 YouTube: 已收集 {len(video_data)} 個影片URL，append到href檔案")
                        elif len(video_data) % 3 == 0:
                            print(f"🔄 YouTube: 已收集 {len(video_data)} 個影片URL（持續搜尋中...）")
                            
                except Exception as e:
                    print(f"⚠️  處理影片容器時出錯: {e}")
                    continue
                    
            # 檢查是否需要繼續滾動
            if not new_url_found and (time.time() - last_new_url_time) > max_wait_time:
                print(f"⏰ YouTube: 60秒內沒有新影片，結束搜尋")  # 直接使用固定數字，避免變數問題
                break
            
            # 檢查影片數量是否足夠
            if len(video_data) >= 2000:  # 🔥 優化：1000→2000 增加最大影片數量限制
                print(f"📊 YouTube: 已收集足夠影片數量({len(video_data)})，結束搜尋")
                break
                
            counter += 1
            
            # 每10次滾動輸出進度
            if counter % 10 == 0:
                print(f"📄 YouTube: 已滾動 {counter} 次，收集到 {len(video_data)} 個影片")
                
        except Exception as e:
            print(f"⚠️  YouTube滾動時發生錯誤: {e}")
            time.sleep(2)
    
    driver.quit()
    
    # 最終append所有剩餘數據（包含完整metadata）
    if video_data:
        append_href_data_with_metadata("youtube", name, video_data)
        print(f"✅ YouTube: 搜尋完成，總共收集 {len(video_data)} 個影片URL")
        
        # 輸出時間統計
        if video_data:
            dates = [entry['date'] for entry in video_data if entry['date'] != datetime.now().strftime('%Y-%m-%d')]
            if dates:
                print(f"📊 收集的影片日期範圍: {min(dates)} 到 {max(dates)}")
        
        return filename, video_data
    else:
        print(f"⚠️  未找到符合條件的影片URL，請檢查搜尋關鍵字或日期篩選條件")
        # 建立空檔案，避免後續處理錯誤
        empty_data = []
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(empty_data, f, ensure_ascii=False, indent=2)
        return filename, empty_data

def scroll_to_bottom(driver):
    """滾動到底部，使用优化器的智能滚动功能"""
    print("🔥 使用优化器智能滚动模式")
    
    # 使用优化器的智能滚动功能
    comment_count = youtube_optimizer.smart_scroll(driver, target_comments=10000)  # 调整为更合理的数量
    
    # 获取最终页面高度
    last_height = driver.execute_script("return document.documentElement.scrollHeight")
    print(f"✅ 智能滚动完成，最终页面高度: {last_height}, 评论数: {comment_count}")
    
    return last_height

def get_buttons(driver):
    """滾動頁面並獲取所有可點擊的按鈕"""
    last_height = scroll_to_bottom(driver)  # 滾動頁面到底部，並獲取最終頁面高度
    reply_buttons = driver.find_elements(By.CSS_SELECTOR, 'button[aria-label*="則回覆"]')
    more_reply_buttons = driver.find_elements(By.CSS_SELECTOR, 'button[aria-label*="顯示更多回覆"]')
    show_more_buttons = driver.find_elements(By.CSS_SELECTOR, 'span[aria-label*="顯示完整內容"]')
    return reply_buttons + more_reply_buttons + show_more_buttons, last_height  # 返回按鈕與最終頁面高度

def click_buttons(driver):
    """
    按順序逐一點擊按鈕，直到按鈕數量不再變化或已經點擊所有按鈕，加入錯誤處理和重試機制
    🔥 超级优化版本：大幅减少循环次数和等待时间 + 強制超時防卡住
    
    Args:
        driver: Selenium WebDriver 實例
        
    Returns:
        None
    """
    wait = WebDriverWait(driver, 8)  # 🔥 超级优化：10→8 (-20%)
    actions = ActionChains(driver)
    last_height = 0
    initial_buttons, _ = get_buttons(driver)
    total_buttons = len(initial_buttons)
    clicked_buttons = 0
    max_retries = 1  # 🔥 超级优化：2→1 (-50%)
    
    # 🔥 超级优化：大幅减少循环计数器
    max_loops = 8  # 🔥 超级优化：15→8 (-47%)
    loop_count = 0
    
    # 🚨 新增：強制超時機制
    click_start_time = time.time()
    max_click_time = 120  # 最多點擊2分鐘
    
    print(f"🔥 超速模式：最多處理{max_loops}個循環，目標點擊{min(total_buttons, 5)}個按鈕")
    print(f"⏰ 點擊超時設置: 最多{max_click_time}秒")
    
    while loop_count < max_loops:
        # 🚨 檢查時間超時
        if time.time() - click_start_time > max_click_time:
            print(f"⏰ 點擊超時({max_click_time}秒)，強制退出")
            break
            
        loop_count += 1
        try:
            buttons, current_height = get_buttons(driver)

            if len(buttons) == 0:
                print("沒有找到可點擊的按鈕，退出點擊循環")
                break

            # 🔥 超级优化：限制点击按钮数量
            if clicked_buttons >= 15:  # 🔥 超级优化：30→15 (-50%)
                print(f"已點擊{clicked_buttons}個按鈕，達到限制，停止處理")
                break

            # 判斷是否頁面滾動到底部或按鈕數量沒有變化
            if current_height == last_height:
                print(f"頁面高度沒有變化，可能已經到底部：{current_height}")
                # 🔥 超级优化：减少确认滚动次数
                for _ in range(1):  # 🔥 超级优化：2→1 (-50%)
                    driver.execute_script("window.scrollBy(0, 1200);")  # 🔥 超级优化：800→1200 (+50%)
                    time.sleep(0.3)  # 🔥 超级优化：0.5→0.3 (-40%)
                new_height = driver.execute_script("return document.documentElement.scrollHeight")
                if new_height == current_height:
                    print("⛔ 頁面高度確認無變化，退出點擊循環")
                    break
                else:
                    current_height = new_height
                    # 繼續處理

            # 比較目前按鈕數量與已計算的數量
            if len(buttons) <= clicked_buttons:
                print(f"已點擊按鈕數量：{clicked_buttons}，當前按鈕數量：{len(buttons)}，可能沒有新按鈕")
                break
            
            # 🔥 超级优化：如果已经点击了足够的按钮，也可以退出
            if clicked_buttons >= min(total_buttons, 5):  # 🔥 超级优化：8→5 (-37%)
                print(f"已點擊足夠按鈕（{clicked_buttons}個）")
                break
            
            clicked = False
            for i, button in enumerate(buttons):
                # 🚨 再次檢查超時
                if time.time() - click_start_time > max_click_time:
                    print(f"⏰ 點擊過程中超時，立即退出")
                    return
                    
                if i % 5 == 0:  # 🔥 超级优化：每隔3个→每隔5个 (+67%)
                    retry_count = 0
                    while retry_count < max_retries:
                        try:                    
                            # 先捲動到按鈕附近，確保它在視窗內
                            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                            time.sleep(0.2)  # 🔥 超级优化：0.3→0.2 (-33%)
                            
                            # 移動到按鈕並嘗試點擊
                            actions.move_to_element(button).perform()
                            time.sleep(0.2)  # 🔥 超级优化：0.3→0.2 (-33%)
                            
                            # 確保按鈕可點擊
                            wait.until(EC.element_to_be_clickable(button))
                            
                            # 嘗試用JavaScript點擊，這通常更可靠
                            driver.execute_script("arguments[0].click();", button)
                            clicked = True
                            clicked_buttons += 1
                            print(f"✅ 成功點擊第 {clicked_buttons} 個按鈕 (循環{loop_count}/{max_loops})")
                            
                            # 給予頁面響應時間
                            time.sleep(0.5)  # 🔥 超级优化：0.8→0.5 (-37.5%)
                            break  # 成功點擊，跳出重試循環
                        except Exception as e:
                            retry_count += 1
                            print(f"點擊按鈕時發生錯誤 (重試 {retry_count}/{max_retries}): {e}")
                            # 短暫暫停後重試
                            time.sleep(0.3)  # 🔥 超级优化：0.5→0.3 (-40%)
                            
                            # 如果已經重試到最大次數，則嘗試下一個按鈕
                            if retry_count >= max_retries:
                                print(f"放棄點擊當前按鈕，嘗試下一個")
                                continue
                    
                    if clicked:
                        break  # 點擊一個按鈕後結束當前循環

            # 如果沒有成功點擊任何按鈕，嘗試滾動頁面並重新獲取按鈕
            if not clicked:
                print("當前循環中沒有點擊任何按鈕，嘗試滾動頁面")
                driver.execute_script("window.scrollBy(0, 1200);")  # 🔥 超级优化：800→1200 (+50%)
                time.sleep(0.5)  # 🔥 超级优化：1→0.5 (-50%)
                # 再檢查一次是否有新的按鈕
                new_buttons, _ = get_buttons(driver)
                if len(new_buttons) <= len(buttons):
                    print("滾動後沒有發現新按鈕，退出循環")
                    break
            
            last_height = current_height
            time.sleep(0.5)  # 🔥 超级优化：1→0.5 (-50%)
            
        except Exception as e:
            print(f"點擊按鈕過程中發生錯誤: {e}")
            # 不中斷循環，繼續嘗試點擊其他按鈕
            time.sleep(0.5)  # 🔥 超级优化：1→0.5 (-50%)

        # 向下滾動，便於加載新內容
        driver.execute_script("window.scrollBy(0, 1200);")  # 🔥 超级优化：600→800 (+33%)
        time.sleep(0.5)
        
    print(f"🏁 點擊按鈕程序結束：共點擊了 {clicked_buttons} 個按鈕，總共處理了 {loop_count} 個循環")

def convert_relative_time(relative_time):
    """
    將相對時間（如 '3 天前'、'直播時間：1 天前'）轉換為日期字符串（如 '2023-05-01'）
    支持多種格式，包括常見的中文相對時間表達
    
    Args:
        relative_time: 相對時間字符串
        
    Returns:
        日期字符串，格式為 YYYY-MM-DD
    """
    now = datetime.now()
    
    # 清理字符串，移除前綴如「直播時間：」、「首播：」等
    cleaned_time = relative_time.strip()
    if '：' in cleaned_time:
        cleaned_time = cleaned_time.split('：')[-1].strip()
    if ':' in cleaned_time:
        cleaned_time = cleaned_time.split(':')[-1].strip()
    
    try:
        # 處理各種時間格式，注意空格
        if " 天前" in cleaned_time:
            match = re.search(r'(\d+)\s*天前', cleaned_time)
            if match:
                days = int(match.group(1))
                actual_time = now - timedelta(days=days)
            else:
                actual_time = now
        elif " 小時前" in cleaned_time:
            match = re.search(r'(\d+)\s*小時前', cleaned_time)
            if match:
                hours = int(match.group(1))
                actual_time = now - timedelta(hours=hours)
            else:
                actual_time = now
        elif " 分鐘前" in cleaned_time:
            match = re.search(r'(\d+)\s*分鐘前', cleaned_time)
            if match:
                minutes = int(match.group(1))
                actual_time = now - timedelta(minutes=minutes)
            else:
                actual_time = now
        elif " 秒前" in cleaned_time:
            match = re.search(r'(\d+)\s*秒前', cleaned_time)
            if match:
                seconds = int(match.group(1))
                actual_time = now - timedelta(seconds=seconds)
            else:
                actual_time = now
        elif " 個月前" in cleaned_time:
            match = re.search(r'(\d+)\s*個月前', cleaned_time)
            if match:
                months = int(match.group(1))
                actual_time = now - timedelta(days=months*30)  # 粗略估計一個月為30天
            else:
                actual_time = now
        elif " 年前" in cleaned_time:
            match = re.search(r'(\d+)\s*年前', cleaned_time)
            if match:
                years = int(match.group(1))
                actual_time = now - timedelta(days=years*365)  # 粗略估計一年為365天
            else:
                actual_time = now
        elif " 週前" in cleaned_time:
            match = re.search(r'(\d+)\s*週前', cleaned_time)
            if match:
                weeks = int(match.group(1))
                actual_time = now - timedelta(weeks=weeks)
            else:
                actual_time = now
        elif " 星期前" in cleaned_time:
            match = re.search(r'(\d+)\s*星期前', cleaned_time)
            if match:
                weeks = int(match.group(1))
                actual_time = now - timedelta(weeks=weeks)
            else:
                actual_time = now
        else:
            # 如果無法解析，返回當前時間
            print(f"⚠️ 無法解析時間格式: '{cleaned_time}'，使用當前時間")
            actual_time = now
            
    except Exception as e:
        print(f"❌ 解析時間時出錯: {relative_time} -> {e}，使用當前時間")
        actual_time = now
    
    return actual_time.strftime('%Y-%m-%d')

def convert_relative_time_to_datetime(relative_time):
    """
    將相對時間轉換為精確的datetime對象（包含小時信息）

    Args:
        relative_time: 相對時間字符串

    Returns:
        datetime對象
    """
    now = datetime.now()

    # 清理字符串，移除前綴如「直播時間：」、「首播：」等
    cleaned_time = relative_time.strip()
    if '：' in cleaned_time:
        cleaned_time = cleaned_time.split('：')[-1].strip()
    if ':' in cleaned_time:
        cleaned_time = cleaned_time.split(':')[-1].strip()

    try:
        # 處理各種時間格式，注意空格
        if " 天前" in cleaned_time:
            match = re.search(r'(\d+)\s*天前', cleaned_time)
            if match:
                days = int(match.group(1))
                actual_time = now - timedelta(days=days)
            else:
                actual_time = now
        elif " 小時前" in cleaned_time:
            match = re.search(r'(\d+)\s*小時前', cleaned_time)
            if match:
                hours = int(match.group(1))
                actual_time = now - timedelta(hours=hours)
            else:
                actual_time = now
        elif " 分鐘前" in cleaned_time:
            match = re.search(r'(\d+)\s*分鐘前', cleaned_time)
            if match:
                minutes = int(match.group(1))
                actual_time = now - timedelta(minutes=minutes)
            else:
                actual_time = now
        elif " 秒前" in cleaned_time:
            match = re.search(r'(\d+)\s*秒前', cleaned_time)
            if match:
                seconds = int(match.group(1))
                actual_time = now - timedelta(seconds=seconds)
            else:
                actual_time = now
        elif " 個月前" in cleaned_time:
            match = re.search(r'(\d+)\s*個月前', cleaned_time)
            if match:
                months = int(match.group(1))
                actual_time = now - timedelta(days=months * 30)  # 近似計算
            else:
                actual_time = now
        elif " 年前" in cleaned_time:
            match = re.search(r'(\d+)\s*年前', cleaned_time)
            if match:
                years = int(match.group(1))
                actual_time = now - timedelta(days=years * 365)  # 近似計算
            else:
                actual_time = now
        elif " 週前" in cleaned_time:
            match = re.search(r'(\d+)\s*週前', cleaned_time)
            if match:
                weeks = int(match.group(1))
                actual_time = now - timedelta(weeks=weeks)
            else:
                actual_time = now
        else:
            # 無法識別的格式，使用當前時間
            actual_time = now

    except Exception as e:
        print(f"❌ 解析時間時出錯: {relative_time} -> {e}，使用當前時間")
        actual_time = now

    return actual_time

def crawl_youtube_comments(name, headless=True, num_threads=4, last_crawled_time=None):
    """
    爬取 YouTube 評論，支援日期篩選
    
    參數:
        name: 要搜尋的關鍵字
        headless: 是否使用無界面模式
        num_threads: 執行緒數量
        last_crawled_time: 上次爬取的時間點 (datetime 物件或字串)，如果提供，只爬取此時間後的影片
    """
    # 處理 last_crawled_time 參數
    cutoff_date = None
    if last_crawled_time:
        if isinstance(last_crawled_time, str):
            try:
                # 嘗試多種常見的日期格式
                for date_format in ['%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y', '%Y%m%d']:
                    try:
                        cutoff_date = datetime.strptime(last_crawled_time, date_format)
                        break
                    except ValueError:
                        continue
                
                # 如果以上格式都不符合，嘗試 ISO 格式
                if cutoff_date is None:
                    cutoff_date = datetime.fromisoformat(last_crawled_time.replace('Z', '+00:00'))
            except Exception as e:
                print(f"無法解析日期格式: {last_crawled_time}，錯誤: {e}")
                print("將不使用日期篩選")
                cutoff_date = None
        elif isinstance(last_crawled_time, datetime):
            cutoff_date = last_crawled_time
            print(f"使用提供的 datetime 物件作為日期篩選: {cutoff_date.strftime('%Y-%m-%d')}")
    
    if cutoff_date:
        print(f"將篩選 {cutoff_date.strftime('%Y-%m-%d')} 之後的影片")
    else:
        print("未指定日期篩選，將爬取所有影片")
    
    # 第一步：獲取所有影片 URL
    print(f"正在搜尋 {name} 的 YouTube 影片...")
    url_json_file, url_data = search_youtube_videos(name, headless=headless)
    
    # 第二步：從爬取的 URL 數據中排除 shorts
    all_urls = [item["url"] for item in url_data if "/shorts/" not in item["url"]]
    print(f"找到 {len(all_urls)} 個影片 URL（排除 shorts）")
    
    # 第三步：如果已存在輸出文件，讀取以避免重複處理
    output_dir = './data/youtube'  # 統一資料夾結構
    os.makedirs(output_dir, exist_ok=True)
    output_file = f'{output_dir}/{name}.json'  # 統一命名為立委.json
    processed_video_urls = set()
    existing_videos_data = []
    
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                existing_videos_data = json.load(f)
                
            # 從現有數據中提取已處理的視頻信息
            for video_data in existing_videos_data:
                # 假設有一個字段存儲了原始URL，如果沒有可以修改此邏輯
                if 'video_url' in video_data:
                    processed_video_urls.add(video_data['video_url'])
                    
            print(f"從現有數據中找到 {len(processed_video_urls)} 個已處理的影片")
        except Exception as e:
            print(f"讀取現有數據時出錯: {e}")
    
    # 第四步：準備要爬取的 URL 列表
    to_crawl_urls = []
    for url in all_urls:
        # 跳過已處理的URL
        if url in processed_video_urls:
            continue
        
        # 如果有時間資訊，可以在這裡進行初步篩選
        # 由於 url_data 中的時間不一定準確，將在實際爬取時再做詳細篩選
        to_crawl_urls.append(url)
    
    print(f"需要爬取 {len(to_crawl_urls)} 個新影片")
    
    # 如果沒有新影片需要爬取，直接返回
    if not to_crawl_urls:
        print(f"沒有新影片需要爬取，返回現有數據")
        return output_file
    
    # 第五步：設置多執行緒爬取影片
    all_videos_data = existing_videos_data.copy()
    all_videos_data_lock = threading.Lock()
    
    def process_video_url(url_list, thread_id):
        """處理一組YouTube URLs的工作執行緒函數 - 增强代理轮换功能"""
        print(f"🧵 執行緒 {thread_id} 開始處理 {len(url_list)} 個影片URL")
        
        # 🚨 增强代理轮换：检查代理池健康状态
        if PROXY_AVAILABLE:
            if not check_proxy_health():
                print(f"⚠️ 執行緒 {thread_id}: 代理池不健康，尝试强制更新...")
                # 强制更换IP
                new_proxy = change_ip_for_thread(thread_id)
                if new_proxy:
                    print(f"🔄 執行緒 {thread_id}: 已强制更换IP为 {new_proxy}")
        
        # 嘗試建立瀏覽器，最多重試3次
        driver = None
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 🚨 增强：传递线程ID给initialize_driver
                driver = initialize_driver(headless, retries=1, use_proxy=True, thread_id=thread_id)
                print(f"✅ 執行緒 {thread_id} 成功建立瀏覽器")
                break
            except Exception as e:
                print(f"❌ 執行緒 {thread_id} 嘗試 {attempt+1}/{max_retries} 建立瀏覽器失敗: {e}")
                
                # 🚨 增强：如果是代理问题，尝试更换IP
                if PROXY_AVAILABLE and ('proxy' in str(e).lower() or 'connection' in str(e).lower()):
                    print(f"🔄 執行緒 {thread_id}: 检测到代理问题，尝试更换IP...")
                    new_proxy = change_ip_for_thread(thread_id)
                    if new_proxy:
                        print(f"✅ 執行緒 {thread_id}: 已更换IP为 {new_proxy}")
                
                if attempt < max_retries - 1:
                    time.sleep(3 * (attempt + 1))  # 逐漸增加等待時間
                else:
                    print(f"❌ 執行緒 {thread_id} 無法建立瀏覽器，放棄執行")
                    return
        
        if not driver:
            print(f"❌ 執行緒 {thread_id} 无法创建WebDriver，跳过处理")
            return
        
        try:
            for i, url in enumerate(url_list):
                try:
                    print(f"🎬 執行緒 {thread_id} 處理第 {i+1}/{len(url_list)} 個URL: {url}")
                    
                    # 🚨 增强：每处理5个URL检查一次代理健康状态
                    if i > 0 and i % 5 == 0 and PROXY_AVAILABLE:
                        if not check_proxy_health():
                            print(f"⚠️ 執行緒 {thread_id}: 处理第{i}个URL时代理池不健康，尝试更换IP...")
                            new_proxy = change_ip_for_thread(thread_id)
                            if new_proxy:
                                print(f"🔄 執行緒 {thread_id}: 已更换IP为 {new_proxy}")
                    
                    # 使用重試機制載入頁面
                    page_load_success = False
                    for load_attempt in range(3):
                        try:
                            driver.get(url)
                            time.sleep(3)  # 減少等待時間：10→3秒
                            page_load_success = True
                            break
                        except Exception as load_e:
                            if load_attempt < 2:
                                print(f"⚠️ 執行緒 {thread_id}: 頁面載入失敗，重試第 {load_attempt+2} 次: {load_e}")
                                
                                # 🚨 增强：如果是网络问题，尝试更换IP
                                if PROXY_AVAILABLE and ('timeout' in str(load_e).lower() or 'connection' in str(load_e).lower()):
                                    print(f"🔄 執行緒 {thread_id}: 检测到网络问题，尝试更换IP...")
                                    new_proxy = change_ip_for_thread(thread_id)
                                    if new_proxy:
                                        print(f"✅ 執行緒 {thread_id}: 已更换IP为 {new_proxy}")
                                
                                time.sleep(2)
                                try:
                                    driver.get("about:blank")
                                    time.sleep(1)
                                except:
                                    pass
                            else:
                                print(f"❌ 執行緒 {thread_id}: 無法載入頁面 {url}: {load_e}")
                                continue
                    
                    if not page_load_success:
                        continue
                    
                    # 嘗試暫停影片播放
                    try:
                        video_element = driver.find_element(By.CSS_SELECTOR, 'video')
                        driver.execute_script("arguments[0].pause();", video_element)
                        print(f"⏸️ 執行緒 {thread_id}: 已暫停影片播放")
                    except Exception as e:
                        print(f"⚠️ 執行緒 {thread_id}: 無法暫停影片: {e}")
                    
                    # 快速滾動頁面以確保加載留言
                    driver.execute_script("window.scrollBy(0, 450);")
                    time.sleep(5)  # 減少等待時間：5→2秒

                    
                    # 獲取影片標題
                    try:
                        # 使用多個備用選擇器
                        title_selectors = [
                            'h1.ytd-video-primary-info-renderer',
                            '#title.style-scope.ytd-watch-metadata',
                            'h1.ytd-video-primary-info-renderer-title',
                            'h1.ytd-video-primary-info-renderer h1'
                        ]
                        
                        video_title = None
                        for selector in title_selectors:
                            try:
                                title_element = WebDriverWait(driver, 5).until(
                                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                                )
                                video_title = title_element.text.strip()
                                if video_title:
                                    break
                            except Exception:
                                continue
                        
                        if video_title:
                            print(f"📺 執行緒 {thread_id}: 成功獲取影片標題: {video_title[:30]}...")
                        else:
                            print(f"⚠️ 執行緒 {thread_id}: 無法獲取影片標題")
                            continue
                    except Exception as e:
                        print(f"❌ 執行緒 {thread_id}: 無法獲取影片標題: {e}")
                        continue
                    
                    # 獲取觀看次數
                    try:
                        parent_element = driver.find_element(By.CSS_SELECTOR, 'div#leading-section')
                        child_elements = parent_element.find_elements(By.CSS_SELECTOR, 'span.style-scope.yt-formatted-string')
                        span_number = 1
                        for span in child_elements:
                            text = span.text.strip()
                            cleaned_text = text.replace(',', '').strip()
                            if cleaned_text.isdigit():
                                span_number = int(cleaned_text)
                    except Exception:
                        span_number = 1
                    
                    if span_number < 1:
                        continue
                    
                    # 設置評論排序為最新
                    try:
                        comment_order = driver.find_element(By.CSS_SELECTOR, '#label.dropdown-trigger.style-scope.yt-dropdown-menu')
                        comment_order.click()
                        time.sleep(1)
                        comment_order_true = driver.find_element(By.XPATH, '//tp-yt-paper-item[@role="option" and contains(.,"由新到舊")]')
                        comment_order_true.click()
                    except NoSuchElementException:
                        pass
                    time.sleep(1)  # 減少等待時間：2→1秒
                    
                    # 展開所有評論
                    try:
                        expand_button = driver.find_element(By.XPATH, '//*[@id="expand"]')
                        expand_button.click()
                        time.sleep(1)  # 減少等待時間：2→1秒
                    except NoSuchElementException:
                        pass
                    
                    # 滾動載入更多評論
                    last_height = driver.execute_script("return document.body.scrollHeight")
                    scroll_attempts = 0
                    max_scroll_attempts = 5  # 減少滾動次數：10→5次
                    
                    while scroll_attempts < max_scroll_attempts:
                        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        time.sleep(1)  # 減少等待時間：2→1秒
                        
                        new_height = driver.execute_script("return document.body.scrollHeight")
                        if new_height == last_height:
                            scroll_attempts += 1
                        else:
                            scroll_attempts = 0
                            last_height = new_height
                    
                    # 提取評論數據
                    try:
                        comment_threads = driver.find_elements(By.CSS_SELECTOR, 'ytd-comment-thread-renderer')
                        print(f"執行緒 {thread_id}: 找到 {len(comment_threads)} 個評論線程")
                        
                        video_comments = []
                        for thread in comment_threads:
                            try:
                                comment_data = extract_comment_data(thread, driver)
                                if comment_data:
                                    video_comments.append(comment_data)
                            except Exception as comment_e:
                                print(f"執行緒 {thread_id}: 提取評論時出錯: {comment_e}")
                                continue
                        
                        # 保存影片數據
                        if video_comments:
                            video_data = {
                                'video_url': url,
                                'video_title': video_title,
                                'view_count': span_number,
                                'comments': video_comments,
                                'comment_count': len(video_comments),
                                'crawl_time': datetime.now().isoformat()
                            }
                            
                            with all_videos_data_lock:
                                all_videos_data.append(video_data)
                            
                            print(f"執行緒 {thread_id}: 成功提取 {len(video_comments)} 條評論")
                        else:
                            print(f"執行緒 {thread_id}: 未找到任何評論")
                            
                    except Exception as extract_e:
                        print(f"執行緒 {thread_id}: 提取評論數據時出錯: {extract_e}")
                    
                    # 添加隨機延遲，避免被限流
                    delay = random.uniform(1, 3)  # 減少延遲：2-5→1-3秒
                    print(f"執行緒 {thread_id}: 等待 {delay:.1f} 秒...")
                    time.sleep(delay)
                    
                except Exception as url_e:
                    print(f"執行緒 {thread_id}: 處理URL時出錯: {url_e}")
                    continue
                    
        except Exception as e:
            print(f"執行緒 {thread_id}: 執行緒執行時出錯: {e}")
        finally:
            if driver:
                try:
                    driver.quit()
                    print(f"執行緒 {thread_id}: 瀏覽器已關閉")
                except Exception as quit_e:
                    print(f"執行緒 {thread_id}: 關閉瀏覽器時出錯: {quit_e}")
    
    # 多執行緒分配
    print(f"🚀 開始多執行緒處理 {len(to_crawl_urls)} 個影片URL...")
    
    # 動態調整執行緒數量，避免過度並行
    if len(to_crawl_urls) <= 5:
        actual_threads = 1  # 少量URL使用單執行緒
    elif len(to_crawl_urls) <= 20:
        actual_threads = min(2, num_threads)  # 中等數量使用2個執行緒
    else:
        actual_threads = min(3, num_threads)  # 大量URL使用3個執行緒
    
    print(f"📊 使用 {actual_threads} 個執行緒處理 {len(to_crawl_urls)} 個URL")
    
    # 分批處理URL，避免單個執行緒處理過多URL
    batch_size = max(1, len(to_crawl_urls) // actual_threads)
    url_batches = []
    
    for i in range(0, len(to_crawl_urls), batch_size):
        batch = to_crawl_urls[i:i + batch_size]
        url_batches.append(batch)
    
    print(f"📦 將URL分成 {len(url_batches)} 個批次，每批約 {batch_size} 個URL")
    
    # 使用ThreadPoolExecutor進行並行處理
    with ThreadPoolExecutor(max_workers=actual_threads) as executor:
        # 提交所有批次任務
        future_to_batch = {
            executor.submit(process_video_url, batch, i+1): i+1 
            for i, batch in enumerate(url_batches)
        }
        
        # 監控任務完成情況
        completed_tasks = 0
        total_tasks = len(future_to_batch)
        
        for future in as_completed(future_to_batch):
            batch_id = future_to_batch[future]
            completed_tasks += 1
            
            try:
                future.result()  # 獲取結果，如果有異常會在這裡拋出
                print(f"✅ 批次 {batch_id}/{total_tasks} 完成 ({completed_tasks}/{total_tasks})")
            except Exception as e:
                print(f"❌ 批次 {batch_id}/{total_tasks} 失敗: {e}")
    
    print(f"🎉 所有批次處理完成！共處理 {len(to_crawl_urls)} 個URL")
    
    # 保存最終結果
    if all_videos_data:
        try:
            with open(output_file, 'w', encoding='utf-8') as json_file:
                json.dump(all_videos_data, json_file, ensure_ascii=False, indent=4)
            print(f"💾 成功保存 {len(all_videos_data)} 個影片的評論數據到 {output_file}")
        except Exception as save_e:
            print(f"❌ 保存數據時出錯: {save_e}")
            return None
    else:
        print("⚠️ 沒有收集到任何評論數據")
    
    return output_file

def crawl_youtube_comments_with_pool(name, webdriver_pool, last_crawled_time=None, max_threads=3):
    """
    使用WebDriver池並行爬取YouTube評論 - 增强代理轮换功能

    Args:
        name: 立委姓名
        webdriver_pool: WebDriver池對象
        last_crawled_time: 上次爬取時間（增量爬取用）
        max_threads: 最大線程數

    Returns:
        dict: 爬取結果統計
    """
    # 🚨 新增：整體超時機制
    overall_start_time = time.time()
    max_overall_time = 1800 # 最多處理30分鐘，避免卡住太久
    
    print(f"🚀 開始使用WebDriver池爬取 {name} 的YouTube評論...")
    print(f"⏰ 整體超時設置: 最多{max_overall_time}秒（{max_overall_time//60}分鐘）")
    
    # 🚨 增强：检查代理池健康状态
    if PROXY_AVAILABLE:
        if not check_proxy_health():
            print("⚠️ 代理池不健康，尝试强制更新...")
            # 这里可以触发代理池更新
            try:
                from proxy_manager import proxy_manager
                proxy_manager.update_proxy_pool(force=True)
                print("✅ 代理池已强制更新")
            except Exception as e:
                print(f"❌ 代理池更新失败: {e}")

    # 第一步：從檔案中讀取影片URLs
    current_dir = os.path.dirname(os.path.abspath(__file__))
    href_file = os.path.join(current_dir, 'href', 'youtube', f'{name}.json')

    if not os.path.exists(href_file):
        print(f"❌ 找不到 {name} 的YouTube URL檔案: {href_file}")
        return {'success': False, 'error': 'URL檔案不存在', 'count': 0}

    try:
        with open(href_file, 'r', encoding='utf-8') as f:
            video_urls = json.load(f)
        print(f"✅ 成功讀取 {len(video_urls)} 個影片URL")
    except Exception as e:
        print(f"❌ 讀取URL檔案失敗: {e}")
        return {'success': False, 'error': f'讀取URL檔案失敗: {e}', 'count': 0}

    if not video_urls:
        print("⚠️ 沒有可處理的影片URL")
        return {'success': False, 'error': '沒有可處理的影片URL', 'count': 0}

    # 🚨 檢查是否已經超時
    if time.time() - overall_start_time > max_overall_time:
        print(f"⏰ 讀取URL階段就已超時，停止處理")
        return {'success': False, 'error': '讀取URL階段超時', 'count': 0}

    # 第二步：使用線程池並行爬取
    all_comments = []
    successful_videos = 0

    def crawl_single_video(video_info):
        """爬取單個影片的評論 - 增强代理轮换功能"""
        # 🚨 檢查單個影片處理超時
        video_start_time = time.time()
        max_video_time = 300  # 單個影片最多5分鐘，避免卡住
        
        # 🚨 檢查整體超時
        if time.time() - overall_start_time > max_overall_time:
            print(f"⏰ 整體超時，跳過影片處理")
            return []
        
        url = video_info.get('url') if isinstance(video_info, dict) else video_info
        print(f"🎬 開始處理影片: {url}")

        try:
            # 🚨 使用正確的上下文管理器獲取WebDriver，传递线程ID
            print(f"🔄 從WebDriver池獲取瀏覽器...")
            thread_id = threading.current_thread().ident % 1000  # 获取当前线程ID
            with webdriver_pool.get_driver(thread_id=thread_id) as driver:
                # 🚨 檢查影片處理超時
                if time.time() - video_start_time > max_video_time:
                    print(f"⏰ 單個影片處理超時({max_video_time}秒)，跳過")
                    return []

                # 導航到影片頁面
                driver.get(url)
                time.sleep(3)  # 減少等待時間：5→3秒

                # 🚨 再次檢查超時
                if time.time() - overall_start_time > max_overall_time:
                    print(f"⏰ 頁面加載後整體超時，停止處理")
                    return []

                # 嘗試暫停影片播放
                try:
                    video_element = driver.find_element(By.CSS_SELECTOR, 'video')
                    driver.execute_script("arguments[0].pause();", video_element)
                    print(f"✅ 已暫停影片播放")
                except Exception as e:
                    print(f"⚠️ 無法暫停影片: {e}")

                # 🚨 檢查超時後再滾動
                if time.time() - overall_start_time > max_overall_time:
                    print(f"⏰ 滾動前整體超時，停止處理")
                    return []

                # 滾動頁面以確保加載留言（加入超時保護）
                print(f"🔄 滾動頁面載入評論...")
                scroll_to_bottom(driver)  # 這個函數現在有超時保護

                # 🚨 檢查超時後再點擊按鈕
                if time.time() - overall_start_time > max_overall_time:
                    print(f"⏰ 點擊按鈕前整體超時，停止處理")
                    return []

                # 點擊"顯示更多"按鈕載入更多留言（加入超時保護）
                print(f"🔄 點擊顯示更多按鈕...")
                click_buttons(driver)  # 使用已存在的函數

                # 🚨 檢查超時後再爬取評論
                if time.time() - overall_start_time > max_overall_time:
                    print(f"⏰ 爬取評論前整體超時，停止處理")
                    return []

                # 爬取評論資料（加入超時保護）
                print(f"📝 爬取評論資料...")
                comments_data = []
                try:
                    comment_threads = driver.find_elements(By.XPATH, '//*[@id="contents"]/ytd-comment-thread-renderer')
                    print(f"📊 找到 {len(comment_threads)} 個評論線程")
                    
                    # 🚨 處理所有評論，不限制數量
                    max_comments_to_process = len(comment_threads)  # 處理所有評論
                    
                    for i, thread in enumerate(comment_threads[:max_comments_to_process]):
                        # 🚨 處理每個評論時也檢查超時
                        if time.time() - overall_start_time > max_overall_time:
                            print(f"⏰ 評論處理中整體超時，停止處理")
                            break
                            
                        if time.time() - video_start_time > max_video_time:
                            print(f"⏰ 單個影片評論處理超時，停止處理")
                            break
                        
                        try:
                            # 提取評論數據
                            comment_data = extract_comment_data(thread, driver)
                            if comment_data:
                                comments_data.append(comment_data)
                                
                        except Exception as e:
                            print(f"⚠️ 處理評論 {i+1} 時出錯: {e}")
                            continue
                            
                except Exception as e:
                    print(f"❌ 獲取評論時出錯: {e}")

                # 獲取影片資訊
                def safe_get_text(selectors, attribute=None, use_xpath=False):
                    """安全獲取元素文本，支援多個備用選擇器"""
                    if isinstance(selectors, str):
                        selectors = [selectors]
                    
                    for selector in selectors:
                        try:
                            if use_xpath:
                                element = driver.find_element(By.XPATH, selector)
                            else:
                                element = driver.find_element(By.CSS_SELECTOR, selector)
                            
                            if attribute:
                                return element.get_attribute(attribute)
                            else:
                                return element.text.strip()
                        except Exception:
                            continue
                    return ""
                
                # 獲取影片標題
                video_title = safe_get_text([
                    'h1.ytd-video-primary-info-renderer',
                    '#title.style-scope.ytd-watch-metadata',
                    'h1.ytd-video-primary-info-renderer-title'
                ])
                
                # 獲取影片發布時間
                video_time = safe_get_text([
                    'ytd-video-primary-info-renderer yt-formatted-string.ytd-video-primary-info-renderer',
                    '#date.style-scope.ytd-video-primary-info-renderer'
                ])
                
                # 獲取觀看次數
                view_count = safe_get_text([
                    'span.view-count.style-scope.ytd-video-view-count-renderer',
                    '#count.style-scope.ytd-video-view-count-renderer'
                ])
                
                # 組合影片資訊
                video_info = {
                    'title': video_title,
                    'time': video_time,
                    'views': view_count,
                    'url': url
                }
                
                # 為每個評論添加影片資訊
                for comment in comments_data:
                    comment.update(video_info)
                
                return comments_data
                
        except Exception as e:
            print(f"❌ 處理影片時出錯: {e}")
            return []

    # 🚨 批次處理改為更小的批次，加入超時檢查
    print(f"🔄 使用 {max_threads} 個線程並行爬取評論...")
    print(f"📊 總共需要處理 {len(video_urls)} 個影片")

    # 批次處理，避免一次性提交太多任務
    batch_size = max(2, max_threads)  # 🚨 減小批次大小，避免資源競爭
    total_batches = (len(video_urls) + batch_size - 1) // batch_size

    for batch_idx in range(total_batches):
        # 🚨 每個批次開始前檢查整體超時
        if time.time() - overall_start_time > max_overall_time:
            print(f"⏰ 批次處理前整體超時，停止所有處理")
            break
            
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(video_urls))
        batch_videos = video_urls[start_idx:end_idx]

        print(f"🔄 處理第 {batch_idx + 1}/{total_batches} 批，影片 {start_idx + 1}-{end_idx}")

        # 🚨 使用更小的超時設置
        batch_start_time = time.time()
        max_batch_time = 7200  # 每批最多30分鐘，給予充足處理時間
        
        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            future_to_video = {
                executor.submit(crawl_single_video, video_info): video_info
                for video_info in batch_videos
            }

            for future in as_completed(future_to_video):
                # 🚨 檢查批次超時
                if time.time() - batch_start_time > max_batch_time:
                    print(f"⏰ 批次處理超時({max_batch_time}秒)，跳過剩餘影片")
                    break
                    
                if time.time() - overall_start_time > max_overall_time:
                    print(f"⏰ 整體超時，強制停止所有處理")
                    break

                video_info = future_to_video[future]
                try:
                    video_data_list = future.result(timeout=300)  # 🚨 單個任務最多5分鐘
                    if video_data_list:
                        # 🔥 立即保存每個影片的數據，而不是等所有影片爬完
                        append_success = append_crawler_data("youtube", name, video_data_list)

                        if append_success:
                            all_comments.extend(video_data_list)
                            successful_videos += 1
                            # 計算實際評論數量
                            total_comments = sum(len(video.get('留言資料', [])) for video in video_data_list)
                            print(f"💾 已保存影片數據: {len(video_data_list)} 個影片，{total_comments} 條評論，累計: {len(all_comments)} 個影片")
                        else:
                            print(f"❌ 保存影片數據失敗")
                except Exception as e:
                    print(f"❌ 線程執行失敗: {e}")

        elapsed_time = time.time() - overall_start_time
        print(f"✅ 第 {batch_idx + 1} 批處理完成 (已用時{elapsed_time:.1f}秒)")
        
        # 🚨 批次間檢查是否需要停止
        if time.time() - overall_start_time > max_overall_time:
            print(f"⏰ 整體時間已到，停止處理")
            break

    # 第三步：返回最終結果
    total_elapsed = time.time() - overall_start_time
    print(f"✅ YouTube 爬取完成 (總耗時{total_elapsed:.1f}秒)")
    print(f"   處理影片數: {successful_videos}/{len(video_urls)}")
    print(f"   總評論數: {len(all_comments)}")

    # 返回統一格式
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_file = os.path.join(current_dir, 'data', 'youtube', f'{name}.json')

    if successful_videos > 0:
        return {
            'success': True,
            'data_file': data_file,
            'count': len(all_comments),
            'videos_processed': successful_videos,
            'total_videos': len(video_urls),
            'processing_time': total_elapsed
        }
    else:
        print("⚠️  沒有成功爬取任何影片")
        return {
            'success': False,
            'error': '沒有成功爬取任何影片',
            'count': 0,
            'processing_time': total_elapsed
        }

def extract_comment_data(thread, driver):
    """使用优化器智能提取评论数据"""
    try:
        # 使用优化器的智能提取功能
        comments = youtube_optimizer.extract_comments_smart(driver)
        
        if comments:
            # 转换为原有格式
            main_comment = comments[0] if comments else {}
            
            return {
                '主留言': {
                    '用戶名': main_comment.get('author', '匿名用户'),
                    '留言時間': main_comment.get('time', ''),
                    '留言內容': main_comment.get('content', ''),
                    '按讚數': main_comment.get('likes', '0')
                },
                '回覆': []  # 简化处理，只提取主评论
            }
        else:
            # 如果优化器提取失败，使用原有方法
            main_user = thread.find_element(By.XPATH, './/*[@id="header-author"]/h3').text.strip()
            main_time = thread.find_element(By.XPATH, './/*[@id="published-time-text"]').text.strip()
            main_comment = thread.find_element(By.XPATH, './/*[@id="content-text"]/span').text.strip()
            main_likes = thread.find_element(By.XPATH, './/*[@id="vote-count-middle"]').text.strip() or "0"
            
            return {
                '主留言': {
                    '用戶名': main_user,
                    '留言時間': main_time,
                    '留言內容': main_comment,
                    '按讚數': main_likes
                },
                '回覆': []
            }
            
    except Exception as e:
        print(f"⚠️ 智能提取评论失败，使用备用方法: {e}")
        
        # 备用方法
        try:
            main_user = thread.find_element(By.XPATH, './/*[@id="header-author"]/h3').text.strip()
            main_time = thread.find_element(By.XPATH, './/*[@id="published-time-text"]').text.strip()
            main_comment = thread.find_element(By.XPATH, './/*[@id="content-text"]/span').text.strip()
            main_likes = thread.find_element(By.XPATH, './/*[@id="vote-count-middle"]').text.strip() or "0"
            
            return {
                '主留言': {
                    '用戶名': main_user,
                    '留言時間': main_time,
                    '留言內容': main_comment,
                    '按讚數': main_likes
                },
                '回覆': []
            }
        except Exception as e2:
            print(f"❌ 备用提取方法也失败: {e2}")
            return None

def extract_replies_data(thread, driver, max_replies=10):
    """快速提取回覆評論數據，限制數量避免卡住"""
    replies_data = []
    try:
        # 嘗試展開回覆（快速失敗）
        try:
            reply_button = thread.find_element(By.CSS_SELECTOR, '#more-replies')
            reply_button.click()
            time.sleep(0.5)  # 減少等待時間
        except:
            pass
        
        reply_elements = thread.find_elements(By.CSS_SELECTOR, '#replies .style-scope.ytd-comment-replies-renderer')
        
        # 限制回覆數量
        max_process = min(max_replies, len(reply_elements))
        
        for i, element in enumerate(reply_elements[:max_process]):
            try:
                reply_user = element.find_element(By.XPATH, './/*[@id="header-author"]/h3').text.strip()
                reply_comment = element.find_element(By.XPATH, './/*[@id="content-text"]').text.strip()
                reply_time = element.find_element(By.XPATH, './/*[@id="published-time-text"]').text.strip()
                reply_likes = element.find_element(By.XPATH, './/*[@id="vote-count-middle"]').text.strip() or "0"
                
                replies_data.append({
                    "回覆留言者": reply_user,
                    "回覆留言時間": reply_time,
                    "回覆留言內容": reply_comment,
                    "回覆留言按讚數": reply_likes
                })
                
            except Exception as e:
                print(f"⚠️ 提取回覆 {i+1} 失敗: {e}")
                continue
                
    except Exception as e:
        print(f"⚠️ 提取回覆評論失敗: {e}")
    
    return replies_data

if __name__ == '__main__':
    crawl_youtube_comments('葉元之',False ,4, '2025-06-29')