#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復丁學忠資料格式並合併fb_data
1. 修復final_data中的日期和平台分離
2. 合併fb_data到final_data
"""

import json
import os
import re
from datetime import datetime
from collections import defaultdict

def load_json_file(file_path, encoding='utf-8'):
    """載入JSON檔案"""
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return json.load(f)
    except Exception as e:
        print(f"無法讀取檔案 {file_path}: {e}")
        return None

def save_json_file(data, file_path):
    """儲存JSON檔案"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"無法儲存檔案 {file_path}: {e}")
        return False

def extract_platform_info(content):
    """從整合留言內容中提取平台資訊"""
    platforms = []
    
    # 檢查是否包含平台標識
    if "平台：ptt" in content:
        platforms.append("ptt")
    if "平台：youtube" in content:
        platforms.append("youtube")
    if "平台：facebook" in content or "粉絲專頁" in content:
        platforms.append("facebook")
    
    return platforms

def extract_date_from_content(content):
    """從內容中提取日期"""
    # 嘗試從內容中提取日期
    date_patterns = [
        r'(\d{4}-\d{2}-\d{2})',
        r'(\d{4}/\d{2}/\d{2})',
        r'(\d{2}/\d{2}/\d{4})'
    ]
    
    for pattern in date_patterns:
        match = re.search(pattern, content)
        if match:
            date_str = match.group(1)
            # 標準化日期格式
            if '/' in date_str:
                parts = date_str.split('/')
                if len(parts[0]) == 4:  # YYYY/MM/DD
                    return f"{parts[0]}-{parts[1]}-{parts[2]}"
                else:  # MM/DD/YYYY
                    return f"{parts[2]}-{parts[0]}-{parts[1]}"
            return date_str
    
    # 如果沒有找到日期，使用今天
    return datetime.now().strftime('%Y-%m-%d')

def fix_ding_final_data():
    """修復丁學忠的final_data格式"""
    print("🔧 開始修復丁學忠的final_data格式...")
    
    final_data_file = "crawler/processed/final_data/丁學忠_使用者分析.json"
    
    # 載入資料
    data = load_json_file(final_data_file)
    if not data:
        print("❌ 無法載入丁學忠的final_data")
        return
    
    print(f"📊 載入 {len(data)} 筆資料")
    
    # 修復每筆資料
    fixed_count = 0
    for item in data:
        if not isinstance(item, dict):
            continue
        
        # 修復日期
        if not item.get('日期') or item['日期'] == "":
            content = item.get('整合留言內容', '')
            item['日期'] = extract_date_from_content(content)
            fixed_count += 1
        
        # 添加平台欄位
        if '平台' not in item:
            content = item.get('整合留言內容', '')
            platforms = extract_platform_info(content)
            if platforms:
                item['平台'] = ','.join(platforms)
            else:
                item['平台'] = 'unknown'
    
    print(f"✅ 修復了 {fixed_count} 筆資料的日期")
    
    # 儲存修復後的資料
    if save_json_file(data, final_data_file):
        print("✅ 成功儲存修復後的final_data")
    else:
        print("❌ 儲存失敗")

def merge_fb_data_to_ding():
    """合併fb_data到丁學忠的final_data"""
    print("🔄 開始合併fb_data到丁學忠的final_data...")
    
    final_data_file = "crawler/processed/final_data/丁學忠_使用者分析.json"
    fb_data_file = "crawler/fb_data/丁學忠_使用者分析.json"
    
    # 載入final_data
    final_data = load_json_file(final_data_file)
    if not final_data:
        print("❌ 無法載入final_data")
        return
    
    # 載入fb_data
    fb_data = load_json_file(fb_data_file)
    if not fb_data:
        print("❌ 無法載入fb_data")
        return
    
    print(f"📊 final_data: {len(final_data)} 筆")
    print(f"📊 fb_data: {len(fb_data)} 筆")
    
    # 建立final_data用戶名稱索引
    final_users = {item['使用者']: item for item in final_data if isinstance(item, dict)}
    
    # 處理fb_data
    merged_count = 0
    new_users = 0
    
    for fb_item in fb_data:
        if not isinstance(fb_item, dict):
            continue
        
        fb_user = fb_item.get('使用者', '')
        
        if fb_user in final_users:
            # 更新現有用戶的資料
            final_item = final_users[fb_user]
            
            # 更新平台資訊
            if '平台' in final_item:
                platforms = final_item['平台'].split(',')
                if 'facebook' not in platforms:
                    platforms.append('facebook')
                final_item['平台'] = ','.join(platforms)
            else:
                final_item['平台'] = 'facebook'
            
            # 更新日期（如果fb_data的日期更完整）
            if fb_item.get('日期') and (not final_item.get('日期') or final_item['日期'] == ''):
                final_item['日期'] = fb_item['日期']
            
            merged_count += 1
        else:
            # 添加新用戶
            final_data.append(fb_item)
            new_users += 1
    
    print(f"✅ 更新了 {merged_count} 個現有用戶")
    print(f"✅ 新增了 {new_users} 個新用戶")
    print(f"📊 合併後總計: {len(final_data)} 筆")
    
    # 儲存合併後的資料
    if save_json_file(final_data, final_data_file):
        print("✅ 成功儲存合併後的資料")
    else:
        print("❌ 儲存失敗")

def main():
    """主函數"""
    print("=== 丁學忠資料修復與合併工具 ===")
    
    # 1. 修復final_data格式
    fix_ding_final_data()
    
    print("\n" + "="*50 + "\n")
    
    # 2. 合併fb_data
    merge_fb_data_to_ding()
    
    print("\n=== 完成 ===")

if __name__ == "__main__":
    main() 