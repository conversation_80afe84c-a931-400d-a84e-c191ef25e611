#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移脚本：从本地MongoDB迁移到Atlas
只迁移 legislators collection
"""

import os
import sys
import json
import logging
from datetime import datetime
from pymongo import MongoClient
from bson import ObjectId
import traceback

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

class DateTimeEncoder(json.JSONEncoder):
    """处理MongoDB数据的JSON序列化"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, ObjectId):
            return str(obj)
        return super().default(obj)

class MongoAtlasMigrator:
    """MongoDB Atlas 迁移工具"""
    
    def __init__(self):
        # 本地MongoDB连接
        self.local_uri = "mongodb://localhost:27017/"
        self.local_db_name = "legislator_recall"
        
        # Atlas连接
        self.atlas_uri = os.getenv('MONGODB_ATLAS_URI')
        self.atlas_db_name = os.getenv('MONGODB_ATLAS_DBNAME', 'legislator_recall')
        
        if not self.atlas_uri:
            raise ValueError("请先设置 MONGODB_ATLAS_URI 环境变量")
        
        self.local_client = None
        self.atlas_client = None
        
    def connect(self):
        """建立数据库连接"""
        try:
            # 连接本地MongoDB
            self.local_client = MongoClient(self.local_uri)
            self.local_db = self.local_client[self.local_db_name]
            
            # 测试本地连接
            self.local_client.admin.command('ping')
            logger.info(f"✅ 本地MongoDB连接成功: {self.local_uri}")
            
            # 连接Atlas
            self.atlas_client = MongoClient(self.atlas_uri)
            self.atlas_db = self.atlas_client[self.atlas_db_name]
            
            # 测试Atlas连接
            self.atlas_client.admin.command('ping')
            logger.info(f"✅ MongoDB Atlas连接成功")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def backup_local_data(self, backup_dir="backup"):
        """备份本地数据"""
        try:
            os.makedirs(backup_dir, exist_ok=True)
            
            # 获取legislators collection数据
            legislators_col = self.local_db['legislators']
            legislators_data = list(legislators_col.find())
            
            # 保存备份
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(backup_dir, f"legislators_backup_{timestamp}.json")
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(legislators_data, f, cls=DateTimeEncoder, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 本地数据已备份到: {backup_file}")
            logger.info(f"📊 备份了 {len(legislators_data)} 条立委记录")
            
            return backup_file, len(legislators_data)
            
        except Exception as e:
            logger.error(f"❌ 备份失败: {e}")
            return None, 0
    
    def migrate_legislators_collection(self):
        """迁移legislators collection"""
        try:
            # 获取本地数据
            local_legislators = self.local_db['legislators']
            local_data = list(local_legislators.find())
            
            if not local_data:
                logger.warning("⚠️ 本地没有找到legislators数据")
                return False
            
            logger.info(f"📋 准备迁移 {len(local_data)} 条立委记录")
            
            # Atlas目标collection
            atlas_legislators = self.atlas_db['legislators']
            
            # 清空Atlas中的现有数据（如果存在）
            existing_count = atlas_legislators.count_documents({})
            if existing_count > 0:
                logger.warning(f"⚠️ Atlas中已存在 {existing_count} 条记录")
                confirm = input("是否要清空Atlas中的现有数据？(y/N): ")
                if confirm.lower() == 'y':
                    result = atlas_legislators.delete_many({})
                    logger.info(f"🗑️ 已删除Atlas中的 {result.deleted_count} 条记录")
                else:
                    logger.info("📝 将进行增量更新")
            
            # 批量插入数据
            successful_count = 0
            failed_count = 0
            
            for i, record in enumerate(local_data, 1):
                try:
                    # 检查是否已存在
                    existing = atlas_legislators.find_one({'name': record.get('name')})
                    
                    if existing:
                        # 更新现有记录
                        atlas_legislators.replace_one({'_id': existing['_id']}, record)
                        logger.info(f"🔄 更新: {record.get('name', 'Unknown')} ({i}/{len(local_data)})")
                    else:
                        # 插入新记录
                        # 移除本地的_id，让Atlas生成新的
                        if '_id' in record:
                            del record['_id']
                        atlas_legislators.insert_one(record)
                        logger.info(f"✅ 插入: {record.get('name', 'Unknown')} ({i}/{len(local_data)})")
                    
                    successful_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ 处理记录失败: {record.get('name', 'Unknown')} - {e}")
                    failed_count += 1
            
            logger.info(f"🎉 迁移完成!")
            logger.info(f"  ✅ 成功: {successful_count} 条")
            logger.info(f"  ❌ 失败: {failed_count} 条")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 迁移失败: {e}")
            traceback.print_exc()
            return False
    
    def verify_migration(self):
        """验证迁移结果"""
        try:
            # 统计本地数据
            local_count = self.local_db['legislators'].count_documents({})
            
            # 统计Atlas数据
            atlas_count = self.atlas_db['legislators'].count_documents({})
            
            logger.info(f"📊 数据验证:")
            logger.info(f"  本地MongoDB: {local_count} 条记录")
            logger.info(f"  MongoDB Atlas: {atlas_count} 条记录")
            
            # 检查关键字段
            atlas_sample = self.atlas_db['legislators'].find_one()
            if atlas_sample:
                logger.info(f"✅ Atlas数据示例检查:")
                logger.info(f"  立委姓名: {atlas_sample.get('name', 'N/A')}")
                logger.info(f"  情感分析: {'存在' if '情感分析' in atlas_sample else '缺失'}")
                logger.info(f"  文字云: {'存在' if 'word_cloud' in atlas_sample else '缺失'}")
                logger.info(f"  用户数: {atlas_sample.get('用戶數', 'N/A')}")
                logger.info(f"  留言数: {atlas_sample.get('留言數', 'N/A')}")
            
            if atlas_count == local_count:
                logger.info("✅ 迁移验证通过：数据数量一致")
                return True
            else:
                logger.warning(f"⚠️ 数据数量不一致，请检查")
                return False
                
        except Exception as e:
            logger.error(f"❌ 验证失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.local_client:
            self.local_client.close()
        if self.atlas_client:
            self.atlas_client.close()
        logger.info("📞 数据库连接已关闭")

def main():
    """主函数"""
    print("🚀 MongoDB Atlas 数据迁移工具")
    print("=" * 50)
    
    migrator = MongoAtlasMigrator()
    
    try:
        # 1. 建立连接
        if not migrator.connect():
            return 1
        
        # 2. 备份本地数据
        print("\n📦 步骤1: 备份本地数据...")
        backup_file, record_count = migrator.backup_local_data()
        if not backup_file:
            logger.error("备份失败，终止迁移")
            return 1
        
        # 3. 执行迁移
        print("\n🔄 步骤2: 执行数据迁移...")
        if not migrator.migrate_legislators_collection():
            logger.error("迁移失败")
            return 1
        
        # 4. 验证迁移
        print("\n🔍 步骤3: 验证迁移结果...")
        if migrator.verify_migration():
            print("\n🎉 迁移成功完成!")
            print("现在可以测试前后端连接Atlas的功能了")
        else:
            print("\n⚠️ 迁移可能存在问题，请检查日志")
            
    except KeyboardInterrupt:
        logger.info("用户中断了迁移过程")
        return 1
    except Exception as e:
        logger.error(f"迁移过程中发生错误: {e}")
        traceback.print_exc()
        return 1
    finally:
        migrator.close()
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main()) 