{"version": 3, "sources": ["../../../../../../node_modules/@coreui/chartjs/dist/js/coreui-chartjs.esm.js", "../../../../../../node_modules/@coreui/angular-chartjs/fesm2022/coreui-angular-chartjs.mjs"], "sourcesContent": ["/*!\n  * CoreUI v4.0.0 (https://coreui.io)\n  * Copyright 2024 [object Object]\n  * Licensed under MIT (https://github.com/coreui/coreui-chartjs/blob/main/LICENSE)\n  */\n/**\n * --------------------------------------------------------------------------\n * Custom Tooltips for Chart.js (v4.0.0): custom-tooltips.js\n * Licensed under MIT (https://github.com/coreui/coreui-chartjs/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst ClassName = {\n  TOOLTIP: 'chartjs-tooltip',\n  TOOLTIP_BODY: 'chartjs-tooltip-body',\n  TOOLTIP_BODY_ITEM: 'chartjs-tooltip-body-item',\n  TOOLTIP_HEADER: 'chartjs-tooltip-header',\n  TOOLTIP_HEADER_ITEM: 'chartjs-tooltip-header-item'\n};\nconst getOrCreateTooltip = chart => {\n  let tooltipEl = chart.canvas.parentNode.querySelector('div');\n  if (!tooltipEl) {\n    tooltipEl = document.createElement('div');\n    tooltipEl.classList.add(ClassName.TOOLTIP);\n    const table = document.createElement('table');\n    table.style.margin = '0px';\n    tooltipEl.append(table);\n    chart.canvas.parentNode.append(tooltipEl);\n  }\n  return tooltipEl;\n};\nconst customTooltips = context => {\n  // Tooltip Element\n  const {\n    chart,\n    tooltip\n  } = context;\n  const tooltipEl = getOrCreateTooltip(chart);\n\n  // Hide if no tooltip\n  if (tooltip.opacity === 0) {\n    tooltipEl.style.opacity = 0;\n    return;\n  }\n\n  // Set Text\n  if (tooltip.body) {\n    const titleLines = tooltip.title || [];\n    const bodyLines = tooltip.body.map(b => b.lines);\n    const tableHead = document.createElement('thead');\n    tableHead.classList.add(ClassName.TOOLTIP_HEADER);\n    for (const title of titleLines) {\n      const tr = document.createElement('tr');\n      tr.style.borderWidth = 0;\n      tr.classList.add(ClassName.TOOLTIP_HEADER_ITEM);\n      const th = document.createElement('th');\n      th.style.borderWidth = 0;\n      const text = document.createTextNode(title);\n      th.append(text);\n      tr.append(th);\n      tableHead.append(tr);\n    }\n    const tableBody = document.createElement('tbody');\n    tableBody.classList.add(ClassName.TOOLTIP_BODY);\n    for (const [i, body] of bodyLines.entries()) {\n      const colors = tooltip.labelColors[i];\n      const span = document.createElement('span');\n      span.style.background = colors.backgroundColor;\n      span.style.borderColor = colors.borderColor;\n      span.style.borderWidth = '2px';\n      span.style.marginRight = '10px';\n      span.style.height = '10px';\n      span.style.width = '10px';\n      span.style.display = 'inline-block';\n      const tr = document.createElement('tr');\n      tr.classList.add(ClassName.TOOLTIP_BODY_ITEM);\n      const td = document.createElement('td');\n      td.style.borderWidth = 0;\n      const text = document.createTextNode(body);\n      td.append(span);\n      td.append(text);\n      tr.append(td);\n      tableBody.append(tr);\n    }\n    const tableRoot = tooltipEl.querySelector('table');\n\n    // Remove old children\n    while (tableRoot.firstChild) {\n      tableRoot.firstChild.remove();\n    }\n\n    // Add new children\n    tableRoot.append(tableHead);\n    tableRoot.append(tableBody);\n  }\n  const {\n    offsetLeft: positionX,\n    offsetTop: positionY\n  } = chart.canvas;\n\n  // Display, position, and set styles for font\n  tooltipEl.style.opacity = 1;\n  tooltipEl.style.left = `${positionX + tooltip.caretX}px`;\n  tooltipEl.style.top = `${positionY + tooltip.caretY}px`;\n  tooltipEl.style.font = tooltip.options.bodyFont.string;\n  tooltipEl.style.padding = `${tooltip.padding}px ${tooltip.padding}px`;\n};\nexport { customTooltips };\n", "import * as i0 from '@angular/core';\nimport { inject, <PERSON><PERSON><PERSON>, Renderer2, ChangeDetectorRef, input, booleanAttribute, numberAttribute, linkedSignal, output, viewChild, computed, afterRenderEffect, untracked, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport merge from 'lodash-es/merge';\nimport { Chart, registerables } from 'chart.js';\nimport { customTooltips } from '@coreui/chartjs';\nconst _c0 = [\"canvasElement\"];\nconst _c1 = [\"*\"];\nChart.register(...registerables);\nlet nextId = 0;\nclass ChartjsComponent {\n  get id() {\n    return this.idInput();\n  }\n  constructor() {\n    this.ngZone = inject(NgZone);\n    this.renderer = inject(Renderer2);\n    this.changeDetectorRef = inject(ChangeDetectorRef);\n    /**\n     * Enables custom html based tooltips instead of standard tooltips.\n     * @return boolean\n     * @default true\n     */\n    this.customTooltips = input(true, {\n      transform: booleanAttribute\n    });\n    /**\n     * The data object that is passed into the Chart.js chart (more info).\n     */\n    this.data = input();\n    /**\n     * A fallback when the canvas cannot be rendered. Can be used for accessible chart descriptions.\n     */\n    // @Input() fallbackContent?: TemplateRef<any>;\n    /**\n     * Height attribute applied to the rendered canvas.\n     * @return number | undefined\n     * @default null\n     */\n    this.height = input(null, {\n      transform: value => numberAttribute(value, undefined)\n    });\n    /**\n     * ID attribute applied to the rendered canvas.\n     * @return string\n     */\n    this.idInput = input(`c-chartjs-${nextId++}`, {\n      alias: 'id'\n    });\n    /**\n     * The options object that is passed into the Chart.js chart.\n     */\n    this.optionsInput = input({}, {\n      alias: 'options'\n    });\n    this.options = linkedSignal(this.optionsInput);\n    /**\n     * The plugins array that is passed into the Chart.js chart\n     */\n    this.plugins = input([]);\n    /**\n     * If true, will tear down and redraw chart on all updates.\n     * @return boolean\n     * @default false\n     */\n    this.redraw = input(false, {\n      transform: booleanAttribute\n    });\n    /**\n     * Chart.js chart type.\n     * @return {'line' | 'bar' | 'radar' | 'doughnut' | 'polarArea' | 'bubble' | 'pie' | 'scatter'}\n     */\n    this.type = input('bar');\n    /**\n     * Width attribute applied to the rendered canvas.\n     * @return number | undefined\n     * @default null\n     */\n    this.width = input(null, {\n      transform: value => numberAttribute(value, undefined)\n    });\n    /**\n     * Put the chart into the wrapper div element.\n     * @default true\n     */\n    this.wrapper = input(true, {\n      transform: booleanAttribute\n    });\n    this.getDatasetAtEvent = output();\n    this.getElementAtEvent = output();\n    this.getElementsAtEvent = output();\n    this.chartRef = output();\n    this.canvasElement = viewChild.required('canvasElement');\n    this.hostClasses = computed(() => {\n      return {\n        'chart-wrapper': this.wrapper()\n      };\n    });\n    this.chartDataConfig = computed(() => {\n      const {\n        labels,\n        datasets\n      } = {\n        ...this.data()\n      };\n      return {\n        labels: labels ?? [],\n        datasets: datasets ?? []\n      };\n    });\n    this.chartOptions = computed(() => this.options() ?? {});\n    this.chartConfig = computed(() => {\n      this.chartCustomTooltips();\n      return {\n        data: this.chartDataConfig(),\n        options: this.chartOptions(),\n        plugins: this.plugins(),\n        type: this.type()\n      };\n    });\n    afterRenderEffect({\n      read: () => {\n        const canvasElement = this.canvasElement();\n        this.ctx = canvasElement?.nativeElement?.getContext('2d');\n        this.chartRender();\n      }\n    });\n  }\n  ngOnChanges(changes) {\n    if (changes['data'] && !changes['data'].firstChange) {\n      this.chartUpdate();\n    }\n  }\n  ngOnDestroy() {\n    this.chartDestroy();\n  }\n  handleClick($event) {\n    if (!this.chart) {\n      return;\n    }\n    const datasetAtEvent = this.chart.getElementsAtEventForMode($event, 'dataset', {\n      intersect: true\n    }, false);\n    this.getDatasetAtEvent.emit(datasetAtEvent);\n    const elementAtEvent = this.chart.getElementsAtEventForMode($event, 'nearest', {\n      intersect: true\n    }, false);\n    this.getElementAtEvent.emit(elementAtEvent);\n    const elementsAtEvent = this.chart.getElementsAtEventForMode($event, 'index', {\n      intersect: true\n    }, false);\n    this.getElementsAtEvent.emit(elementsAtEvent);\n  }\n  chartDestroy() {\n    this.chart?.destroy();\n    this.chartRef.emit(undefined);\n  }\n  chartRender() {\n    const canvasElement = this.canvasElement();\n    if (!canvasElement?.nativeElement || !this.ctx || this.chart) {\n      return;\n    }\n    this.ngZone.runOutsideAngular(() => {\n      const config = this.chartConfig();\n      if (config) {\n        this.chart = new Chart(this.ctx, config);\n        this.ngZone.run(() => {\n          this.renderer.setStyle(canvasElement.nativeElement, 'display', 'block');\n          this.changeDetectorRef.markForCheck();\n          this.chartRef.emit(this.chart);\n        });\n      }\n    });\n  }\n  chartUpdate() {\n    if (!this.chart) {\n      return;\n    }\n    if (this.redraw()) {\n      this.chartDestroy();\n      this.chartRender();\n      return;\n    }\n    const config = this.chartConfig();\n    if (this.options()) {\n      Object.assign(this.chart.options ?? {}, config.options ?? {});\n    }\n    if (!this.chart.config.data) {\n      this.chart.config.data = {\n        ...config.data\n      };\n      this.chartUpdateOutsideAngular();\n    }\n    if (this.chart) {\n      Object.assign(this.chart.config.options ?? {}, config.options ?? {});\n      Object.assign(this.chart.config.plugins ?? [], config.plugins ?? []);\n      Object.assign(this.chart.config.data, config.data);\n    }\n    this.chartUpdateOutsideAngular();\n  }\n  chartUpdateOutsideAngular() {\n    setTimeout(() => {\n      this.ngZone.runOutsideAngular(() => {\n        this.chart?.update();\n        this.ngZone.run(() => {\n          this.changeDetectorRef.markForCheck();\n        });\n      });\n    });\n  }\n  chartToBase64Image() {\n    return this.chart?.toBase64Image();\n  }\n  chartCustomTooltips() {\n    if (this.customTooltips()) {\n      const options = this.options();\n      const plugins = options?.plugins;\n      const tooltip = options?.plugins?.tooltip;\n      untracked(() => {\n        this.options.set(merge({\n          ...options,\n          plugins: {\n            ...plugins,\n            tooltip: {\n              ...tooltip,\n              enabled: false,\n              mode: 'index',\n              position: 'nearest',\n              external: customTooltips\n            }\n          }\n        }));\n      });\n    }\n  }\n  static {\n    this.ɵfac = function ChartjsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ChartjsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ChartjsComponent,\n      selectors: [[\"c-chart\"]],\n      viewQuery: function ChartjsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuerySignal(ctx.canvasElement, _c0, 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵqueryAdvance();\n        }\n      },\n      hostVars: 6,\n      hostBindings: function ChartjsComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.hostClasses());\n          i0.ɵɵstyleProp(\"height\", ctx.height(), \"px\")(\"width\", ctx.width(), \"px\");\n        }\n      },\n      inputs: {\n        customTooltips: [1, \"customTooltips\"],\n        data: [1, \"data\"],\n        height: [1, \"height\"],\n        idInput: [1, \"id\", \"idInput\"],\n        optionsInput: [1, \"options\", \"optionsInput\"],\n        plugins: [1, \"plugins\"],\n        redraw: [1, \"redraw\"],\n        type: [1, \"type\"],\n        width: [1, \"width\"],\n        wrapper: [1, \"wrapper\"]\n      },\n      outputs: {\n        getDatasetAtEvent: \"getDatasetAtEvent\",\n        getElementAtEvent: \"getElementAtEvent\",\n        getElementsAtEvent: \"getElementsAtEvent\",\n        chartRef: \"chartRef\"\n      },\n      exportAs: [\"cChart\"],\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c1,\n      decls: 3,\n      vars: 3,\n      consts: [[\"canvasElement\", \"\"], [\"role\", \"img\", 2, \"display\", \"none\", 3, \"click\", \"height\", \"id\", \"width\"]],\n      template: function ChartjsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"canvas\", 1, 0);\n          i0.ɵɵlistener(\"click\", function ChartjsComponent_Template_canvas_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleClick($event));\n          });\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"height\", ctx.height() || null)(\"id\", ctx.id)(\"width\", ctx.width() || null);\n        }\n      },\n      styles: [\".chart-wrapper[_nghost-%COMP%]{display:block}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ChartjsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'c-chart',\n      exportAs: 'cChart',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class]': 'hostClasses()',\n        '[style.height.px]': 'height()',\n        '[style.width.px]': 'width()'\n      },\n      template: \"<canvas\\n  #canvasElement\\n  (click)=\\\"handleClick($event)\\\"\\n  [height]=\\\"height() || null\\\"\\n  [id]=\\\"id\\\"\\n  [width]=\\\"width() || null\\\"\\n  role=\\\"img\\\"\\n  style=\\\"display: none;\\\"\\n>\\n  <ng-content />\\n  <!--  <ng-container *ngTemplateOutlet=\\\"fallbackContent\\\"/>-->\\n</canvas>\\n\",\n      styles: [\":host.chart-wrapper{display:block}\\n\"]\n    }]\n  }], () => [], null);\n})();\nclass ChartjsModule {\n  static {\n    this.ɵfac = function ChartjsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ChartjsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ChartjsModule,\n      imports: [ChartjsComponent],\n      exports: [ChartjsComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ChartjsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ChartjsComponent],\n      exports: [ChartjsComponent]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of coreui-angular-chartjs\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ChartjsComponent, ChartjsModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,YAAY;AAAA,EAChB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,qBAAqB;AACvB;AACA,IAAM,qBAAqB,WAAS;AAClC,MAAI,YAAY,MAAM,OAAO,WAAW,cAAc,KAAK;AAC3D,MAAI,CAAC,WAAW;AACd,gBAAY,SAAS,cAAc,KAAK;AACxC,cAAU,UAAU,IAAI,UAAU,OAAO;AACzC,UAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,UAAM,MAAM,SAAS;AACrB,cAAU,OAAO,KAAK;AACtB,UAAM,OAAO,WAAW,OAAO,SAAS;AAAA,EAC1C;AACA,SAAO;AACT;AACA,IAAM,iBAAiB,aAAW;AAEhC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,mBAAmB,KAAK;AAG1C,MAAI,QAAQ,YAAY,GAAG;AACzB,cAAU,MAAM,UAAU;AAC1B;AAAA,EACF;AAGA,MAAI,QAAQ,MAAM;AAChB,UAAM,aAAa,QAAQ,SAAS,CAAC;AACrC,UAAM,YAAY,QAAQ,KAAK,IAAI,OAAK,EAAE,KAAK;AAC/C,UAAM,YAAY,SAAS,cAAc,OAAO;AAChD,cAAU,UAAU,IAAI,UAAU,cAAc;AAChD,eAAW,SAAS,YAAY;AAC9B,YAAM,KAAK,SAAS,cAAc,IAAI;AACtC,SAAG,MAAM,cAAc;AACvB,SAAG,UAAU,IAAI,UAAU,mBAAmB;AAC9C,YAAM,KAAK,SAAS,cAAc,IAAI;AACtC,SAAG,MAAM,cAAc;AACvB,YAAM,OAAO,SAAS,eAAe,KAAK;AAC1C,SAAG,OAAO,IAAI;AACd,SAAG,OAAO,EAAE;AACZ,gBAAU,OAAO,EAAE;AAAA,IACrB;AACA,UAAM,YAAY,SAAS,cAAc,OAAO;AAChD,cAAU,UAAU,IAAI,UAAU,YAAY;AAC9C,eAAW,CAAC,GAAG,IAAI,KAAK,UAAU,QAAQ,GAAG;AAC3C,YAAM,SAAS,QAAQ,YAAY,CAAC;AACpC,YAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,WAAK,MAAM,aAAa,OAAO;AAC/B,WAAK,MAAM,cAAc,OAAO;AAChC,WAAK,MAAM,cAAc;AACzB,WAAK,MAAM,cAAc;AACzB,WAAK,MAAM,SAAS;AACpB,WAAK,MAAM,QAAQ;AACnB,WAAK,MAAM,UAAU;AACrB,YAAM,KAAK,SAAS,cAAc,IAAI;AACtC,SAAG,UAAU,IAAI,UAAU,iBAAiB;AAC5C,YAAM,KAAK,SAAS,cAAc,IAAI;AACtC,SAAG,MAAM,cAAc;AACvB,YAAM,OAAO,SAAS,eAAe,IAAI;AACzC,SAAG,OAAO,IAAI;AACd,SAAG,OAAO,IAAI;AACd,SAAG,OAAO,EAAE;AACZ,gBAAU,OAAO,EAAE;AAAA,IACrB;AACA,UAAM,YAAY,UAAU,cAAc,OAAO;AAGjD,WAAO,UAAU,YAAY;AAC3B,gBAAU,WAAW,OAAO;AAAA,IAC9B;AAGA,cAAU,OAAO,SAAS;AAC1B,cAAU,OAAO,SAAS;AAAA,EAC5B;AACA,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,EACb,IAAI,MAAM;AAGV,YAAU,MAAM,UAAU;AAC1B,YAAU,MAAM,OAAO,GAAG,YAAY,QAAQ,MAAM;AACpD,YAAU,MAAM,MAAM,GAAG,YAAY,QAAQ,MAAM;AACnD,YAAU,MAAM,OAAO,QAAQ,QAAQ,SAAS;AAChD,YAAU,MAAM,UAAU,GAAG,QAAQ,OAAO,MAAM,QAAQ,OAAO;AACnE;;;ACrGA,IAAM,MAAM,CAAC,eAAe;AAC5B,IAAM,MAAM,CAAC,GAAG;AAChB,MAAM,SAAS,GAAG,aAAa;AAC/B,IAAI,SAAS;AACb,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,IAAI,KAAK;AACP,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,OAAO,MAAM;AAC3B,SAAK,WAAW,OAAO,SAAS;AAChC,SAAK,oBAAoB,OAAO,iBAAiB;AAMjD,SAAK,iBAAiB,MAAM,MAAM;AAAA,MAChC,WAAW;AAAA,IACb,CAAC;AAID,SAAK,OAAO,MAAM;AAUlB,SAAK,SAAS,MAAM,MAAM;AAAA,MACxB,WAAW,WAAS,gBAAgB,OAAO,MAAS;AAAA,IACtD,CAAC;AAKD,SAAK,UAAU,MAAM,aAAa,QAAQ,IAAI;AAAA,MAC5C,OAAO;AAAA,IACT,CAAC;AAID,SAAK,eAAe,MAAM,CAAC,GAAG;AAAA,MAC5B,OAAO;AAAA,IACT,CAAC;AACD,SAAK,UAAU,aAAa,KAAK,YAAY;AAI7C,SAAK,UAAU,MAAM,CAAC,CAAC;AAMvB,SAAK,SAAS,MAAM,OAAO;AAAA,MACzB,WAAW;AAAA,IACb,CAAC;AAKD,SAAK,OAAO,MAAM,KAAK;AAMvB,SAAK,QAAQ,MAAM,MAAM;AAAA,MACvB,WAAW,WAAS,gBAAgB,OAAO,MAAS;AAAA,IACtD,CAAC;AAKD,SAAK,UAAU,MAAM,MAAM;AAAA,MACzB,WAAW;AAAA,IACb,CAAC;AACD,SAAK,oBAAoB,OAAO;AAChC,SAAK,oBAAoB,OAAO;AAChC,SAAK,qBAAqB,OAAO;AACjC,SAAK,WAAW,OAAO;AACvB,SAAK,gBAAgB,UAAU,SAAS,eAAe;AACvD,SAAK,cAAc,SAAS,MAAM;AAChC,aAAO;AAAA,QACL,iBAAiB,KAAK,QAAQ;AAAA,MAChC;AAAA,IACF,CAAC;AACD,SAAK,kBAAkB,SAAS,MAAM;AACpC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,mBACC,KAAK,KAAK;AAEf,aAAO;AAAA,QACL,QAAQ,UAAU,CAAC;AAAA,QACnB,UAAU,YAAY,CAAC;AAAA,MACzB;AAAA,IACF,CAAC;AACD,SAAK,eAAe,SAAS,MAAM,KAAK,QAAQ,KAAK,CAAC,CAAC;AACvD,SAAK,cAAc,SAAS,MAAM;AAChC,WAAK,oBAAoB;AACzB,aAAO;AAAA,QACL,MAAM,KAAK,gBAAgB;AAAA,QAC3B,SAAS,KAAK,aAAa;AAAA,QAC3B,SAAS,KAAK,QAAQ;AAAA,QACtB,MAAM,KAAK,KAAK;AAAA,MAClB;AAAA,IACF,CAAC;AACD,sBAAkB;AAAA,MAChB,MAAM,MAAM;AACV,cAAM,gBAAgB,KAAK,cAAc;AACzC,aAAK,MAAM,eAAe,eAAe,WAAW,IAAI;AACxD,aAAK,YAAY;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,aAAa;AACnD,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,YAAY,QAAQ;AAClB,QAAI,CAAC,KAAK,OAAO;AACf;AAAA,IACF;AACA,UAAM,iBAAiB,KAAK,MAAM,0BAA0B,QAAQ,WAAW;AAAA,MAC7E,WAAW;AAAA,IACb,GAAG,KAAK;AACR,SAAK,kBAAkB,KAAK,cAAc;AAC1C,UAAM,iBAAiB,KAAK,MAAM,0BAA0B,QAAQ,WAAW;AAAA,MAC7E,WAAW;AAAA,IACb,GAAG,KAAK;AACR,SAAK,kBAAkB,KAAK,cAAc;AAC1C,UAAM,kBAAkB,KAAK,MAAM,0BAA0B,QAAQ,SAAS;AAAA,MAC5E,WAAW;AAAA,IACb,GAAG,KAAK;AACR,SAAK,mBAAmB,KAAK,eAAe;AAAA,EAC9C;AAAA,EACA,eAAe;AACb,SAAK,OAAO,QAAQ;AACpB,SAAK,SAAS,KAAK,MAAS;AAAA,EAC9B;AAAA,EACA,cAAc;AACZ,UAAM,gBAAgB,KAAK,cAAc;AACzC,QAAI,CAAC,eAAe,iBAAiB,CAAC,KAAK,OAAO,KAAK,OAAO;AAC5D;AAAA,IACF;AACA,SAAK,OAAO,kBAAkB,MAAM;AAClC,YAAM,SAAS,KAAK,YAAY;AAChC,UAAI,QAAQ;AACV,aAAK,QAAQ,IAAI,MAAM,KAAK,KAAK,MAAM;AACvC,aAAK,OAAO,IAAI,MAAM;AACpB,eAAK,SAAS,SAAS,cAAc,eAAe,WAAW,OAAO;AACtE,eAAK,kBAAkB,aAAa;AACpC,eAAK,SAAS,KAAK,KAAK,KAAK;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,OAAO;AACf;AAAA,IACF;AACA,QAAI,KAAK,OAAO,GAAG;AACjB,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB;AAAA,IACF;AACA,UAAM,SAAS,KAAK,YAAY;AAChC,QAAI,KAAK,QAAQ,GAAG;AAClB,aAAO,OAAO,KAAK,MAAM,WAAW,CAAC,GAAG,OAAO,WAAW,CAAC,CAAC;AAAA,IAC9D;AACA,QAAI,CAAC,KAAK,MAAM,OAAO,MAAM;AAC3B,WAAK,MAAM,OAAO,OAAO,mBACpB,OAAO;AAEZ,WAAK,0BAA0B;AAAA,IACjC;AACA,QAAI,KAAK,OAAO;AACd,aAAO,OAAO,KAAK,MAAM,OAAO,WAAW,CAAC,GAAG,OAAO,WAAW,CAAC,CAAC;AACnE,aAAO,OAAO,KAAK,MAAM,OAAO,WAAW,CAAC,GAAG,OAAO,WAAW,CAAC,CAAC;AACnE,aAAO,OAAO,KAAK,MAAM,OAAO,MAAM,OAAO,IAAI;AAAA,IACnD;AACA,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,4BAA4B;AAC1B,eAAW,MAAM;AACf,WAAK,OAAO,kBAAkB,MAAM;AAClC,aAAK,OAAO,OAAO;AACnB,aAAK,OAAO,IAAI,MAAM;AACpB,eAAK,kBAAkB,aAAa;AAAA,QACtC,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,OAAO,cAAc;AAAA,EACnC;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,eAAe,GAAG;AACzB,YAAM,UAAU,KAAK,QAAQ;AAC7B,YAAM,UAAU,SAAS;AACzB,YAAM,UAAU,SAAS,SAAS;AAClC,gBAAU,MAAM;AACd,aAAK,QAAQ,IAAI,cAAM,iCAClB,UADkB;AAAA,UAErB,SAAS,iCACJ,UADI;AAAA,YAEP,SAAS,iCACJ,UADI;AAAA,cAEP,SAAS;AAAA,cACT,MAAM;AAAA,cACN,UAAU;AAAA,cACV,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF,EAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,SAAS,CAAC;AAAA,MACvB,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,kBAAkB,IAAI,eAAe,KAAK,CAAC;AAAA,QAChD;AACA,YAAI,KAAK,GAAG;AACV,UAAG,eAAe;AAAA,QACpB;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,IAAI,YAAY,CAAC;AAC/B,UAAG,YAAY,UAAU,IAAI,OAAO,GAAG,IAAI,EAAE,SAAS,IAAI,MAAM,GAAG,IAAI;AAAA,QACzE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,gBAAgB,CAAC,GAAG,gBAAgB;AAAA,QACpC,MAAM,CAAC,GAAG,MAAM;AAAA,QAChB,QAAQ,CAAC,GAAG,QAAQ;AAAA,QACpB,SAAS,CAAC,GAAG,MAAM,SAAS;AAAA,QAC5B,cAAc,CAAC,GAAG,WAAW,cAAc;AAAA,QAC3C,SAAS,CAAC,GAAG,SAAS;AAAA,QACtB,QAAQ,CAAC,GAAG,QAAQ;AAAA,QACpB,MAAM,CAAC,GAAG,MAAM;AAAA,QAChB,OAAO,CAAC,GAAG,OAAO;AAAA,QAClB,SAAS,CAAC,GAAG,SAAS;AAAA,MACxB;AAAA,MACA,SAAS;AAAA,QACP,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,QAAQ;AAAA,MACnB,UAAU,CAAI,oBAAoB;AAAA,MAClC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,QAAQ,OAAO,GAAG,WAAW,QAAQ,GAAG,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,MAC1G,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,UAAU,GAAG,CAAC;AACnC,UAAG,WAAW,SAAS,SAAS,kDAAkD,QAAQ;AACxF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,UAC/C,CAAC;AACD,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,UAAU,IAAI,OAAO,KAAK,IAAI,EAAE,MAAM,IAAI,EAAE,EAAE,SAAS,IAAI,MAAM,KAAK,IAAI;AAAA,QAC1F;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,+CAA+C;AAAA,MACxD,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,MACtB;AAAA,MACA,UAAU;AAAA,MACV,QAAQ,CAAC,sCAAsC;AAAA,IACjD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,gBAAgB;AAAA,MAC1B,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,gBAAgB;AAAA,MAC1B,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}