#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量重命名user_data文件，删除user_data_20250720_前缀
"""

import os
import shutil
from pathlib import Path

def rename_user_data_files():
    """批量重命名user_data文件"""
    user_data_dir = Path("crawler/processed/final_data")
    
    if not user_data_dir.exists():
        print(f"❌ 目录不存在: {user_data_dir}")
        return
    
    # 查找所有带有user_data_20250720_前缀的文件
    files_to_rename = []
    for file_path in user_data_dir.glob("final_data_20250720_*"):
        files_to_rename.append(file_path)
    
    if not files_to_rename:
        print("✅ 没有找到需要重命名的文件")
        return
    
    print(f"📁 找到 {len(files_to_rename)} 个文件需要重命名")
    
    # 重命名文件
    renamed_count = 0
    for file_path in files_to_rename:
        # 提取新文件名（删除user_data_20250720_前缀）
        new_name = file_path.name.replace("final_data_20250720_", "")
        new_path = file_path.parent / new_name
        
        # 检查目标文件是否已存在
        if new_path.exists():
            print(f"⚠️ 目标文件已存在，跳过: {new_path.name}")
            continue
        
        try:
            # 重命名文件
            file_path.rename(new_path)
            print(f"✅ 重命名: {file_path.name} → {new_name}")
            renamed_count += 1
        except Exception as e:
            print(f"❌ 重命名失败: {file_path.name} - {e}")
    
    print(f"🎉 重命名完成！共重命名 {renamed_count} 个文件")

if __name__ == "__main__":
    rename_user_data_files() 