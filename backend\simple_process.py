#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化處理腳本：PTT所有立委 + YouTube未處理立委 + Gemini分析 + MongoDB更新
"""

import os
import sys
import json
import logging
from typing import List, Dict

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('simple_process.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_legislators():
    """獲取立委列表"""
    return [
        "丁學忠", "傅崐萁", "廖偉翔", "楊瓊瓔", "江啟臣", "游顥", "呂玉玲", 
        "廖先翔", "張智倫", "邱若華", "魯明哲", "萬美玲", "羅明才", "林思銘", 
        "林德福", "鄭正鈐", "賴士葆", "涂權吉", "徐欣瑩", "李彥秀", "林沛祥", 
        "洪孟楷", "牛煦庭", "王鴻薇"
    ]

def is_valid_analysis_file(file_path: str) -> bool:
    """檢查分析文件是否有效（有實際內容）"""
    try:
        # 檢查文件大小
        if os.path.getsize(file_path) == 0:
            return False
        
        # 檢查JSON內容
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 檢查是否有實際的分析數據
        if not data:
            return False
        
        # 檢查是否有用戶數據
        if 'users' not in data or not data['users']:
            return False
        
        # 檢查用戶數據是否有效
        user_count = len(data['users'])
        if user_count == 0:
            return False
        
        # 檢查是否有情感分析結果
        has_emotion_data = False
        for user_data in data['users'].values():
            if 'emotion_analysis' in user_data and user_data['emotion_analysis']:
                has_emotion_data = True
                break
        
        return has_emotion_data
        
    except Exception as e:
        logger.error(f"檢查文件 {file_path} 時出錯: {e}")
        return False

def check_data_files():
    """檢查數據文件狀態"""
    logger.info("📊 檢查數據文件狀態...")
    
    legislators = get_legislators()
    yt_analyzed = []
    yt_unanalyzed = []
    ptt_analyzed = []
    ptt_unanalyzed = []
    
    for legislator in legislators:
        logger.info(f"\n👤 {legislator}:")
        
        # 檢查YouTube數據
        yt_file = f"crawler/data/youtube/{legislator}.json"
        if os.path.exists(yt_file):
            try:
                with open(yt_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"  📺 YouTube: 有數據 ({len(data)} 條)")
            except:
                logger.info(f"  📺 YouTube: 文件存在但讀取失敗")
        else:
            logger.info(f"  📺 YouTube: 無文件")
        
        # 檢查PTT數據
        ptt_file = f"crawler/data/ptt/{legislator}.json"
        if os.path.exists(ptt_file):
            try:
                with open(ptt_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"  💻 PTT: 有數據 ({len(data)} 條)")
            except:
                logger.info(f"  💻 PTT: 文件存在但讀取失敗")
        else:
            logger.info(f"  💻 PTT: 無文件")
        
        # 檢查最終分析結果（檢查文件內容是否有效）
        final_file = f"crawler/processed/final_data/{legislator}_使用者分析.json"
        if os.path.exists(final_file):
            if is_valid_analysis_file(final_file):
                logger.info(f"  ✅ 最終分析: 已完成且有效")
                yt_analyzed.append(legislator)
                ptt_analyzed.append(legislator)
            else:
                logger.info(f"  ⚠️ 最終分析: 文件存在但內容無效，需要重新處理")
                yt_unanalyzed.append(legislator)
                ptt_unanalyzed.append(legislator)
        else:
            logger.info(f"  ❌ 最終分析: 文件不存在，需要處理")
            yt_unanalyzed.append(legislator)
            ptt_unanalyzed.append(legislator)
    
    # 顯示統計摘要
    logger.info(f"\n📊 統計摘要:")
    logger.info(f"  📺 YouTube已分析: {len(yt_analyzed)} 位 ({', '.join(yt_analyzed)})")
    logger.info(f"  📺 YouTube未分析: {len(yt_unanalyzed)} 位 ({', '.join(yt_unanalyzed)})")
    logger.info(f"  💻 PTT已分析: {len(ptt_analyzed)} 位 ({', '.join(ptt_analyzed)})")
    logger.info(f"  💻 PTT未分析: {len(ptt_unanalyzed)} 位 ({', '.join(ptt_unanalyzed)})")
    logger.info(f"  📋 總計: {len(legislators)} 位立委")

def process_ptt_data():
    """處理PTT所有立委數據（因為PTT都沒分析過）"""
    logger.info("🔄 開始處理PTT所有立委數據（PTT都沒分析過）...")
    
    legislators = get_legislators()
    success_count = 0
    
    for legislator in legislators:
        ptt_file = f"crawler/data/ptt/{legislator}.json"
        
        if os.path.exists(ptt_file):
            try:
                # 讀取PTT數據
                with open(ptt_file, 'r', encoding='utf-8') as f:
                    ptt_data = json.load(f)
                
                logger.info(f"  📁 {legislator}: PTT數據 {len(ptt_data)} 條")
                
                # 創建整合數據目錄
                os.makedirs(f"crawler/processed/user_data", exist_ok=True)
                
                # 轉換為Gemini格式
                gemini_data = {
                    "legislator": legislator,
                    "platform": "ptt",
                    "users": {}
                }
                
                # 處理PTT數據結構
                for item in ptt_data:
                    if isinstance(item, dict) and 'user' in item:
                        user_id = item['user']
                        if user_id not in gemini_data["users"]:
                            gemini_data["users"][user_id] = {
                                "comments": [],
                                "platform": "ptt"
                            }
                        
                        # 添加評論
                        comment = {
                            "標題": item.get('title', ''),
                            "留言內容": item.get('content', ''),
                            "時間": item.get('time', ''),
                            "推文數": item.get('push', 0)
                        }
                        gemini_data["users"][user_id]["comments"].append(comment)
                
                # 保存整合後的數據
                output_file = f"crawler/processed/user_data/{legislator}_gemini_format.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(gemini_data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"  ✅ {legislator}: PTT數據整合完成 ({len(gemini_data['users'])} 位用戶)")
                success_count += 1
                
            except Exception as e:
                logger.error(f"  ❌ {legislator}: PTT數據處理失敗 - {e}")
        else:
            logger.info(f"  ⚠️ {legislator}: 無PTT數據文件")
    
    logger.info(f"🎉 PTT數據處理完成！成功處理 {success_count}/{len(legislators)} 位立委")
    return success_count > 0

def process_youtube_data():
    """處理YouTube未分析立委數據（跳過已分析的）"""
    logger.info("🔄 開始處理YouTube未分析立委數據（跳過已分析的）...")
    
    legislators = get_legislators()
    success_count = 0
    skipped_count = 0
    
    for legislator in legislators:
        yt_file = f"crawler/data/youtube/{legislator}.json"
        
        # 檢查是否已經有效分析過
        final_file = f"crawler/processed/final_data/{legislator}_使用者分析.json"
        if os.path.exists(final_file) and is_valid_analysis_file(final_file):
            logger.info(f"  ⏭️ {legislator}: 已有效分析過，跳過YouTube處理")
            skipped_count += 1
            continue
        
        if os.path.exists(yt_file):
            try:
                # 讀取YouTube數據
                with open(yt_file, 'r', encoding='utf-8') as f:
                    yt_data = json.load(f)
                
                logger.info(f"  📁 {legislator}: YouTube數據 {len(yt_data)} 條")
                
                # 檢查是否已有整合數據（PTT數據）
                existing_file = f"crawler/processed/user_data/{legislator}_gemini_format.json"
                if os.path.exists(existing_file):
                    # 讀取現有PTT數據
                    with open(existing_file, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                    
                    # 添加YouTube數據到現有PTT數據中
                    for item in yt_data:
                        if isinstance(item, dict) and 'user' in item:
                            user_id = item['user']
                            if user_id not in existing_data["users"]:
                                existing_data["users"][user_id] = {
                                    "comments": [],
                                    "platform": "youtube"
                                }
                            
                            # 添加評論
                            comment = {
                                "標題": item.get('title', ''),
                                "留言內容": item.get('content', ''),
                                "時間": item.get('time', ''),
                                "讚數": item.get('like', 0)
                            }
                            existing_data["users"][user_id]["comments"].append(comment)
                    
                    # 保存合併後的數據
                    output_file = f"crawler/processed/user_data/{legislator}_gemini_format.json"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(existing_data, f, ensure_ascii=False, indent=2)
                    
                    logger.info(f"  ✅ {legislator}: YouTube+PTT數據合併完成 ({len(existing_data['users'])} 位用戶)")
                    
                else:
                    # 只有YouTube數據
                    gemini_data = {
                        "legislator": legislator,
                        "platform": "youtube",
                        "users": {}
                    }
                    
                    for item in yt_data:
                        if isinstance(item, dict) and 'user' in item:
                            user_id = item['user']
                            if user_id not in gemini_data["users"]:
                                gemini_data["users"][user_id] = {
                                    "comments": [],
                                    "platform": "youtube"
                                }
                            
                            comment = {
                                "標題": item.get('title', ''),
                                "留言內容": item.get('content', ''),
                                "時間": item.get('time', ''),
                                "讚數": item.get('like', 0)
                            }
                            gemini_data["users"][user_id]["comments"].append(comment)
                    
                    output_file = f"crawler/processed/user_data/{legislator}_gemini_format.json"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(gemini_data, f, ensure_ascii=False, indent=2)
                    
                    logger.info(f"  ✅ {legislator}: YouTube數據整合完成 ({len(gemini_data['users'])} 位用戶)")
                
                success_count += 1
                
            except Exception as e:
                logger.error(f"  ❌ {legislator}: YouTube數據處理失敗 - {e}")
        else:
            logger.info(f"  ⚠️ {legislator}: 無YouTube數據文件")
    
    logger.info(f"🎉 YouTube數據處理完成！成功處理 {success_count} 位立委，跳過 {skipped_count} 位已分析立委")
    return success_count > 0

def run_gemini_analysis():
    """執行Gemini情感分析"""
    logger.info("🧠 開始執行Gemini情感分析...")
    
    try:
        # 導入Gemini分析模組
        from gemini_analyzer import analyze_legislators_emotions
        
        legislators = get_legislators()
        
        success = analyze_legislators_emotions(
            legislators=legislators,
            batch_size=500,
            quiet=False,
            incremental=True,
            base_dir="."
        )
        
        if success:
            logger.info(f"🎉 Gemini情感分析完成！")
        else:
            logger.warning(f"⚠️ Gemini情感分析部分失敗")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Gemini情感分析失敗: {e}")
        return False

def update_mongodb():
    """更新MongoDB數據庫"""
    logger.info("🗄️ 開始更新MongoDB數據庫...")
    
    try:
        # 導入最終處理模組
        from final_processor import process_all_legislators_final_data
        
        success = process_all_legislators_final_data(".")
        
        if success:
            logger.info(f"🎉 MongoDB更新完成！")
        else:
            logger.warning(f"⚠️ MongoDB更新部分失敗")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ MongoDB更新失敗: {e}")
        return False

def main():
    """主函數"""
    try:
        logger.info("🚀 開始執行簡化處理流程...")
        
        # 步驟1: 檢查數據狀態
        check_data_files()
        
        # 步驟2: 處理PTT所有立委數據
        if not process_ptt_data():
            logger.error("❌ PTT數據處理失敗，停止執行")
            return 1
        
        # 步驟3: 處理YouTube未處理立委數據
        if not process_youtube_data():
            logger.error("❌ YouTube數據處理失敗，停止執行")
            return 1
        
        # 步驟4: 執行Gemini情感分析
        if not run_gemini_analysis():
            logger.error("❌ Gemini情感分析失敗，停止執行")
            return 1
        
        # 步驟5: 更新MongoDB
        if not update_mongodb():
            logger.error("❌ MongoDB更新失敗，停止執行")
            return 1
        
        logger.info("🎉 所有流程執行完成！")
        return 0
        
    except KeyboardInterrupt:
        logger.info("⏹️ 用戶中斷操作")
        return 1
    except Exception as e:
        logger.error(f"❌ 系統錯誤: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 