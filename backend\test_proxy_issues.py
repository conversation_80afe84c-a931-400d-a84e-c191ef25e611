#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理問題診斷腳本
分析為什麼免費代理都不可用
"""

import requests
import time
from bs4 import BeautifulSoup

def test_direct_connection():
    """測試直接連線是否正常"""
    print("🔍 測試1: 直接連線測試")
    try:
        response = requests.get('https://httpbin.org/ip', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 直接連線正常，本機IP: {data.get('origin', 'unknown')}")
            return True
        else:
            print(f"❌ 直接連線失敗，狀態碼: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 直接連線失敗: {e}")
        return False

def test_proxy_sources():
    """測試代理源網站是否可訪問"""
    print("\n🔍 測試2: 代理源網站訪問測試")
    
    sources = [
        {
            'name': 'US Proxy List',
            'url': 'https://www.us-proxy.org/',
        },
        {
            'name': 'Free Proxy List',
            'url': 'https://free-proxy-list.net/',
        },
        {
            'name': 'Proxy List API',
            'url': 'https://www.proxy-list.download/api/v1/get?type=https',
        }
    ]
    
    accessible_sources = 0
    for source in sources:
        try:
            print(f"🔄 測試 {source['name']}...")
            response = requests.get(source['url'], timeout=15, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                content_length = len(response.text)
                print(f"✅ {source['name']} 可訪問，內容長度: {content_length}")
                accessible_sources += 1
                
                # 簡單分析內容
                if 'proxy' in response.text.lower() or ':' in response.text:
                    print(f"   📊 內容包含代理相關信息")
                else:
                    print(f"   ⚠️ 內容可能不包含代理信息")
            else:
                print(f"❌ {source['name']} 訪問失敗，狀態碼: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {source['name']} 訪問失敗: {e}")
            
        time.sleep(1)  # 避免請求太快
    
    print(f"\n📊 代理源測試結果: {accessible_sources}/{len(sources)} 個源可訪問")
    return accessible_sources > 0

def test_alternative_test_endpoints():
    """測試不同的代理測試端點"""
    print("\n🔍 測試3: 代理測試端點測試")
    
    test_endpoints = [
        'https://httpbin.org/ip',
        'https://ipapi.co/json',
        'https://ifconfig.me/ip',
        'https://api.ipify.org?format=json',
        'https://checkip.amazonaws.com',
    ]
    
    working_endpoints = []
    for endpoint in test_endpoints:
        try:
            print(f"🔄 測試端點: {endpoint}")
            response = requests.get(endpoint, timeout=10)
            if response.status_code == 200:
                print(f"✅ {endpoint} 可用")
                print(f"   回應內容: {response.text[:100]}...")
                working_endpoints.append(endpoint)
            else:
                print(f"❌ {endpoint} 失敗，狀態碼: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} 失敗: {e}")
        time.sleep(1)
    
    print(f"\n📊 測試端點結果: {len(working_endpoints)}/{len(test_endpoints)} 個端點可用")
    return working_endpoints

def test_sample_proxies():
    """測試一些已知的公共代理"""
    print("\n🔍 測試4: 公共代理測試")
    
    # 一些常見的公共代理（僅用於測試）
    test_proxies = [
        'http://***********:80',
        'http://************:8888',
        'http://*************:3128',
        # 這些可能不可用，僅用於測試連接邏輯
    ]
    
    working_count = 0
    for proxy_url in test_proxies:
        try:
            print(f"🔄 測試代理: {proxy_url}")
            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            
            response = requests.get(
                'https://httpbin.org/ip',
                proxies=proxies,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                origin_ip = data.get('origin', '')
                print(f"✅ 代理可用: {proxy_url} -> IP: {origin_ip}")
                working_count += 1
            else:
                print(f"❌ 代理失敗: {proxy_url} (狀態碼: {response.status_code})")
                
        except requests.exceptions.ProxyError as e:
            print(f"❌ 代理錯誤: {proxy_url} - {e}")
        except requests.exceptions.ConnectTimeout as e:
            print(f"⏰ 連接超時: {proxy_url}")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 連接錯誤: {proxy_url}")
        except Exception as e:
            print(f"❌ 其他錯誤: {proxy_url} - {e}")
        
        time.sleep(1)
    
    print(f"\n📊 公共代理測試結果: {working_count}/{len(test_proxies)} 個代理可用")
    return working_count > 0

def analyze_proxy_requirements():
    """分析代理要求和限制"""
    print("\n🔍 測試5: 代理要求分析")
    
    # 測試是否需要認證
    print("📋 分析要點:")
    print("1. 免費代理通常不穩定，壽命短")
    print("2. 很多免費代理需要驗證或有使用限制")
    print("3. 一些代理可能只支持特定協議")
    print("4. 地理位置限制可能影響訪問")
    print("5. 反爬蟲機制可能阻止代理訪問")
    
    # 測試不同的請求頭
    print("\n🔄 測試不同User-Agent...")
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'curl/7.68.0'
    ]
    
    for ua in user_agents:
        try:
            response = requests.get(
                'https://httpbin.org/headers',
                headers={'User-Agent': ua},
                timeout=10
            )
            if response.status_code == 200:
                print(f"✅ User-Agent有效: {ua[:50]}...")
            else:
                print(f"❌ User-Agent失敗: {ua[:50]}...")
        except Exception as e:
            print(f"❌ User-Agent測試錯誤: {e}")

def main():
    """主診斷函數"""
    print("🚀 開始代理問題診斷...")
    print("=" * 60)
    
    # 測試1: 直接連線
    direct_ok = test_direct_connection()
    
    # 測試2: 代理源網站
    sources_ok = test_proxy_sources()
    
    # 測試3: 測試端點
    endpoints = test_alternative_test_endpoints()
    
    # 測試4: 樣本代理
    sample_proxies_ok = test_sample_proxies()
    
    # 測試5: 代理要求分析
    analyze_proxy_requirements()
    
    # 總結診斷結果
    print("\n" + "=" * 60)
    print("🎯 診斷總結:")
    print(f"✅ 直接連線: {'正常' if direct_ok else '異常'}")
    print(f"✅ 代理源: {'可訪問' if sources_ok else '不可訪問'}")
    print(f"✅ 測試端點: {len(endpoints)} 個可用")
    print(f"✅ 樣本代理: {'有可用' if sample_proxies_ok else '全部失敗'}")
    
    # 提供建議
    print("\n💡 建議:")
    if not direct_ok:
        print("❌ 網路連線有問題，請檢查網路設定")
    elif not sources_ok:
        print("❌ 代理源網站無法訪問，可能被防火牆阻擋")
    elif len(endpoints) == 0:
        print("❌ 測試端點都無法訪問，網路環境可能有限制")
    elif not sample_proxies_ok:
        print("⚠️ 免費代理普遍不可用，這是正常現象")
        print("   建議:")
        print("   1. 使用付費代理服務")
        print("   2. 使用VPN服務")
        print("   3. 使用 --no-proxy 參數直接連線")
        print("   4. 設置本地代理（如V2Ray、Clash等）")
    else:
        print("✅ 基本功能正常，代理問題可能是臨時性的")

if __name__ == "__main__":
    main() 