.politicians-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 20px;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
  
  h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 0.5rem;
  }
  
  p {
    font-size: 1.1rem;
    color: #666;
  }
}

.category-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
  
  @media (max-width: 768px) {
    gap: 0.5rem;
  }
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px solid #e9ecef;
  background: white;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: #666;
  
  i {
    font-size: 1.1rem;
  }
  
  &:hover {
    border-color: #007bff;
    color: #007bff;
  }
  
  &.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
  }
  
  @media (max-width: 768px) {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    
    span {
      display: none;
    }
  }
}

.politicians-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.politician-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.politician-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .politician-card:hover & img {
    transform: scale(1.05);
  }
  
  .politician-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    i {
      font-size: 1.2rem;
    }
  }
}

.politician-info {
  padding: 1.5rem;
  
  h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 0.5rem;
    font-weight: 600;
  }
  
  .politician-title {
    color: #007bff;
    font-weight: 500;
    margin-bottom: 0.25rem;
  }
  
  .politician-region {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }
}

.politician-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
    
    i {
      color: #007bff;
      font-size: 0.8rem;
    }
  }
}

.politician-actions {
  padding: 0 1.5rem 1.5rem;
  
  .btn-detail {
    width: 100%;
    padding: 0.75rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1rem;
    
    &:hover {
      background: #0056b3;
    }
    
    i {
      font-size: 0.9rem;
    }
  }
}

.no-data {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
  
  i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
  }
  
  h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #333;
  }
  
  p {
    font-size: 1.1rem;
  }
}
