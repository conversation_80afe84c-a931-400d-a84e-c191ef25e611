#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据整合器模块
用于整合多平台的爬虫数据
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class DataIntegrator:
    """数据整合器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def integrate_all_legislators(self, legislators: List[str], platforms: List[str]) -> Dict[str, Any]:
        """
        整合所有立委的数据
        
        Args:
            legislators: 立委列表
            platforms: 平台列表
            
        Returns:
            整合结果
        """
        try:
            self.logger.info(f"🔄 开始整合数据: {len(legislators)} 位立委, 平台: {platforms}")
            
            success_count = 0
            total_records = 0
            
            for legislator in legislators:
                try:
                    records = self.integrate_legislator_data(legislator, platforms)
                    total_records += records
                    success_count += 1
                    self.logger.info(f"✅ {legislator} 数据整合完成: {records} 条记录")
                except Exception as e:
                    self.logger.error(f"❌ {legislator} 数据整合失败: {e}")
            
            return {
                'success': True,
                'processed_legislators': success_count,
                'total_legislators': len(legislators),
                'total_records': total_records
            }
            
        except Exception as e:
            self.logger.error(f"❌ 数据整合失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def integrate_legislator_data(self, legislator: str, platforms: List[str]) -> int:
        """
        整合单个立委的数据
        
        Args:
            legislator: 立委姓名
            platforms: 平台列表
            
        Returns:
            整合的记录数
        """
        try:
            total_records = 0
            integrated_data = []
            
            # 读取各平台数据
            for platform in platforms:
                platform_data = self.load_platform_data(legislator, platform)
                if platform_data:
                    integrated_data.extend(platform_data)
                    total_records += len(platform_data)
                    
            # 保存整合后的数据
            if integrated_data:
                self.save_integrated_data(legislator, integrated_data)
                
            return total_records
            
        except Exception as e:
            self.logger.error(f"❌ {legislator} 数据整合失败: {e}")
            return 0
    
    def load_platform_data(self, legislator: str, platform: str) -> List[Dict]:
        """加载平台数据"""
        try:
            data_file = f"crawler/data/{platform}/{legislator}.json"
            
            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # 统一数据格式
                normalized_data = self.normalize_platform_data(data, platform)
                return normalized_data
            else:
                self.logger.warning(f"⚠️ 数据文件不存在: {data_file}")
                return []
                
        except Exception as e:
            self.logger.error(f"❌ 加载 {legislator}-{platform} 数据失败: {e}")
            return []
    
    def normalize_platform_data(self, data: Any, platform: str) -> List[Dict]:
        """标准化平台数据格式"""
        try:
            normalized = []
            
            if platform == 'youtube':
                if isinstance(data, list):
                    for video in data:
                        if isinstance(video, dict) and '留言資料' in video:
                            for comment in video.get('留言資料', []):
                                normalized.append({
                                    'platform': 'youtube',
                                    'title': video.get('影片標題', ''),
                                    'user': comment.get('主留言', {}).get('用戶名', ''),
                                    'content': comment.get('主留言', {}).get('留言內容', ''),
                                    'time': comment.get('主留言', {}).get('留言時間', ''),
                                    'likes': comment.get('主留言', {}).get('按讚數', '0'),
                                    'video_url': video.get('video_url', ''),
                                    'publish_time': video.get('影片發布時間', '')
                                })
                                
                                # 处理回复
                                for reply in comment.get('回覆', []):
                                    normalized.append({
                                        'platform': 'youtube',
                                        'title': video.get('影片標題', ''),
                                        'user': reply.get('回覆留言者', ''),
                                        'content': reply.get('回覆留言內容', ''),
                                        'time': reply.get('回覆留言時間', ''),
                                        'likes': reply.get('回覆留言按讚數', '0'),
                                        'video_url': video.get('video_url', ''),
                                        'publish_time': video.get('影片發布時間', ''),
                                        'is_reply': True
                                    })
            
            elif platform == 'ptt':
                if isinstance(data, list):
                    for article in data:
                        if isinstance(article, dict):
                            # 主文章
                            normalized.append({
                                'platform': 'ptt',
                                'title': article.get('標題', ''),
                                'user': article.get('作者', ''),
                                'content': article.get('內容', ''),
                                'time': article.get('時間', ''),
                                'url': article.get('url', ''),
                                'board': article.get('看板', '')
                            })
                            
                            # 推文
                            for comment in article.get('推文', []):
                                normalized.append({
                                    'platform': 'ptt',
                                    'title': article.get('標題', ''),
                                    'user': comment.get('用戶', ''),
                                    'content': comment.get('內容', ''),
                                    'time': comment.get('時間', ''),
                                    'push_type': comment.get('推文類型', ''),
                                    'url': article.get('url', ''),
                                    'board': article.get('看板', ''),
                                    'is_comment': True
                                })
            
            elif platform == 'threads':
                # Threads数据格式处理
                if isinstance(data, list):
                    for post in data:
                        if isinstance(post, dict):
                            normalized.append({
                                'platform': 'threads',
                                'title': post.get('貼文內容', '')[:100] + '...' if len(post.get('貼文內容', '')) > 100 else post.get('貼文內容', ''),
                                'user': post.get('用戶名稱', ''),
                                'content': post.get('貼文內容', ''),
                                'time': post.get('發布時間', ''),
                                'likes': post.get('按讚數', '0'),
                                'url': post.get('貼文連結', '')
                            })
            
            return normalized
            
        except Exception as e:
            self.logger.error(f"❌ 标准化 {platform} 数据失败: {e}")
            return []
    
    def save_integrated_data(self, legislator: str, data: List[Dict]):
        """保存整合后的数据"""
        try:
            # 确保目录存在
            output_dir = "crawler/processed/alldata"
            os.makedirs(output_dir, exist_ok=True)
            
            output_file = f"{output_dir}/{legislator}.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            self.logger.info(f"✅ {legislator} 整合数据已保存: {output_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存 {legislator} 整合数据失败: {e}")

def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO)
    
    integrator = DataIntegrator()
    result = integrator.integrate_all_legislators(['高虹安'], ['youtube', 'ptt'])
    
    print(f"结果: {result}")

if __name__ == "__main__":
    main() 