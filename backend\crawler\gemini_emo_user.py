#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import re
import time
import random
from concurrent.futures import ThreadPoolExecutor
import google.generativeai as genai
from itertools import cycle

# 設定路徑 - 使用 processed 資料夾結構
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
PROCESSED_DIR = os.path.join(CURRENT_DIR, 'processed')
USER_DATA_DIR = os.path.join(PROCESSED_DIR, 'user_data')
FINAL_DATA_DIR = os.path.join(PROCESSED_DIR, 'final_data')

# 確保目錄存在
os.makedirs(FINAL_DATA_DIR, exist_ok=True)

# ===== 工具與設定 =====
def load_api_keys(path=None):
    """加載API keys，支持多個路徑"""
    if path is None:
        # 嘗試多個可能的路徑
        possible_paths = [
            'api.json',  # 當前目錄
            '../api.json',  # 上一級目錄（backend/api.json）
            '../../api.json',  # 上兩級目錄
            os.path.join(os.path.dirname(__file__), '../api.json'),  # 相對於當前文件的上級目錄
            os.path.join(os.path.dirname(os.path.dirname(__file__)), 'api.json'),  # backend/api.json
        ]
        
        for test_path in possible_paths:
            if os.path.exists(test_path):
                path = test_path
                break
        
        if path is None:
            print(f"❌ 無法找到 api.json 文件。嘗試過的路徑:")
            for p in possible_paths:
                abs_path = os.path.abspath(p)
                exists = "✅" if os.path.exists(abs_path) else "❌"
                print(f"   {exists} {abs_path}")
            return []
    
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                api_keys = data.get('api_keys', [])
                print(f"✅ 成功加載 {len(api_keys)} 個API keys from {os.path.abspath(path)}")
                return api_keys
        except Exception as e:
            print(f"❌ 加載API keys失敗: {e}")
            print(f"   文件路徑: {os.path.abspath(path) if path else 'None'}")
            return []

emotion_keywords = {
    "anger": ["氣", "火大", "無恥", "去死", "惱怒", "抓狂", "氣死人"],
    "disgust": ["噁", "垃圾", "爛", "快吐", "髒", "惡心", "臭"],
    "fear": ["怕", "恐", "好可怕", "擔憂", "不安", "害怕"],
    "surprise": ["竟然", "傻眼", "沒想到", "驚呆", "嚇到", "不可思議"],
    "joy": ["太好了", "爽翻", "開心", "歡呼", "開心到不行"],
    "trust": ["支持", "相信", "挺你", "信任", "為你加油"],
    "anticipation": ["期待", "快點", "拭目以待", "等著瞧", "有感覺"],
    "sadness": ["可憐", "難過", "失望", "痛心", "心碎", "悲傷"]
}

# Build the LLM prompt
def build_prompt(combined_content, user_name, person_name, positive_party, negative_party):
    return f"""
你是一位專業的語意理解與輿情分析員，任務是根據政治留言的語意、情緒與立場，判斷其對於「立法委員{person_name}罷免案的情緒與立場判斷」，並回傳格式正確的 JSON 結果。
支持罷免{person_name}是負面(NEGATIVE)
反對罷免{person_name}是正面(POSITIVE)

【分析任務】
請根據下列使用者的所有留言內容，判斷：
1. 該使用者的每一則留言，判斷整體立場（Label）：
   - 是否支持罷免{person_name}（{positive_party}立委）NEGATIVE？
   - 或是反對罷免（即支持{person_name}或批評{negative_party}）POSITIVE？

2. 該使用者留言情緒（Emotion）：從以下八種中選擇最接近的情緒以下是判斷時些微加分原則:假如用戶偏向支持罷免則情緒請往負面情緒為優先反之用戶反對罷免情緒則是正面情緒為優先。
3. 最後說明評判的原因

【立場分類規則】
- NEGATIVE：留言表達支持罷免{person_name}，或批評{positive_party}、{person_name}本人，支持{negative_party}或其立場。
- POSITIVE：留言反對罷免{person_name}，或批評{negative_party}、支持{positive_party}、或{person_name}本人。
- 若留言內容模糊或無法判斷，請以語氣傾向與上下文意圖推論最可能立場。
- 反對或批評{negative_party}為POSITIVE
- 國民黨是藍營，民進黨是綠營，依據立委所屬政黨判斷是否支持罷免
- 如果政黨為國民黨那批評民進黨、賴清德總統、相關民進黨議員為POSITIVE，反之亦然
- 如果政黨為民進黨那批評國民黨、陳水扁、馬英九、相關國民黨議員為POSITIVE，反之亦然

【強化判讀重點】
- 若出現嘲諷、反話、雙關語、鄉民語氣，需判斷是否為反語，並根據語境還原真實立場。
- 留言若批評對象是"{negative_party}"、"總統"、"綠營"傾向 POSITIVE，批評"{positive_party}"、"{person_name}"、"藍營"傾向 NEGATIVE。
- 若內容太模糊，可根據語氣合理推測立場，但不強行猜測。
- 強調{positive_party}與共產黨合作的為負面
- 如果有表達對政治人物的反感，則根據反感的陣營，判別是否支持罷免
- 忽略新聞網站來源文字

【情緒分類規則】
請從以下八種英文小寫情緒中選擇最符合者：
{emotion_keywords}

請務必以以下 JSON Schema 格式回傳：
{{
  "Label": "POSITIVE" | "NEGATIVE",
  "Emotion": "joy" | "trust" | "anticipation" | "sadness" | "surprise" | "disgust" | "fear" | "anger"
}}

JSON Response:
整合留言內容：{combined_content}
說明評判的原因:
"""

def analyze_user_comments(model, user_name, combined_content, person_name, positive_party, negative_party, jsonfile, txtfile, max_retries=6):
    """分析單一使用者的留言內容"""
    prompt = build_prompt(combined_content, user_name, person_name, positive_party, negative_party)
    
    safety_settings = [
        {
            "category": "HARM_CATEGORY_HATE_SPEECH",
            "threshold": "BLOCK_NONE"
        },
        {
            "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            "threshold": "BLOCK_NONE"
        },
        {
            "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
            "threshold": "BLOCK_NONE"
        },
        {
            "category": "HARM_CATEGORY_HARASSMENT",
            "threshold": "BLOCK_NONE"
        }
    ]

    # 初始化平台变量
    platform = 'unknown'
    
    # 從留言內容中提取平台信息（格式：平台：xxx）
    platform_match = re.search(r'平台：([^\n]+)', combined_content)
    if platform_match:
        platform = platform_match.group(1).strip()
        # 標準化平台名稱
        if platform.lower() in ['youtube', 'yt']:
            platform = 'yt'
        elif platform.lower() in ['ptt']:
            platform = 'ptt'
        elif platform.lower() in ['threads', 'thread']:
            platform = 'threads'
        elif platform.lower() in ['facebook', 'fb']:
            platform = 'fb'

    for attempt in range(max_retries):
        try:
            response = model.generate_content(
                prompt,
                safety_settings=safety_settings
            )
            
            # 檢查回應是否為空
            if response is None:
                raise ValueError("Response is None")
            
            if not hasattr(response, 'text') or not response.text:
                raise ValueError("Response has no text content")
                
            text = response.text.strip()
            if not text:
                raise ValueError("Empty response text")
            # 嚴格檢查Label/Emotion格式
            label = re.search(r'"Label"\s*:\s*"(POSITIVE|NEGATIVE)"', text)
            emotion = re.search(r'"Emotion"\s*:\s*"(joy|trust|anticipation|sadness|surprise|disgust|fear|anger)"', text)
            result = {
                "使用者": user_name,
                "情感標籤": label.group(1) if label else "Unknown",
                "情緒": emotion.group(1) if emotion else "Unknown",
                "整合留言內容": combined_content,
                "平台": platform  # 添加平台作為獨立欄位
            }
            # 保存結果到JSON檔案
            if os.path.exists(jsonfile):
                with open(jsonfile, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = []
            data.append(result)
            with open(jsonfile, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            # 保存詳細記錄到TXT檔案
            with open(txtfile, 'a', encoding='utf-8') as f:
                f.write(f"=== {time.strftime('%Y-%m-%d %H:%M:%S')} ===\n")
                f.write(f"使用者: {user_name}\n平台: {platform}\n整合留言: {combined_content}\nResponse: {text}\n\n")
            return result
        except Exception as e:
            print(f"Retry {attempt+1}: {e}")
            time.sleep(2)
    # 標記失敗用戶
    fail_result = {
        "使用者": user_name,
        "情感標籤": "Failed",
        "情緒": "Failed",
        "整合留言內容": combined_content,
        "平台": platform  # 添加平台作為獨立欄位
    }
    if os.path.exists(jsonfile):
        with open(jsonfile, 'r', encoding='utf-8') as f:
            data = json.load(f)
    else:
        data = []
    data.append(fail_result)
    with open(jsonfile, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    return fail_result

def process_user_data(input_file, person_name, positive_party, negative_party, platform_filter=None):
    """
    處理用戶數據文件，返回過濾後的用戶數據（不進行分析）
    
    Args:
        input_file: 輸入文件路徑
        person_name: 立委姓名
        positive_party: 正面政黨
        negative_party: 負面政黨
        platform_filter: 平台過濾器，如果指定則只處理該平台的數據
        
    Returns:
        dict: 過濾後的用戶數據，格式為 {user_name: user_info}
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            user_data = json.load(f)
    except Exception as e:
        print(f"❌ 讀取文件失敗: {input_file} - {e}")
        return {}
    
    print(f"�� 開始處理 {person_name} 的用戶數據...")
    
    # 過濾用戶數據
    filtered_users = {}
    total_users = len(user_data)
    filtered_count = 0
    
    for user_name, user_info in user_data.items():
        comments = user_info.get('comments', [])
        
        # 如果指定了平台過濾器，只保留該平台的評論
        if platform_filter:
            filtered_comments = []
            for comment in comments:
                if comment.get('source') == platform_filter:
                    filtered_comments.append(comment)
            
            if filtered_comments:
                filtered_users[user_name] = {
                    'comments': filtered_comments,
                    'latest_date': user_info.get('latest_date', ''),
                    'comment_count': len(filtered_comments)
                }
                filtered_count += 1
        else:
            # 不過濾，保留所有數據
            filtered_users[user_name] = user_info
            filtered_count += 1
    
    if platform_filter:
        print(f"🔍 平台過濾: 只處理 {platform_filter} 平台數據")
        print(f"�� 總用戶數: {total_users}, 過濾後用戶數: {filtered_count}")
    
    if not filtered_users:
        print(f"⚠️  沒有找到符合條件的用戶數據")
        return {}
    
    print(f"✅ 數據過濾完成: {filtered_count}/{total_users} 位用戶")
    return filtered_users

def run_batch(batch_index, batch_data, api_key, person_name, positive_party, negative_party):
    safe_name = person_name.replace(" ", "_")
    temp_dir = f'temp/{safe_name}'
    os.makedirs(temp_dir, exist_ok=True)

    output_path = f'{temp_dir}/batch_{batch_index}.json'
    log_path = f'{temp_dir}/log_{batch_index}.txt'

    # 配置Gemini API
    genai.configure(api_key=api_key)
    model = genai.GenerativeModel('gemini-1.5-flash')
    print(f"[{person_name}][Batch {batch_index}] 開始處理，共 {len(batch_data)} 位使用者")

    # 🚀 並行處理批次內的所有用戶
    with ThreadPoolExecutor(max_workers=min(8, len(batch_data))) as executor:
        futures = []
        for user_name, user_info in batch_data.items():
            # 準備用戶數據
            comments = user_info.get('comments', [])
            combined_content = ""
            for comment in comments:
                title = comment.get('標題', '')
                content = comment.get('留言內容', '')
                if title:
                    combined_content += f"標題: {title}\n"
                if content:
                    combined_content += f"內容: {content}\n"
                combined_content += "---\n"
            
            if combined_content.strip():
                future = executor.submit(
                    analyze_user_comments,
                    model, user_name, combined_content, person_name, 
                    positive_party, negative_party, output_path, log_path
                )
                futures.append((user_name, future))
        
        # 等待所有用戶處理完成
        for user_name, future in futures:
            try:
                result = future.result()
                print(f"[{person_name}][Batch {batch_index}] ✅ {user_name} 處理完成")
            except Exception as e:
                print(f"[{person_name}][Batch {batch_index}] ❌ {user_name} 處理失敗: {e}")
def split_user_batches(user_data, size):
    """將使用者資料分批"""
    users = list(user_data.items())
    batches = []
    for i in range(0, len(users), size):
        batch_dict = dict(users[i:i + size])
        batches.append(batch_dict)
    return batches

def get_users_for_incremental_analysis(legislator_name):
    """
    獲取需要進行增量分析的用戶列表

    Args:
        legislator_name: 立委名稱

    Returns:
        list: 需要分析的用戶ID列表
    """
    import json

    # 讀取當前用戶數據
    current_data_path = os.path.join(USER_DATA_DIR, f"{legislator_name}_gemini_format.json")
    if not os.path.exists(current_data_path):
        return []

    with open(current_data_path, 'r', encoding='utf-8') as f:
        current_data = json.load(f)

    # 讀取已分析的結果
    analyzed_data_path = os.path.join(FINAL_DATA_DIR, f'{legislator_name}_使用者分析.json')
    analyzed_users = set()

    if os.path.exists(analyzed_data_path):
        with open(analyzed_data_path, 'r', encoding='utf-8') as f:
            analyzed_data = json.load(f)
            analyzed_users = {item.get('使用者', '') for item in analyzed_data}

    # 找出新用戶和有新留言的舊用戶
    users_to_analyze = []
    for user_id, user_data in current_data.items():
        if user_id not in analyzed_users:
            # 新用戶
            users_to_analyze.append(user_id)
        else:
            # 檢查是否有新留言（可以通過留言數量或最新日期判斷）
            current_comment_count = user_data.get('comment_count', 0)
            # 這裡可以添加更複雜的邏輯來判斷是否有新留言
            # 暫時假設如果用戶存在就可能有新留言
            users_to_analyze.append(user_id)

    return users_to_analyze

def merge_temp_results(person_name):
    """合併temp目錄中的分析結果到final_data，並添加日期信息"""
    safe_name = person_name.replace(" ", "_")
    temp_dir = f'temp/{safe_name}'
    output_file = os.path.join(FINAL_DATA_DIR, f'{person_name}_使用者分析.json')
    log_output = os.path.join(FINAL_DATA_DIR, f'{person_name}_使用者分析_log.txt')
    
    # 確保final_data目錄存在
    os.makedirs(FINAL_DATA_DIR, exist_ok=True)
    
    # 檢查temp目錄是否存在
    if not os.path.exists(temp_dir):
        print(f"⚠️ temp目錄不存在: {temp_dir}")
        return
    
    # 讀取原始用戶數據以獲取日期信息
    user_data_file = os.path.join(USER_DATA_DIR, f'{person_name}_user.json')
    user_dates = {}
    if os.path.exists(user_data_file):
        try:
            with open(user_data_file, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
                # 提取每個用戶的最新日期
                for user_id, user_info in user_data.items():
                    if 'comments' in user_info:
                        latest_date = None
                        for comment in user_info['comments']:
                            date_str = comment.get('日期', '')
                            if date_str:
                                try:
                                    # 嘗試解析日期
                                    from datetime import datetime
                                    if ' ' in date_str:  # 包含時間的格式
                                        date_str = date_str.split(' ')[0]  # 只取日期部分
                                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                                    if not latest_date or date_obj > latest_date:
                                        latest_date = date_obj
                                except:
                                    continue
                        if latest_date:
                            user_dates[user_id] = latest_date.strftime('%Y-%m-%d')
            print(f"📅 提取了 {len(user_dates)} 個用戶的日期信息")
        except Exception as e:
            print(f"⚠️ 讀取用戶日期信息失敗: {e}")
    
    all_results = []
    with open(log_output, 'w', encoding='utf-8') as log_out:
        for fname in sorted(os.listdir(temp_dir)):
            path = os.path.join(temp_dir, fname)
            if fname.endswith(".json"):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        batch_data = json.load(f)
                        # 為每個結果添加日期信息
                        for result in batch_data:
                            user_id = result.get('使用者', '')
                            
                            # 添加日期信息
                            if user_id in user_dates:
                                result['日期'] = user_dates[user_id]
                            else:
                                result['日期'] = ''  # 如果沒有日期信息，設為空字串
                            
                            # 平台信息已經在分析結果中，不需要額外處理
                            # 確保平台字段存在
                            if '平台' not in result:
                                result['平台'] = 'yt'  # 默認平台
                        
                        all_results.extend(batch_data)
                        print(f"📄 讀取 {fname}: {len(batch_data)} 筆資料")
                except Exception as e:
                    print(f"❌ 讀取 {fname} 失敗: {e}")
            elif fname.endswith(".txt"):
                try:
                    with open(path, 'r', encoding='utf-8') as log_in:
                        log_out.write(f"\n=== {fname} ===\n")
                        log_out.write(log_in.read())
                except Exception as e:
                    print(f"❌ 讀取日誌 {fname} 失敗: {e}")

    # 保存合併結果
    if all_results:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        print(f"✅ 合併完成：{output_file}（共 {len(all_results)} 位使用者）")
        
        # 统计平台分布
        platform_stats = {}
        for result in all_results:
            platform = result.get('平台', 'ptt')
            platform_stats[platform] = platform_stats.get(platform, 0) + 1
        
        print("📊 平台分布统计：")
        for platform, count in platform_stats.items():
            print(f"  {platform}: {count} 位使用者")
    else:
        print(f"⚠️ 沒有找到任何分析結果: {temp_dir}")

def process_api_key_group(task_batches, api_keys):
    """確保每個 batch 都能被分配到一個 API key（循環分配）"""
    futures = []
    key_cycle = cycle(api_keys)
    with ThreadPoolExecutor(max_workers=len(api_keys)) as executor:
        for task_batch in task_batches:
            api_key = next(key_cycle)
            futures.append(
                executor.submit(
                    run_batch,
                    task_batch['batch_index'],
                    task_batch['batch'],
                    api_key,
                    task_batch['person'],
                    task_batch['positive_party'],
                    task_batch['negative_party']
                )
            )
    for future in futures:
        future.result()

def main():
    batch_size = 500  # 每批次處理50位使用者
    api_keys = load_api_keys()
    
    # 確保至少有API鍵可用
    if len(api_keys) < 1:
        print("錯誤：至少需要1個API鍵")
        return
    
    # 每組API鍵的數量
    group_size = 16
    
    # 將API鍵分組
    api_key_groups = [api_keys[i:i+group_size] for i in range(0, len(api_keys), group_size)]
    
    print(f"載入了 {len(api_keys)} 個API鍵，分為 {len(api_key_groups)} 組")

    tasks = [
        {
            "input_path": "USER/丁學忠_user.json",
            "person": "丁學忠",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/傅崐萁_user.json",
            "person": "傅崐萁",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/廖偉翔_user.json",
            "person": "廖偉翔",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/楊瓊瓔_user.json",
            "person": "楊瓊瓔",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/江啟臣_user.json",
            "person": "江啟臣",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/游顥_user.json",
            "person": "游顥",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/呂玉玲_user.json",
            "person": "呂玉玲",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/廖先翔_user.json",
            "person": "廖先翔",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/張智倫_user.json",
            "person": "張智倫",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/邱若華_user.json",
            "person": "邱若華",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/魯明哲_user.json",
            "person": "魯明哲",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/萬美玲_user.json",
            "person": "萬美玲",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/羅明才_user.json",
            "person": "羅明才",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/林思銘_user.json",
            "person": "林思銘",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/林德福_user.json",
            "person": "林德福",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/鄭正鈐_user.json",
            "person": "鄭正鈐",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/賴士葆_user.json",
            "person": "賴士葆",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/涂權吉_user.json",
            "person": "涂權吉",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/徐欣瑩_user.json",
            "person": "徐欣瑩",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/李彥秀_user.json",
            "person": "李彥秀",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/林沛祥_user.json",
            "person": "林沛祥",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/洪孟楷_user.json",
            "person": "洪孟楷",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/牛煦庭_user.json",
            "person": "牛煦庭",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/王鴻薇_user.json",
            "person": "王鴻薇",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        }
    ]

    # 準備所有批次任務
    all_batches = []
    persons = []

    for task in tasks:
        input_path = task["input_path"]
        person = task["person"]
        positive_party = task["positive_party"]
        negative_party = task["negative_party"]

        try:
            # 處理使用者分組資料
            user_data = process_user_data(input_path, person, positive_party, negative_party)
            batches = split_user_batches(user_data, batch_size)
            print(f"\n📦 {person}: {len(user_data)} 位使用者 → {len(batches)} 個批次")

            for i, batch in enumerate(batches):
                all_batches.append({
                    'batch_index': f"{person}_{i}",
                    'batch': batch,
                    'person': person,
                    'positive_party': positive_party,
                    'negative_party': negative_party
                })
            
            persons.append(person)
        except Exception as e:
            print(f"處理任務 {person} 時發生錯誤: {e}")
    
    print(f"共有 {len(all_batches)} 個批次任務")
    process_api_key_group(all_batches, api_keys)
    
    print("\n📂 所有批次已完成，開始合併暫存資料...")
    for p in persons:
        merge_temp_results(p)

    print("\n✅ 所有任務完成，結果存於 data/")

def analyze_legislators_emotions(legislators=None, batch_size=500, quiet=False, incremental=True, platform_filter=None):
    """
    分析多位立委的情感資料（支持增量分析）
    📌 只使用 gemini_format.json 作為輸入

    Args:
        legislators: 立委列表，預設為 None（處理所有立委）
        batch_size: 批次大小
        quiet: 是否安靜模式
        incremental: 是否增量分析（只分析新用戶和有新留言的舊用戶）
        platform_filter: 平台過濾器，如果指定則只處理該平台的數據（如'ptt'）
    """
    if not legislators:
        # 如果沒有指定立委，從 user_data 目錄讀取所有立委
        if os.path.exists(USER_DATA_DIR):
            user_files = [f for f in os.listdir(USER_DATA_DIR) if f.endswith('_gemini_format.json')]
            legislators = [f.replace('_gemini_format.json', '') for f in user_files]
        else:
            print(f"ERROR: {USER_DATA_DIR} 目錄不存在")
            return

    if not quiet:
        mode_text = "增量分析" if incremental else "完整分析"
        platform_text = f"（僅{platform_filter}平台）" if platform_filter else "（全平台）"
        print(f"🤖 開始{mode_text} {len(legislators)} 位立委的情感資料{platform_text}...")
        print("📌 使用 gemini_format.json 作為唯一輸入來源")

    # 建立任務列表
    tasks = []
    for legislator in legislators:
        # 📌 只使用 gemini_format.json 檔案
        input_path = os.path.join(USER_DATA_DIR, f"{legislator}_gemini_format.json")
        if os.path.exists(input_path):
            # 如果是增量分析，檢查是否有需要分析的新用戶
            if incremental:
                users_to_analyze = get_users_for_incremental_analysis(legislator)
                if not users_to_analyze:
                    if not quiet:
                        print(f"⏭️ 跳過 {legislator}: 沒有新用戶或新留言需要分析")
                    continue

            tasks.append({
                "input_path": input_path,
                "person": legislator,
                "positive_party": "國民黨",  # 可根據實際情況調整
                "negative_party": "民進黨",
                "incremental": incremental,
                "platform_filter": platform_filter
            })
        elif not quiet:
            print(f"⚠️ 警告: {input_path} 不存在，跳過")

    if not tasks:
        if not quiet:
            print("❌ 沒有找到任何需要分析的立委資料")
        return

    # 執行分析流程
    api_keys = load_api_keys()
    if len(api_keys) < 1:
        print("❌ 錯誤：至少需要1個API鍵")
        return
    
    # 準備所有批次任務
    all_batches = []
    persons = []

    for task in tasks:
        person_name = task["person"]
        input_path = task["input_path"]
        positive_party = task["positive_party"]
        negative_party = task["negative_party"]
        platform_filter = task.get("platform_filter")
        
        if not quiet:
            platform_text = f"（{platform_filter}平台）" if platform_filter else ""
            print(f"\n📊 處理 {person_name}{platform_text}...")
        
        try:
            # 📌 使用優化的 process_user_data 處理 gemini_format.json
            user_combined_messages = process_user_data(input_path, person_name, positive_party, negative_party, platform_filter)
            
            if not user_combined_messages:
                print(f"⚠️ {person_name} 沒有用戶數據，跳過")
                continue
            
            # 將用戶分批
            user_items = list(user_combined_messages.items())
            batches = [dict(user_items[i:i+batch_size]) for i in range(0, len(user_items), batch_size)]
            
            for batch_index, batch_data in enumerate(batches):
                all_batches.append({
                    'batch_index': batch_index,
                    'batch_data': batch_data,
                    'person_name': person_name,
                    'positive_party': positive_party,
                    'negative_party': negative_party
                })
                
                if not quiet:
                    print(f"  📦 批次 {batch_index}: {len(batch_data)} 位用戶")
            
            persons.append(person_name)
        except Exception as e:
            print(f"❌ 處理 {person_name} 時出錯: {e}")

    if not all_batches:
        print("❌ 沒有批次需要處理")
        return
    
    if not quiet:
        print(f"\n🚀 開始並行處理 {len(all_batches)} 個批次...")
    
    # 並行處理所有批次
    api_key_cycle = cycle(api_keys)
    
    with ThreadPoolExecutor(max_workers=min(len(api_keys), len(all_batches))) as executor:
        futures = []
        for batch_info in all_batches:
            api_key = next(api_key_cycle)
            future = executor.submit(
                run_batch,
                batch_info['batch_index'],
                batch_info['batch_data'],
                api_key,
                batch_info['person_name'],
                batch_info['positive_party'],
                batch_info['negative_party']
            )
            futures.append(future)
        
        # 等待所有批次完成
        for future in futures:
            try:
                future.result()
            except Exception as e:
                print(f"❌ 批次處理失敗: {e}")

    if not quiet:
        print(f"\n🎉 情感分析完成！")
        print(f"📂 結果已保存到 {FINAL_DATA_DIR} 目錄")

def analyze_legislators_emotions_incremental(legislators=None, batch_size=500, quiet=False):
    """
    增量分析立委的情感資料（只處理新資料）
    
    Args:
        legislators: 立委列表
        batch_size: 批次大小  
        quiet: 是否安靜模式
    """
    if not quiet:
        print("執行增量情感分析...")
    
    # 檢查是否已有分析結果
    processed_legislators = []
    if legislators:
        for legislator in legislators:
            result_file = os.path.join(FINAL_DATA_DIR, f"{legislator}_使用者分析.json")
            if not os.path.exists(result_file):
                processed_legislators.append(legislator)
            elif not quiet:
                print(f"跳過 {legislator}，已有分析結果")
    
    if processed_legislators:
        analyze_legislators_emotions(processed_legislators, batch_size, quiet)
    elif not quiet:
        print("所有立委都已有分析結果，跳過分析階段")

if __name__ == "__main__":
    # 運行完整的情感分析流程
    print("🚀 開始重新生成final_data...")
    
    # 使用analyze_legislators_emotions函數重新生成所有立委的數據
    analyze_legislators_emotions(batch_size=500, quiet=False, incremental=False)
    
    print("✅ 重新生成完成！")