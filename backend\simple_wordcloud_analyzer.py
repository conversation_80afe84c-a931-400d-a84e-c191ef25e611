"""
MongoDB更新分析器
專門處理立委數據的MongoDB更新，包含最近7天一天一個統計點、最近兩個禮拜兩天一個統計點等
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from collections import Counter, defaultdict
from pathlib import Path
import jieba
import re

# 導入增強詞雲處理器
from crawler.enhanced_wordcloud_processor import EnhancedWordCloudProcessor

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MongoUpdateAnalyzer:
    """MongoDB更新分析器"""
    
    def __init__(self):
        """初始化分析器"""
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 設置目錄
        self.base_dir = Path("crawler")
        self.final_data_dir = self.base_dir / "processed" / "final_data"
        self.analysis_dir = self.base_dir / "processed" / "analysis"
        self.analysis_dir.mkdir(exist_ok=True)
        
        # 初始化增強詞雲處理器
        self.wordcloud_processor = EnhancedWordCloudProcessor()
        
        # 情感分析關鍵詞
        self.positive_words = {
            'joy': ['高興', '開心', '快樂', '興奮', '歡喜', '愉悅', '滿意', '讚', '棒', '好', '優秀', '專業', '加油'],
            'trust': ['信任', '相信', '可靠', '誠實', '正直', '負責', '認真', '用心', '努力'],
            'anticipation': ['期待', '希望', '展望', '未來', '進步', '發展', '改善', '提升']
        }
        
        self.negative_words = {
            'anger': ['憤怒', '生氣', '火大', '不爽', '討厭', '恨', '垃圾', '爛', '差', '廢物', '無能'],
            'sadness': ['悲傷', '難過', '失望', '沮喪', '傷心', '痛苦', '絕望', '無奈'],
            'fear': ['恐懼', '害怕', '擔心', '憂慮', '不安', '緊張', '恐慌', '驚慌'],
            'disgust': ['噁心', '厭惡', '反感', '討厭', '嫌棄', '鄙視', '看不起'],
            'surprise': ['驚訝', '震驚', '意外', '沒想到', '不可思議', '難以置信']
        }
        
        # 政治相關停用詞
        self.political_stopwords = {
            '政黨相關': ['民進黨', '國民黨', '民眾黨', '時代力量', '基進黨', '綠黨', '藍營', '綠營', '白營'],
            '政治術語': ['立委', '議員', '市長', '總統', '行政院長', '部長', '局長', '官員', '政治人物'],
            '政策相關': ['法案', '政策', '預算', '質詢', '委員會', '院會', '黨團', '協商'],
            '選舉相關': ['選舉', '投票', '民調', '支持度', '當選', '落選', '初選', '大選'],
            '媒體相關': ['新聞', '報導', '記者', '媒體', '電視', '報紙', '網路', '社群'],
            '時間相關': ['今天', '昨天', '明天', '週一', '週二', '週三', '週四', '週五', '週六', '週日'],
            '地點相關': ['台北', '新北', '桃園', '台中', '台南', '高雄', '基隆', '新竹', '苗栗', '彰化', '南投', '雲林', '嘉義', '屏東', '宜蘭', '花蓮', '台東', '澎湖', '金門', '馬祖'],
            '網路用語': ['推', '噓', '爆', '熱門', '置頂', '精華', '刪文', '水桶', '版主', '管理員'],
            '表情符號': ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐', '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '💩', '👻', '💀', '☠️', '👽', '👾', '🤖', '😺', '😸', '😹', '😻', '😼', '😽', '🙀', '😿', '😾']
        }
        
        # 將所有停用詞合併到一個集合中
        for category, words in self.political_stopwords.items():
            self.stopwords.update(words)
    
    def _load_stopwords(self) -> set:
        """載入停用詞"""
        stopwords = {
            # 基本停用詞
            '的', '了', '是', '我', '也', '和', '就', '都', '不', '在', '會', '要', '很',
            '有', '這', '那', '個', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
            '他', '她', '它', '們', '你', '您', '我們', '你們', '他們', '她們',
            '但', '而', '或', '與', '及', '以', '為', '於', '從', '到', '由', '對',
            '可以', '可能', '應該', '必須', '需要', '想要', '希望', '覺得', '認為',
            '什麼', '怎麼', '為什麼', '哪裡', '什麼時候', '誰', '哪個', '多少',
            '這樣', '那樣', '如此', '這麼', '那麼', '非常', '特別', '尤其',
            '因為', '所以', '如果', '雖然', '但是', '然而', '不過', '而且', '另外',
            '首先', '其次', '最後', '總之', '因此', '所以', '然後', '接著',
            '比較', '相對', '絕對', '完全', '幾乎', '差不多', '大概', '可能',
            '真的', '確實', '當然', '其實', '實際', '基本', '主要', '重要',
            '一些', '一點', '一下', '一直', '一起', '一樣', '一定', '一般',
            '現在', '以前', '之前', '以後', '之後', '目前', '當時', '那時',
            '今天', '昨天', '明天', '今年', '去年', '明年', '這次', '上次', '下次',
            
            # 網路用語
            '哈哈', '呵呵', '嗯嗯', '喔喔', '哦哦', '嘿嘿', '嘻嘻',
            '讚', '推', '頂', '樓主', '原po', 'po', 'Po', 'PO',
            '回覆', '留言', '評論', '分享', '轉發', '按讚',
            '網友', '鄉民', '大家', '各位', '朋友們',
            '影片', '文章', '新聞', '報導', '消息', '資訊',
            '連結', '網址', '來源', '出處', '引用',
            '更新', '編輯', '修改', '刪除', '發布', '上傳',
            
            # 新聞媒體相關停用詞
            '標題', '留言', '內容', '錯票', '快訊', '新聞', '報導', '記者', '編輯', '主編', '總編', '社長', '總裁', 'CEO',
            '董事長', '總經理', '副總', '經理', '主管', '領導', '幹部', '官員', '議員', '立委',
            '市長', '縣長', '總統', '副總統', '院長', '部長', '局長', '處長', '科長',
            '主任', '副主任', '助理', '秘書', '顧問', '委員', '代表', '發言人', '新聞官', '公關',
            '幕僚', '智囊', '參謀', '軍師', '謀士', '策士', '謀略家', '戰略家', '戰術家', '兵法家',
            '軍事家', '將領', '統帥', '指揮官', '司令', '將軍', '元帥', '統帥', '領袖', '領導人',
            '領導者', '領袖', '首領', '首長', '首腦', '頭目', '頭頭', '老大', '大哥', '大姐',
            '前輩', '長輩', '前輩', '老師', '教授', '學者', '專家', '大師', '宗師', '泰斗',
            
            # 政治相關
            '立委', '議員', '市長', '縣長', '總統', '院長', '部長', '局長',
            '政府', '政治', '政策', '選舉', '投票', '民調', '支持', '反對',
            '黨', '政黨', '民進黨', '國民黨', '民眾黨', '時代力量',
            '立法院', '行政院', '監察院', '司法院', '考試院',
            '台灣', '中華民國', '中國', '大陸', '兩岸',
            '民眾', '人民', '國民', '市民', '鄉親', '同胞',
            
            # 無意義詞彙（新增）
            '不要', '自己', '出來', '這個', '那個', '這些', '那些',
            '什麼', '怎麼', '為什麼', '哪裡', '什麼時候', '誰', '哪個',
            '可以', '可能', '應該', '必須', '需要', '想要', '希望',
            '覺得', '認為', '知道', '看到', '聽到', '想到', '說到',
            '還是', '就是', '只是', '就是', '還是', '就是', '只是',
            '沒有', '不會', '不能', '不要', '不用', '不是', '不會',
            '一個', '兩個', '三個', '幾個', '很多', '一些', '一點',
            '真的', '確實', '當然', '其實', '實際', '基本', '主要',
            '現在', '以前', '之前', '以後', '之後', '目前', '當時',
            '今天', '昨天', '明天', '今年', '去年', '明年',
            '這樣', '那樣', '如此', '這麼', '那麼', '非常', '特別',
            '比較', '相對', '絕對', '完全', '幾乎', '差不多', '大概',
            '因為', '所以', '如果', '雖然', '但是', '然而', '不過',
            '首先', '其次', '最後', '總之', '因此', '所以', '然後',
            '而且', '另外', '或者', '以及', '還有', '並且', '同時'
        }
        return stopwords
    
    def analyze_legislator(self, legislator_name: str) -> Dict[str, Any]:
        """分析單個立委的數據"""
        try:
            # 檢查數據文件是否存在
            data_file = f"crawler/processed/final_data/{legislator_name}_使用者分析.json"
            if not os.path.exists(data_file):
                logger.warning(f"⚠️ 找不到數據文件: {data_file}")
                return None
            
            # 讀取數據
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not data:
                logger.warning(f"⚠️ {legislator_name} 數據為空")
                return None
            
            # 檢查數據格式 - final_data是列表格式
            if not isinstance(data, list):
                logger.warning(f"⚠️ {legislator_name} 數據格式不是列表")
                return None
            
            # 生成文字雲
            wordcloud = self._generate_wordcloud(data)
            
            # 生成情感統計
            sentiment_stats = self._generate_emotion_analysis(data)
            
            # 生成時間序列統計
            time_series_stats = self._generate_time_series_stats(data)
            
            # 生成情緒分析
            emotion_analysis = self._generate_emotion_analysis(data)
            
            # 生成總統計
            total_stats = self._get_total_stats(data)
            
            # 組合分析結果
            analysis_result = {
                'legislator_name': legislator_name,
                'wordcloud': wordcloud,
                'time_series_stats': time_series_stats,
                'emotion_analysis': emotion_analysis,
                'total_stats': total_stats,
                'analysis_date': datetime.now().isoformat()
            }
            
            logger.info(f"✅ {legislator_name} 分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ 分析 {legislator_name} 失敗: {e}")
            return None
    
    def _prepare_users_data(self, data: List[Dict]) -> List[Dict]:
        """準備用戶數據"""
        users_data = []
        
        for item in data:
            if isinstance(item, dict):
                user_data = {
                    'user_id': item.get('使用者', ''),
                    'emotion_label': item.get('情感標籤', 'NEUTRAL'),
                    'emotion': item.get('情緒', 'neutral'),
                    'comment_count': 1,
                    'content': item.get('整合留言內容', ''),
                    'platform': self._extract_platform_from_content(item.get('整合留言內容', ''))
                }
                users_data.append(user_data)
        
        return users_data
    
    def _extract_platform_from_content(self, content: str) -> str:
        """從內容中提取平台信息"""
        content_lower = content.lower()
        
        # 根據新的final_data格式提取平台
        if '平台：youtube' in content_lower or '平台：yt' in content_lower:
            return 'YouTube'
        elif '平台：ptt' in content_lower:
            return 'PTT'
        elif '平台：threads' in content_lower:
            return 'Threads'
        elif '平台：facebook' in content_lower or '平台：fb' in content_lower:
            return 'Facebook'
        elif '平台：twitter' in content_lower or '平台：x' in content_lower:
            return 'Twitter'
        elif '平台：instagram' in content_lower or '平台：ig' in content_lower:
            return 'Instagram'
        
        # 備用方案：根據內容特徵判斷平台
        if any(keyword in content_lower for keyword in ['@newsebc', '@中天新聞', '@中視新聞', '@華視新聞', '@三立新聞', '@tvbs', '@民視新聞']):
            return 'YouTube'
        elif any(keyword in content_lower for keyword in ['ptt', '批踢踢', '八卦版', '政黑版']):
            return 'PTT'
        elif any(keyword in content_lower for keyword in ['threads', 'thread']):
            return 'Threads'
        elif any(keyword in content_lower for keyword in ['facebook', 'fb', '臉書']):
            return 'Facebook'
        elif any(keyword in content_lower for keyword in ['twitter', 'x', '推特']):
            return 'Twitter'
        elif any(keyword in content_lower for keyword in ['instagram', 'ig']):
            return 'Instagram'
        elif any(keyword in content_lower for keyword in ['youtube', 'yt', '@']):
            # 檢查是否有YouTube特徵
            if '@' in content and any(keyword in content for keyword in ['新聞', '直播', '精華', '全程']):
                return 'YouTube'
            else:
                return 'PTT'  # 默認PTT而不是Unknown
        else:
            return 'PTT'  # 默認PTT而不是Unknown
    
    def _generate_wordcloud(self, data: Dict) -> List[Dict[str, Any]]:
        """生成文字雲 - 使用增強詞雲處理器"""
        all_text = ""
        
        # 處理列表格式的數據
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict):
                    # 提取留言內容
                    content = item.get('整合留言內容', '') or item.get('留言內容', '') or item.get('content', '')
                    if content:
                        # 解析留言內容格式
                        extracted_text = self._extract_text_from_content(content)
                        all_text += extracted_text + " "
        # 處理字典格式的數據（原有邏輯）
        elif isinstance(data, dict) and 'users' in data:
            for user in data['users']:
                if 'comments' in user:
                    for comment in user['comments']:
                        if 'content' in comment:
                            all_text += comment['content'] + " "
        
        if not all_text.strip():
            return []
        
        # 使用增強詞雲處理器生成詞雲
        return self.wordcloud_processor.extract_keywords(all_text, max_words=50)
    
    def _is_valid_word_for_wordcloud(self, word: str, flag: str) -> bool:
        """使用詞性標註判斷詞彙是否適合用於文字雲"""
        # 基本長度檢查
        if len(word) < 2:
            return False
        
        # 排除純數字
        if word.isdigit():
            return False
        
        # 排除單個英文字母
        if len(word) == 1 and word.isalpha():
            return False
        
        # 排除停用詞
        if word in self.stopwords:
            return False
        
        # 使用詞性標註進行過濾
        # 保留的詞性：
        # n: 名詞, nr: 人名, ns: 地名, nt: 機構名, nz: 其他專名
        # v: 動詞, vd: 副動詞, vn: 名動詞
        # a: 形容詞, ad: 副形詞, an: 名形詞
        # d: 副詞
        # 排除的詞性：
        # u: 助詞, p: 介詞, c: 連詞, e: 嘆詞, y: 語氣詞
        # r: 代詞, m: 數詞, q: 量詞, w: 標點符號
        
        valid_flags = {'n', 'nr', 'ns', 'nt', 'nz', 'v', 'vd', 'vn', 'a', 'ad', 'an', 'd'}
        exclude_flags = {'u', 'p', 'c', 'e', 'y', 'r', 'm', 'q', 'w'}
        
        # 檢查詞性
        if flag in exclude_flags:
            return False
        
        # 如果是名詞、動詞、形容詞等，進一步檢查
        if flag in valid_flags:
            # 排除一些常見的無意義詞彙
            meaningless_words = {
                '不要', '自己', '出來', '這個', '那個', '這些', '那些',
                '什麼', '怎麼', '為什麼', '哪裡', '什麼時候', '誰', '哪個',
                '可以', '可能', '應該', '必須', '需要', '想要', '希望',
                '覺得', '認為', '知道', '看到', '聽到', '想到', '說到',
                '還是', '就是', '只是', '沒有', '不會', '不能', '不用', '不是',
                '一個', '兩個', '三個', '幾個', '很多', '一些', '一點',
                '真的', '確實', '當然', '其實', '實際', '基本', '主要',
                '現在', '以前', '之前', '以後', '之後', '目前', '當時',
                '今天', '昨天', '明天', '今年', '去年', '明年',
                '這樣', '那樣', '如此', '這麼', '那麼', '非常', '特別',
                '比較', '相對', '絕對', '完全', '幾乎', '差不多', '大概',
                '因為', '所以', '如果', '雖然', '但是', '然而', '不過',
                '首先', '其次', '最後', '總之', '因此', '所以', '然後',
                '而且', '另外', '或者', '以及', '還有', '並且', '同時'
            }
            
            if word in meaningless_words:
                return False
            
            return True
        
        return False
    
    def _extract_text_from_content(self, content: str) -> str:
        """從留言內容中提取實際文本"""
        if not content:
            return ""
        
        # 如果內容包含"留言內容："，提取後面的實際文本
        if "留言內容：" in content:
            parts = content.split("留言內容：")
            if len(parts) > 1:
                # 取最後一個"留言內容："後面的內容
                actual_content = parts[-1].strip()
                # 移除可能的平台、標題等標記
                lines = actual_content.split('\n')
                text_lines = []
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('平台：') and not line.startswith('標題：'):
                        text_lines.append(line)
                return ' '.join(text_lines)
        
        # 如果沒有"留言內容："標記，直接返回內容
        return content
    
    def _is_meaningless_word(self, word: str) -> bool:
        """檢查是否為無意義詞彙"""
        meaningless_words = {
            '不要', '自己', '出來', '這個', '那個', '這些', '那些',
            '什麼', '怎麼', '為什麼', '哪裡', '什麼時候', '誰', '哪個',
            '可以', '可能', '應該', '必須', '需要', '想要', '希望',
            '覺得', '認為', '知道', '看到', '聽到', '想到', '說到',
            '還是', '就是', '只是', '沒有', '不會', '不能', '不用', '不是',
            '一個', '兩個', '三個', '幾個', '很多', '一些', '一點',
            '真的', '確實', '當然', '其實', '實際', '基本', '主要',
            '現在', '以前', '之前', '以後', '之後', '目前', '當時',
            '今天', '昨天', '明天', '今年', '去年', '明年',
            '這樣', '那樣', '如此', '這麼', '那麼', '非常', '特別',
            '比較', '相對', '絕對', '完全', '幾乎', '差不多', '大概',
            '因為', '所以', '如果', '雖然', '但是', '然而', '不過',
            '首先', '其次', '最後', '總之', '因此', '所以', '然後',
            '而且', '另外', '或者', '以及', '還有', '並且', '同時'
        }
        return word in meaningless_words
    
    def _generate_time_series_stats(self, data: Dict) -> Dict[str, Any]:
        """生成時間序列統計 - 最近7天一天一個統計點、最近兩個禮拜兩天一個統計點等"""
        try:
            if not self._check_date_info(data):
                logger.warning("⚠️ 數據中缺少日期信息，無法生成時間序列統計")
                return {}
            
            # 獲取所有日期
            all_dates = []
            for item in data:
                if isinstance(item, dict):
                    # 優先使用日期字段
                    date_str = item.get('日期', '')
                    if date_str:
                        try:
                            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                            all_dates.append(date_obj)
                        except ValueError:
                            continue
                    else:
                        # 從內容中提取日期
                        content = item.get('整合留言內容', '')
                        date_obj = self._extract_date_from_content(content)
                        if date_obj:
                            all_dates.append(date_obj)
            
            if not all_dates:
                logger.warning("⚠️ 無法解析日期，跳過時間序列統計")
                return {}
            
            # 排序日期
            all_dates.sort()
            start_date = min(all_dates)
            end_date = max(all_dates)
            
            # 定義統計時間段 - 對應前端需求
            time_periods = {
                'recent_7_days': {
                    'days': 7,
                    'interval': 1,  # 一天一個統計點
                    'description': '最近7天 (一天一個統計點)'
                },
                'recent_14_days': {
                    'days': 14,
                    'interval': 2,  # 兩天一個統計點
                    'description': '最近2週 (兩天一個統計點)'
                },
                'recent_30_days': {
                    'days': 30,
                    'interval': 3,  # 三天一個統計點
                    'description': '最近1個月 (三天一個統計點)'
                },
                'recent_90_days': {
                    'days': 90,
                    'interval': 7,  # 一週一個統計點
                    'description': '最近3個月 (一週一個統計點)'
                },
                'recent_180_days': {
                    'days': 180,
                    'interval': 14,  # 兩週一個統計點
                    'description': '最近6個月 (兩週一個統計點)'
                },
                'recent_365_days': {
                    'days': 365,
                    'interval': 30,  # 一個月一個統計點
                    'description': '最近1年 (一個月一個統計點)'
                }
            }
            
            stats = {}
            current_date = datetime.now()
            
            for period_name, period_config in time_periods.items():
                period_stats = self._calculate_period_stats(
                    data, current_date, period_config['days'], period_config['interval']
                )
                stats[period_name] = {
                    'description': period_config['description'],
                    'data': period_stats,
                    'total_points': len(period_stats),
                    'total_comments': sum(point.get('comment_count', 0) for point in period_stats)
                }
            
            # 添加總體統計
            stats['overall'] = {
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d'),
                'total_days': (end_date - start_date).days + 1,
                'total_comments': len(data)
            }
            
            logger.info(f"✅ 時間序列統計生成完成: {len(stats)} 個時間段")
            return stats
            
        except Exception as e:
            logger.error(f"❌ 生成時間序列統計失敗: {e}")
            return {}
    
    def _calculate_period_stats(self, data: Dict, current_date: datetime, days: int, interval: int) -> List[Dict]:
        """計算特定時間段的統計數據"""
        try:
            # 修正：使用昨天作為基準點，因為每天凌晨抓取前一天資料
            yesterday = datetime.now() - timedelta(days=1)
            
            # 對於最近7天、14天、30天，使用昨天作為基準點
            if days <= 30:  # 對於最近7天、14天、30天，使用昨天作為基準
                period_start = yesterday - timedelta(days=days-1)  # 從昨天往前推days-1天
                period_end = yesterday  # 到昨天為止
            else:  # 對於更長的時間段，使用數據中的日期範圍
                period_start = current_date - timedelta(days=days)
                period_end = current_date
            
            # 按間隔生成統計點
            stats_points = []
            current_point = period_start
            
            while current_point <= period_end:
                # 計算該時間點的統計數據
                point_stats = self._calculate_point_stats(data, current_point, interval)
                
                if point_stats:
                    stats_points.append({
                        'date': current_point.strftime('%Y-%m-%d'),
                        'emotion_counts': point_stats['emotion_counts'],
                        'emotion_ratio': point_stats['emotion_ratio'],
                        'platform_stats': point_stats['platform_stats'],
                        'comment_count': point_stats['comment_count']
                    })
                
                # 移動到下一個時間點
                current_point += timedelta(days=interval)
            
            # 按日期排序
            stats_points.sort(key=lambda x: x['date'])
            
            return stats_points
            
        except Exception as e:
            logger.error(f"❌ 計算時間段統計失敗: {e}")
            return []
    
    def _calculate_point_stats(self, data: Dict, point_date: datetime, interval: int) -> Dict:
        """計算單個時間點的統計數據"""
        try:
            # 計算時間範圍
            start_date = point_date
            end_date = point_date + timedelta(days=interval)
                
            # 初始化統計
            emotion_counts = {
                'joy': 0, 'anger': 0, 'sadness': 0, 'fear': 0,
                'surprise': 0, 'disgust': 0, 'trust': 0, 'anticipation': 0, 'neutral': 0
                }
            platform_stats = {}
            comment_count = 0
            
            # 統計該時間範圍內的數據
            for item in data:
                if isinstance(item, dict):
                    # 檢查日期
                    item_date = self._get_item_date(item)
                    if item_date and start_date <= item_date < end_date:
                        comment_count += 1
                        
                        # 統計情緒
                        emotion = item.get('情緒', '').lower()
                        if emotion in emotion_counts:
                            emotion_counts[emotion] += 1
                        else:
                            emotion_counts['neutral'] += 1
                
                        # 統計平台
                        platform = item.get('平台', 'Unknown')
                    platform_stats[platform] = platform_stats.get(platform, 0) + 1
                
            # 計算情緒比例
            total_emotions = sum(emotion_counts.values())
            emotion_ratio = {}
            if total_emotions > 0:
                for emotion, count in emotion_counts.items():
                    emotion_ratio[emotion] = count / total_emotions
            
            return {
                    'emotion_counts': emotion_counts,
                'emotion_ratio': emotion_ratio,
                    'platform_stats': platform_stats,
                'comment_count': comment_count
            }
            
        except Exception as e:
            logger.error(f"❌ 計算時間點統計失敗: {e}")
            return {}
    
    def _get_item_date(self, item: Dict) -> Optional[datetime]:
        """從數據項中提取日期"""
        try:
            # 優先使用日期字段
            date_str = item.get('日期', '')
            if date_str:
                return datetime.strptime(date_str, '%Y-%m-%d')
                
            # 從時間字段提取
            time_str = item.get('時間', '')
            if time_str:
                return self._parse_time(time_str)
            
            return None
        except Exception:
            return None
    
    def _check_date_info(self, data: Dict) -> bool:
        """檢查數據中是否有日期信息"""
        try:
            for item in data:
                if isinstance(item, dict):
                    # 檢查是否有日期字段
                    if '日期' in item:
                        return True
                    # 檢查標題中是否有日期信息
                    content = item.get('整合留言內容', '')
                    if self._extract_date_from_content(content):
                        return True
            return False
        except Exception as e:
            logger.error(f"❌ 檢查日期信息失敗: {e}")
            return False
    
    def _extract_date_from_content(self, content: str) -> Optional[datetime]:
        """從內容中提取日期信息"""
        try:
            if not content:
                return None
                
            # 尋找標題中的日期格式
            # 1. 匹配 YYYY.MM.DD 格式 (如 2025.07.01)
            date_pattern1 = r'(\d{4})\.(\d{1,2})\.(\d{1,2})'
            match1 = re.search(date_pattern1, content)
            if match1:
                year, month, day = match1.groups()
                return datetime(int(year), int(month), int(day))
            
            # 2. 匹配 YYYY-MM-DD 格式
            date_pattern2 = r'(\d{4})-(\d{1,2})-(\d{1,2})'
            match2 = re.search(date_pattern2, content)
            if match2:
                year, month, day = match2.groups()
                return datetime(int(year), int(month), int(day))
            
            # 3. 匹配 YYYY/MM/DD 格式
            date_pattern3 = r'(\d{4})/(\d{1,2})/(\d{1,2})'
            match3 = re.search(date_pattern3, content)
            if match3:
                year, month, day = match3.groups()
                return datetime(int(year), int(month), int(day))
            
            # 4. 匹配 MM/DD/YYYY 格式
            date_pattern4 = r'(\d{1,2})/(\d{1,2})/(\d{4})'
            match4 = re.search(date_pattern4, content)
            if match4:
                month, day, year = match4.groups()
                return datetime(int(year), int(month), int(day))
            
            # 5. 匹配中文日期格式 (如 2025年7月1日)
            date_pattern5 = r'(\d{4})年(\d{1,2})月(\d{1,2})日?'
            match5 = re.search(date_pattern5, content)
            if match5:
                year, month, day = match5.groups()
                return datetime(int(year), int(month), int(day))
            
            # 6. 匹配簡化中文日期格式 (如 7月1日)
            current_year = datetime.now().year
            date_pattern6 = r'(\d{1,2})月(\d{1,2})日?'
            match6 = re.search(date_pattern6, content)
            if match6:
                month, day = match6.groups()
                return datetime(current_year, int(month), int(day))
            
            return None
        except Exception as e:
            logger.error(f"❌ 提取日期失敗: {e}")
            return None
    
    def _get_total_stats(self, data: Dict) -> Dict[str, Any]:
        """獲取總統計數據（當沒有日期信息時使用）- 使用八大情緒分類"""
        try:
            total_comments = len(data)
            
            # 八大情緒統計
            emotion_counts = {
                'joy': 0,
                'trust': 0,
                'anticipation': 0,
                'sadness': 0,
                'surprise': 0,
                'disgust': 0,
                'fear': 0,
                'anger': 0,
                'neutral': 0
            }
            
            for item in data:
                if isinstance(item, dict):
                    emotion_value = item.get('情緒', '').lower()
                    if emotion_value in emotion_counts:
                        emotion_counts[emotion_value] += 1
                    else:
                        # 如果沒有情緒值，根據情感標籤推斷
                        sentiment_label = item.get('情感標籤', '').upper()
                        if sentiment_label in ['POSITIVE', '正面', '積極']:
                            emotion_counts['joy'] += 1
                        elif sentiment_label in ['NEGATIVE', '負面', '消極']:
                            emotion_counts['sadness'] += 1
                        else:
                            emotion_counts['neutral'] += 1
            
            # 計算最近7天的評論數 - 使用昨天作為基準點
            recent_7_days_count = 0
            yesterday = datetime.now() - timedelta(days=1)
            seven_days_ago = yesterday - timedelta(days=6)  # 從昨天往前推6天
            
            for item in data:
                if isinstance(item, dict):
                    # 優先使用日期字段
                    date_str = item.get('日期', '')
                    comment_date = None
                    
                    if date_str:
                        try:
                            comment_date = datetime.strptime(date_str, '%Y-%m-%d')
                        except ValueError:
                            continue
                    else:
                        # 從內容中提取日期
                        content = item.get('整合留言內容', '')
                        comment_date = self._extract_date_from_content(content)
                    
                    if comment_date and seven_days_ago <= comment_date <= yesterday:
                        recent_7_days_count += 1
            
            stats = {
                'total_comments': total_comments,
                'emotion_counts': emotion_counts,
                'recent_7_days': recent_7_days_count,
                'emotion_ratio': {
                    emotion: count / total_comments if total_comments > 0 else 0
                    for emotion, count in emotion_counts.items()
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ 計算總統計失敗: {e}")
            return {
                'total_comments': 0,
                'emotion_counts': {
                    'joy': 0, 'trust': 0, 'anticipation': 0, 'sadness': 0,
                    'surprise': 0, 'disgust': 0, 'fear': 0, 'anger': 0, 'neutral': 0
                },
                'recent_7_days': 0,
                'emotion_ratio': {
                    'joy': 0, 'trust': 0, 'anticipation': 0, 'sadness': 0,
                    'surprise': 0, 'disgust': 0, 'fear': 0, 'anger': 0, 'neutral': 0
                }
            }
    
    def _get_date_range_stats(self, data: Dict, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """獲取指定日期範圍的統計 - 使用八大情緒分類"""
        # 八大情緒統計
        emotion_counts = {
            'joy': 0,
            'trust': 0,
            'anticipation': 0,
            'sadness': 0,
            'surprise': 0,
            'disgust': 0,
            'fear': 0,
            'anger': 0,
            'neutral': 0
        }
        
        stats = {
            'total_comments': 0,
            'emotion_counts': emotion_counts,
            'emotion_ratio': {},
            'platforms': defaultdict(int)
        }
        
        # 處理列表格式的數據
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict):
                    # 嘗試獲取日期
                    date_str = item.get('日期', '') or item.get('date', '') or item.get('時間', '')
                    if date_str:
                        try:
                            # 嘗試解析日期
                            if 'T' in date_str:
                                comment_date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                            else:
                                # 嘗試其他日期格式
                                comment_date = datetime.strptime(date_str, '%Y-%m-%d')
                            
                            if start_date <= comment_date <= end_date:
                                stats['total_comments'] += 1
                                
                                # 八大情緒分析
                                emotion_value = item.get('情緒', '').lower()
                                if emotion_value in emotion_counts:
                                    emotion_counts[emotion_value] += 1
                                else:
                                    # 如果沒有情緒值，根據情感標籤推斷
                                    emotion_label = item.get('情感標籤', '').upper()
                                    if emotion_label in ['POSITIVE', '正面', '積極']:
                                        emotion_counts['joy'] += 1
                                    elif emotion_label in ['NEGATIVE', '負面', '消極']:
                                        emotion_counts['sadness'] += 1
                                    else:
                                        # 分析文本內容
                                        content = item.get('整合留言內容', '') or item.get('留言內容', '') or item.get('content', '')
                                        if content:
                                            extracted_text = self._extract_text_from_content(content)
                                            if extracted_text.strip():
                                                emotion = self._analyze_emotion(extracted_text)
                                                emotion_counts[emotion] += 1
                                            else:
                                                emotion_counts['neutral'] += 1
                                        else:
                                            emotion_counts['neutral'] += 1
                                
                                # 平台統計
                                platform = item.get('平台', '') or item.get('platform', 'unknown')
                                stats['platforms'][platform] += 1
                                
                        except Exception as e:
                            logger.warning(f"日期解析失敗: {e}")
                    else:
                        # 如果沒有日期，仍然統計
                        stats['total_comments'] += 1
                        emotion_value = item.get('情緒', '').lower()
                        if emotion_value in emotion_counts:
                            emotion_counts[emotion_value] += 1
                        else:
                            emotion_label = item.get('情感標籤', '').upper()
                            if emotion_label in ['POSITIVE', '正面', '積極']:
                                emotion_counts['joy'] += 1
                            elif emotion_label in ['NEGATIVE', '負面', '消極']:
                                emotion_counts['sadness'] += 1
                            else:
                                content = item.get('整合留言內容', '') or item.get('留言內容', '') or item.get('content', '')
                                if content:
                                    extracted_text = self._extract_text_from_content(content)
                                    if extracted_text.strip():
                                        emotion = self._analyze_emotion(extracted_text)
                                        emotion_counts[emotion] += 1
                                    else:
                                        emotion_counts['neutral'] += 1
                                else:
                                    emotion_counts['neutral'] += 1
                        
                        platform = item.get('平台', '') or item.get('platform', 'unknown')
                        stats['platforms'][platform] += 1
        
        # 處理字典格式的數據（原有邏輯）
        elif isinstance(data, dict) and 'users' in data:
            for user in data['users']:
                if 'comments' in user:
                    for comment in user['comments']:
                        if 'content' in comment and 'date' in comment:
                            try:
                                comment_date = datetime.fromisoformat(comment['date'].replace('Z', '+00:00'))
                                if start_date <= comment_date <= end_date:
                                    stats['total_comments'] += 1
                                    
                                    emotion = self._analyze_emotion(comment['content'])
                                    emotion_counts[emotion] += 1
                                    
                                    platform = comment.get('platform', 'unknown')
                                    stats['platforms'][platform] += 1
                                    
                            except Exception as e:
                                logger.warning(f"日期解析失敗: {e}")
        
        # 計算情緒比例
        total = stats['total_comments']
        if total > 0:
            stats['emotion_ratio'] = {
                emotion: round(count / total * 100, 2)
                for emotion, count in emotion_counts.items()
            }
        else:
            stats['emotion_ratio'] = {emotion: 0 for emotion in emotion_counts.keys()}
        
        return stats
    
    def _generate_emotion_analysis(self, data: Dict) -> Dict[str, Any]:
        """生成情感分析 - 使用八大情緒分類"""
        # 八大情緒統計
        emotion_stats = {
            'joy': 0,
            'trust': 0,
            'anticipation': 0,
            'sadness': 0,
            'surprise': 0,
            'disgust': 0,
            'fear': 0,
            'anger': 0,
            'neutral': 0
        }
        
        # 處理列表格式的數據
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict):
                    # 檢查是否有預設的情感標籤
                    emotion_label = item.get('情感標籤', '').upper()
                    emotion_value = item.get('情緒', '').lower()
                    
                    # 優先使用已分析的情緒結果
                    if emotion_value in emotion_stats:
                        emotion_stats[emotion_value] += 1
                    elif emotion_label in ['POSITIVE', '正面', '積極']:
                        # 正面情緒預設為joy
                        emotion_stats['joy'] += 1
                    elif emotion_label in ['NEGATIVE', '負面', '消極']:
                        # 負面情緒預設為sadness
                        emotion_stats['sadness'] += 1
                    else:
                        # 如果沒有預設標籤，分析文本內容
                        content = item.get('整合留言內容', '') or item.get('留言內容', '') or item.get('content', '')
                        if content:
                            extracted_text = self._extract_text_from_content(content)
                            if extracted_text.strip():
                                emotion = self._analyze_emotion(extracted_text)
                                emotion_stats[emotion] += 1
                            else:
                                emotion_stats['neutral'] += 1
                        else:
                            emotion_stats['neutral'] += 1
        # 處理字典格式的數據（原有邏輯）
        elif isinstance(data, dict) and 'users' in data:
            for user in data['users']:
                if 'comments' in user:
                    for comment in user['comments']:
                        if 'content' in comment:
                            emotion = self._analyze_emotion(comment['content'])
                            emotion_stats[emotion] += 1
        
        total = sum(emotion_stats.values())
        if total > 0:
            emotion_ratio = {
                emotion: round(count / total * 100, 2)
                for emotion, count in emotion_stats.items()
            }
        else:
            emotion_ratio = {emotion: 0 for emotion in emotion_stats.keys()}
        
        return {
            'stats': emotion_stats,
            'ratio': emotion_ratio
        }
    
    def _analyze_emotion(self, text: str) -> str:
        """分析文本情緒 - 使用八大情緒分類"""
        if not text:
            return 'neutral'
        
        # 八大情緒關鍵詞匹配
        emotion_scores = {
            'joy': 0,
            'trust': 0, 
            'anticipation': 0,
            'sadness': 0,
            'surprise': 0,
            'disgust': 0,
            'fear': 0,
            'anger': 0
        }
        
        # 計算各情緒分數
        for emotion, keywords in self.positive_words.items():
            for keyword in keywords:
                if keyword in text:
                    emotion_scores[emotion] += 1
        
        for emotion, keywords in self.negative_words.items():
            for keyword in keywords:
                if keyword in text:
                    emotion_scores[emotion] += 1
        
        # 找出最高分數的情緒
        max_score = max(emotion_scores.values())
        if max_score == 0:
            return 'neutral'
        
        # 返回最高分數的情緒
        for emotion, score in emotion_scores.items():
            if score == max_score:
                return emotion
        
        return 'neutral'
    
    def update_mongodb(self, analysis_result: Dict[str, Any]) -> bool:
        """更新MongoDB - 先本地後遠端"""
        try:
            from pymongo import MongoClient
            from config import get_mongo_config
            
            # 獲取MongoDB配置
            try:
                mongo_config = get_mongo_config()
                mongodb_uri = mongo_config['MONGODB_URI']
                database_name = mongo_config['MONGODB_DBNAME']
                mongo_options = mongo_config['MONGODB_OPTIONS']
            except Exception as e:
                logger.warning(f"❌ 無法獲取MongoDB配置: {e}")
                return False
            
            if not mongodb_uri:
                logger.warning("❌ 未設置MongoDB URI，跳過數據庫更新")
                return False
            
            # 1. 先更新本地MongoDB
            local_success = self._update_local_mongodb(analysis_result)
            
            # 2. 再更新遠端Atlas（如果配置有效）
            atlas_success = False
            if 'mongodb+srv://' in mongodb_uri and '<db_password>' not in mongodb_uri:
                atlas_success = self._update_atlas_mongodb(analysis_result, mongodb_uri, database_name, mongo_options)
            
            return local_success or atlas_success
                
        except Exception as e:
            logger.error(f"❌ MongoDB更新失敗: {e}")
            return False
    
    def _update_local_mongodb(self, analysis_result: Dict[str, Any]) -> bool:
        """更新本地MongoDB"""
        try:
            from pymongo import MongoClient
            
            # 本地MongoDB連接
            client = MongoClient('mongodb://localhost:27017')
            db = client['legislator_recall']
            
            # 1. 更新 crawler_data collection
            crawler_collection = db.crawler_data
            self._update_crawler_data(crawler_collection, analysis_result)
            
            # 2. 更新 legislators collection
            legislators_collection = db.legislators
            self._update_legislators_data(legislators_collection, analysis_result)
            
            client.close()
            logger.info(f"✅ {analysis_result['legislator_name']} 本地MongoDB更新完成")
            return True
                
        except Exception as e:
            logger.warning(f"⚠️ 本地MongoDB更新失敗: {e}")
            return False
    
    def _update_atlas_mongodb(self, analysis_result: Dict[str, Any], uri: str, db_name: str, options: dict) -> bool:
        """更新遠端Atlas MongoDB"""
        try:
            from pymongo import MongoClient
            
            # 連接Atlas
            client = MongoClient(uri, **options)
            db = client[db_name]
            
            # 1. 不更新 crawler_data collection 怕容量爆掉 
            #crawler_collection = db.crawler_data
            #self._update_crawler_data(crawler_collection, analysis_result)
            
            # 2. 更新 legislators collection
            legislators_collection = db.legislators
            self._update_legislators_data(legislators_collection, analysis_result)
            
            client.close()
            logger.info(f"✅ {analysis_result['legislator_name']} Atlas MongoDB更新完成")
            return True
                
        except Exception as e:
            logger.warning(f"⚠️ Atlas MongoDB更新失敗: {e}")
            return False
    
    def _update_crawler_data(self, collection, analysis_result: Dict[str, Any]):
        """更新 crawler_data collection - 增量更新用戶分析數據"""
        try:
            legislator_name = analysis_result['legislator_name']
            
            # 獲取現有用戶列表
            existing_users = set()
            cursor = collection.find(
                {'legislator_name': legislator_name},
                {'user': 1}
            )
            for doc in cursor:
                existing_users.add(doc.get('user', ''))
            
            # 準備新用戶數據
            new_users = []
            users_data = analysis_result.get('users', [])
            
            # 確保users_data是列表格式
            if isinstance(users_data, list):
                for user_data in users_data:
                    if isinstance(user_data, dict):
                        user_id = user_data.get('user_id', '')
                        if user_id and user_id not in existing_users:
                            # 從用戶數據中提取平台信息
                            platform = self._extract_platform_from_user_data(user_data)
                            
                            # 準備用戶記錄
                            user_record = {
                                'legislator_name': legislator_name,
                                'user': user_id,
                                'emotion_label': user_data.get('emotion_label', 'NEUTRAL'),
                                'emotion': user_data.get('emotion', 'neutral'),
                                'platform': platform,
                                'comment_count': user_data.get('comment_count', 1),
                                'content': user_data.get('content', ''),
                                'analysis_date': datetime.now(),
                                'data_source': 'simple_wordcloud_analysis'
                            }
                            new_users.append(user_record)
            
            # 批量插入新用戶
            if new_users:
                collection.insert_many(new_users)
                logger.info(f"✅ crawler_data增量更新: 新增 {len(new_users)} 條用戶記錄")
            else:
                logger.info(f"✅ crawler_data: 無新用戶數據需要更新")
                
        except Exception as e:
            logger.error(f"❌ 更新 crawler_data 失敗: {e}")
            raise
    
    def _extract_platform_from_user_data(self, user_data: Dict[str, Any]) -> str:
        """從用戶數據中提取平台信息"""
        try:
            # 從內容中提取平台信息
            content = user_data.get('content', '')
            
            # 檢查新的final_data格式中的平台標識
            if '平台：PTT' in content or '平台：ptt' in content:
                return 'PTT'
            elif '平台：YouTube' in content or '平台：youtube' in content or '平台：yt' in content:
                return 'YouTube'
            elif '平台：Threads' in content or '平台：threads' in content:
                return 'Threads'
            elif '平台：Facebook' in content or '平台：facebook' in content or '平台：fb' in content:
                return 'Facebook'
            elif '平台：Twitter' in content or '平台：twitter' in content or '平台：x' in content:
                return 'Twitter'
            elif '平台：Instagram' in content or '平台：instagram' in content or '平台：ig' in content:
                return 'Instagram'
            else:
                # 如果無法從內容判斷，嘗試從其他字段判斷
                if 'platform' in user_data:
                    return user_data['platform']
                elif 'source' in user_data:
                    return user_data['source']
                else:
                    # 最後的備用方案：根據數據結構推測
                    return 'PTT'  # 默認PTT而不是Unknown
                    
        except Exception as e:
            logger.error(f"❌ 提取平台信息失敗: {e}")
            return 'PTT'  # 默認PTT而不是Unknown
    
    def _update_legislators_data(self, collection, analysis_result: Dict[str, Any]):
        """更新 legislators collection - 使用 upsert"""
        try:
            legislator_name = analysis_result['legislator_name']
            
            # 準備更新數據
            update_data = {
                'legislator_name': legislator_name,
                'wordcloud_data': analysis_result.get('wordcloud', []),
                'time_series_stats': analysis_result.get('time_series_stats', {}),
                'emotion_analysis': analysis_result.get('emotion_analysis', {}),
                'total_stats': analysis_result.get('total_stats', {}),
                'last_updated': datetime.now(),
                'analysis_date': datetime.now()
            }
            
            # 計算罷免支持/反對數據
            support_count, oppose_count = self._calculate_recall_support(analysis_result)
            update_data['recall_support'] = support_count
            update_data['recall_oppose'] = oppose_count
            
            # 使用 upsert 操作
            result = collection.update_one(
                {'legislator_name': legislator_name},
                {'$set': update_data},
                upsert=True
            )
            
            if result.upserted_id:
                logger.info(f"✅ legislators新增: {legislator_name}")
            else:
                logger.info(f"✅ legislators更新: {legislator_name}")
            
            # 記錄統計信息
            total_comments = analysis_result.get('total_stats', {}).get('total_comments', 0)
            recent_7_days = analysis_result.get('total_stats', {}).get('recent_7_days', 0)
            logger.info(f"📊 支持{support_count}人，反對{oppose_count}人")
            logger.info(f"📊 總評論數: {total_comments}, 最近7天: {recent_7_days} 條評論")
            
        except Exception as e:
            logger.error(f"❌ 更新 legislators 數據失敗: {e}")
            raise
    
    def _calculate_recall_support(self, analysis_result: Dict[str, Any]) -> tuple:
        """從final數據中讀取罷免支持度（已由Gemini計算好）"""
        try:
            legislator_name = analysis_result['legislator_name']
            data_file = f"crawler/processed/final_data/{legislator_name}_使用者分析.json"
            
            if not os.path.exists(data_file):
                return 0, 0
            
            # 讀取用戶分析數據
            with open(data_file, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
            
            support_count = 0
            oppose_count = 0
            
            # 直接使用Gemini已經分析好的情感標籤
            for item in user_data:
                if isinstance(item, dict):
                    emotion_label = item.get('情感標籤', '').upper()
                    if emotion_label == 'POSITIVE':
                        support_count += 1
                    elif emotion_label == 'NEGATIVE':
                        oppose_count += 1
            
            return support_count, oppose_count
            
        except Exception as e:
            logger.error(f"❌ 讀取罷免支持度失敗: {e}")
            return 0, 0
    
    def get_legislators_with_gaps(self) -> List[str]:
        """從data_gap_analysis.json獲取需要重新抓取的立委"""
        gap_file = Path("data_gap_analysis.json")
        if not gap_file.exists():
            logger.warning("❌ data_gap_analysis.json 不存在")
            return []
        
        try:
            with open(gap_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            legislators_with_gaps = []
            for legislator_name, legislator_data in data['legislators'].items():
                if legislator_data['summary']['needs_rerun']:
                    legislators_with_gaps.append(legislator_name)
            
            logger.info(f"📊 找到 {len(legislators_with_gaps)} 位需要重新抓取的立委")
            return legislators_with_gaps
            
        except Exception as e:
            logger.error(f"❌ 讀取data_gap_analysis.json失敗: {e}")
            return []
    
    def analyze_all_legislators(self, legislators: List[str] = None) -> Dict[str, Any]:
        """分析所有立委數據"""
        if not legislators:
            # 自動檢測有數據的立委
            legislators = []
            for file_path in self.final_data_dir.glob("*_使用者分析.json"):
                legislator_name = file_path.stem.replace("_使用者分析", "")
                legislators.append(legislator_name)
        
        logger.info(f"開始分析 {len(legislators)} 位立委的數據...")
        
        results = {}
        success_count = 0
        
        for legislator in legislators:
            try:
                analysis_result = self.analyze_legislator(legislator)
                if analysis_result:
                    # 更新MongoDB
                    self.update_mongodb(analysis_result)
                    results[legislator] = analysis_result
                    success_count += 1
                else:
                    logger.warning(f"⚠️ {legislator} 分析結果為空")
                    
            except Exception as e:
                logger.error(f"❌ {legislator} 分析失敗: {e}")
        
        logger.info(f"🎉 分析完成: 成功 {success_count}/{len(legislators)} 位立委")
        return results

def analyze_all_legislators_parallel(legislators: List[str] = None, max_workers: int = 4) -> Dict[str, Any]:
    """
    並行分析所有立委數據
    
    Args:
        legislators: 立委列表，如果為None則自動檢測
        max_workers: 最大並行執行緒數
        
    Returns:
        分析結果統計
    """
    from concurrent.futures import ThreadPoolExecutor, as_completed
    
    # 獲取立委列表
    if legislators is None:
        final_data_dir = Path("crawler/processed/final_data")
        legislators = []
        if final_data_dir.exists():
            for file in final_data_dir.glob("*_使用者分析.json"):
                name = file.name.replace('_使用者分析.json', '')
                legislators.append(name)
    
    if not legislators:
        logger.error("❌ 沒有找到任何final_data文件")
        return {}
    
    logger.info(f"🚀 開始並行分析 {len(legislators)} 位立委...")
    logger.info(f"📊 使用並行處理，最大並行數: {max_workers}")
    
    success_count = 0
    failed_legislators = []
    results = {}
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任務
        future_to_legislator = {
            executor.submit(analyze_single_legislator_parallel, legislator): legislator 
            for legislator in legislators
        }
        
        # 處理完成的任務
        for future in as_completed(future_to_legislator):
            legislator_name, success, result = future.result()
            
            if success:
                success_count += 1
                logger.info(f"✅ {legislator_name} 分析完成")
            else:
                failed_legislators.append(legislator_name)
                logger.error(f"❌ {legislator_name} 分析失敗: {result}")
            
            results[legislator_name] = result
    
    # 顯示最終結果
    logger.info(f"🎉 完成！成功分析 {success_count}/{len(legislators)} 位立委")
    
    if failed_legislators:
        logger.warning(f"⚠️ 以下立委分析失敗: {failed_legislators}")
    
    return {
        'success_count': success_count,
        'failed_count': len(failed_legislators),
        'failed_legislators': failed_legislators,
        'results': results
    }

def analyze_single_legislator_parallel(legislator_name: str) -> tuple:
    """
    並行分析單個立委數據
    
    Args:
        legislator_name: 立委姓名
        
    Returns:
        tuple: (legislator_name, success, result)
    """
    try:
        # 創建分析器實例
        analyzer = MongoUpdateAnalyzer()
        
        # 分析立委數據
        result = analyzer.analyze_legislator(legislator_name)
        
        if result and result.get('success'):
            return (legislator_name, True, "分析成功")
        else:
            error_msg = result.get('error', '未知錯誤') if isinstance(result, dict) else str(result)
            return (legislator_name, False, error_msg)
        
    except Exception as e:
        import traceback
        error_msg = f"分析 {legislator_name} 時出錯: {e}\n詳細錯誤: {traceback.format_exc()}"
        return (legislator_name, False, error_msg)

def main():
    """主函數 - 支援並行處理"""
    import argparse
    
    parser = argparse.ArgumentParser(description='立委數據分析器')
    parser.add_argument('--parallel', action='store_true', help='使用並行處理')
    parser.add_argument('--workers', type=int, default=4, help='並行執行緒數')
    parser.add_argument('--legislators', nargs='+', help='指定立委列表')
    args = parser.parse_args()
    
    if args.parallel:
        # 並行處理模式
        logger.info("=== 並行立委數據分析系統 ===")
        results = analyze_all_legislators_parallel(
            legislators=args.legislators,
            max_workers=args.workers
        )
        logger.info(f"🎉 並行分析完成！成功: {results.get('success_count', 0)}")
    else:
        # 原有模式
        logger.info("=== 立委數據分析系統 ===")
        analyzer = MongoUpdateAnalyzer()
        
        if args.legislators:
            # 分析指定立委
            for legislator in args.legislators:
                logger.info(f"🔄 分析立委: {legislator}")
                result = analyzer.analyze_legislator(legislator)
                if result and result.get('success'):
                    logger.info(f"✅ {legislator} 分析完成")
                else:
                    logger.error(f"❌ {legislator} 分析失敗")
        else:
            # 分析所有立委
            legislators = analyzer.get_legislators_with_gaps()
            if legislators:
                logger.info(f"🔄 開始分析 {len(legislators)} 位立委...")
                for legislator in legislators:
                    logger.info(f"🔄 分析立委: {legislator}")
                    result = analyzer.analyze_legislator(legislator)
                    if result and result.get('success'):
                        logger.info(f"✅ {legislator} 分析完成")
                    else:
                        logger.error(f"❌ {legislator} 分析失敗")
            else:
                logger.info("✅ 所有立委數據都已是最新狀態")

if __name__ == "__main__":
    main() 