#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json

def check_merge_status():
    """檢查哪些立委需要合併fb_data"""
    final_dir = 'crawler/processed/final_data'
    fb_dir = 'crawler/fb_data'
    
    if not os.path.exists(fb_dir):
        print("❌ fb_data目錄不存在")
        return
    
    final_files = os.listdir(final_dir)
    fb_files = os.listdir(fb_dir)
    
    print("=== 合併狀態檢查 ===")
    
    # 檢查有fb_data的立委
    fb_legislators = []
    for fb_file in fb_files:
        if fb_file.endswith('_使用者分析.json'):
            name = fb_file.replace('_使用者分析.json', '')
            fb_legislators.append(name)
    
    print(f"📊 fb_data中有 {len(fb_legislators)} 位立委:")
    for name in fb_legislators:
        final_file = f'{name}_使用者分析.json'
        if final_file in final_files:
            print(f"✅ {name}: final_data + fb_data")
        else:
            print(f"❌ {name}: 只有fb_data，沒有final_data")
    
    print(f"\n📊 總計: {len(fb_legislators)} 位立委有fb_data")

if __name__ == "__main__":
    check_merge_status() 