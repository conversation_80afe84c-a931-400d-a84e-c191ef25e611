#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB Atlas 索引優化腳本

為了符合 Atlas 免費層的限制（每個集合最多 64 個索引），
此腳本將移除不必要的索引，只保留最重要的索引。

運行前請確保：
1. 已設定 MONGODB_ATLAS_URI 環境變數
2. 已備份重要資料
3. 了解哪些索引對您的應用程式最重要
"""

import os
import sys
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.errors import OperationFailure
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_atlas_client():
    """取得 Atlas 連接"""
    uri = os.getenv('MONGODB_ATLAS_URI')
    if not uri:
        raise ValueError("請設定 MONGODB_ATLAS_URI 環境變數")
    
    if not uri.startswith('mongodb+srv://'):
        raise ValueError("請確認使用正確的 Atlas 連接字串格式 (mongodb+srv://)")
    
    return MongoClient(uri)

def analyze_current_indexes(db):
    """分析目前的索引使用情況"""
    logger.info("📊 分析目前索引使用情況...")
    
    total_indexes = 0
    collection_indexes = {}
    
    for collection_name in db.list_collection_names():
        collection = db[collection_name]
        indexes = list(collection.list_indexes())
        collection_indexes[collection_name] = indexes
        total_indexes += len(indexes)
        
        logger.info(f"📋 {collection_name}: {len(indexes)} 個索引")
        for idx in indexes:
            logger.info(f"  - {idx['name']}: {idx.get('key', {})}")
    
    logger.info(f"📊 總計: {total_indexes} 個索引")
    return collection_indexes

def optimize_legislators_collection(db):
    """優化 legislators 集合的索引"""
    logger.info("🔧 優化 legislators 集合...")
    
    collection = db.legislators
    
    # 刪除現有索引（保留 _id）
    try:
        collection.drop_indexes()
        logger.info("✅ 已移除舊索引")
    except OperationFailure as e:
        logger.warning(f"⚠️ 移除索引時發生錯誤: {e}")
    
    # 建立優化的索引
    essential_indexes = [
        # 立委姓名 - 最重要的查詢欄位
        ([('name', ASCENDING)], {'name': 'name_1'}),
        
        # 建立時間 - 用於排序和時間範圍查詢
        ([('created_at', DESCENDING)], {'name': 'created_at_-1'}),
        
        # 複合索引：姓名 + 更新時間
        ([('name', ASCENDING), ('updated_at', DESCENDING)], {'name': 'name_1_updated_at_-1'}),
    ]
    
    for index_spec, options in essential_indexes:
        try:
            collection.create_index(index_spec, **options)
            logger.info(f"✅ 建立索引: {options['name']}")
        except OperationFailure as e:
            logger.error(f"❌ 建立索引失敗 {options['name']}: {e}")

def optimize_crawler_data_collection(db):
    """優化 crawler_data 集合的索引"""
    logger.info("🔧 優化 crawler_data 集合...")
    
    collection = db.crawler_data
    
    # 刪除現有索引（保留 _id）
    try:
        collection.drop_indexes()
        logger.info("✅ 已移除舊索引")
    except OperationFailure as e:
        logger.warning(f"⚠️ 移除索引時發生錯誤: {e}")
    
    # 建立優化的索引
    essential_indexes = [
        # 立委姓名 - 主要查詢欄位
        ([('legislator_name', ASCENDING)], {'name': 'legislator_name_1'}),
        
        # 平台 - 常用過濾條件
        ([('platform', ASCENDING)], {'name': 'platform_1'}),
        
        # 建立時間 - 時間範圍查詢
        ([('created_at', DESCENDING)], {'name': 'created_at_-1'}),
        
        # 複合索引：立委 + 平台
        ([('legislator_name', ASCENDING), ('platform', ASCENDING)], {'name': 'legislator_platform_1'}),
        
        # 複合索引：立委 + 時間（最常用的查詢組合）
        ([('legislator_name', ASCENDING), ('created_at', DESCENDING)], {'name': 'legislator_time_1'}),
    ]
    
    for index_spec, options in essential_indexes:
        try:
            collection.create_index(index_spec, **options)
            logger.info(f"✅ 建立索引: {options['name']}")
        except OperationFailure as e:
            logger.error(f"❌ 建立索引失敗 {options['name']}: {e}")

def optimize_visitor_collection(db):
    """優化訪問統計集合的索引"""
    logger.info("🔧 優化訪問統計集合...")
    
    if 'site_visits' not in db.list_collection_names():
        logger.info("📋 site_visits 集合不存在，跳過")
        return
    
    collection = db.site_visits
    
    # 刪除現有索引（保留 _id）
    try:
        collection.drop_indexes()
        logger.info("✅ 已移除舊索引")
    except OperationFailure as e:
        logger.warning(f"⚠️ 移除索引時發生錯誤: {e}")
    
    # 建立簡單索引
    essential_indexes = [
        # 日期索引
        ([('date', DESCENDING)], {'name': 'date_-1'}),
        
        # 頁面索引
        ([('page', ASCENDING)], {'name': 'page_1'}),
    ]
    
    for index_spec, options in essential_indexes:
        try:
            collection.create_index(index_spec, **options)
            logger.info(f"✅ 建立索引: {options['name']}")
        except OperationFailure as e:
            logger.error(f"❌ 建立索引失敗 {options['name']}: {e}")

def optimize_other_collections(db):
    """優化其他集合的索引"""
    logger.info("🔧 處理其他集合...")
    
    # 獲取所有集合
    all_collections = db.list_collection_names()
    processed_collections = {'legislators', 'crawler_data', 'site_visits'}
    
    for collection_name in all_collections:
        if collection_name not in processed_collections:
            logger.info(f"📋 處理集合: {collection_name}")
            collection = db[collection_name]
            
            # 只保留 _id 索引
            try:
                collection.drop_indexes()
                logger.info(f"✅ {collection_name}: 已移除所有自定義索引")
            except OperationFailure as e:
                logger.warning(f"⚠️ {collection_name}: 移除索引時發生錯誤: {e}")

def verify_index_limits(db):
    """檢查索引數量是否符合 Atlas 限制"""
    logger.info("🔍 檢查索引數量限制...")
    
    total_indexes = 0
    max_indexes_per_collection = 64  # Atlas 免費層限制
    
    for collection_name in db.list_collection_names():
        collection = db[collection_name]
        indexes = list(collection.list_indexes())
        index_count = len(indexes)
        total_indexes += index_count
        
        if index_count > max_indexes_per_collection:
            logger.error(f"❌ {collection_name}: {index_count} 個索引超過限制 ({max_indexes_per_collection})")
        else:
            logger.info(f"✅ {collection_name}: {index_count} 個索引 (符合限制)")
    
    logger.info(f"📊 總索引數量: {total_indexes}")
    return total_indexes

def main():
    """主要執行函數"""
    try:
        logger.info("🚀 開始 MongoDB Atlas 索引優化...")
        
        # 連接到 Atlas
        client = get_atlas_client()
        db_name = os.getenv('MONGODB_ATLAS_DBNAME', 'legislator_recall')
        db = client[db_name]
        
        logger.info(f"🔗 連接到資料庫: {db_name}")
        
        # 測試連接
        client.admin.command('ping')
        logger.info("✅ Atlas 連接成功")
        
        # 分析目前狀況
        logger.info("\n" + "="*50)
        logger.info("📊 優化前狀況分析")
        logger.info("="*50)
        current_indexes = analyze_current_indexes(db)
        
        # 詢問用戶確認
        logger.info("\n" + "⚠️"*20)
        logger.info("警告：此操作將移除現有索引並重新建立優化的索引")
        logger.info("請確保您已備份重要資料")
        logger.info("⚠️"*20)
        
        response = input("\n是否繼續？(y/N): ").strip().lower()
        if response != 'y':
            logger.info("❌ 操作已取消")
            return
        
        # 執行優化
        logger.info("\n" + "="*50)
        logger.info("🔧 開始索引優化")
        logger.info("="*50)
        
        optimize_legislators_collection(db)
        optimize_crawler_data_collection(db)
        optimize_visitor_collection(db)
        optimize_other_collections(db)
        
        # 驗證結果
        logger.info("\n" + "="*50)
        logger.info("🔍 優化後狀況檢查")
        logger.info("="*50)
        total_indexes = verify_index_limits(db)
        
        # 關閉連接
        client.close()
        
        logger.info("\n" + "🎉"*20)
        logger.info("索引優化完成！")
        logger.info(f"總索引數量: {total_indexes}")
        logger.info("您的 Atlas 資料庫現在已針對免費層進行優化")
        logger.info("🎉"*20)
        
    except Exception as e:
        logger.error(f"❌ 優化過程中發生錯誤: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 