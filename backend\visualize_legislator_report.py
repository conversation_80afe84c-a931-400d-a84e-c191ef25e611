#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
將final_data_analysis_report.json的每位立委各平台正負面比例、評論數畫成合併長條圖，並輸出為PNG圖片。
只顯示fb/yt/ptt，fb<50則不顯示，所有長條圖顯示數字，並加一組綜合結果。
"""
import json
import os
import matplotlib
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei', 'SimHei', 'Noto Sans CJK TC', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
import numpy as np

REPORT_PATH = "final_data_analysis_report.json"
OUTPUT_DIR = "legislator_charts"

os.makedirs(OUTPUT_DIR, exist_ok=True)

PLATFORMS = ['fb', 'yt', 'ptt']


def plot_combined_platform_analysis(legislator_name, platform_counts, platform_sentiment):
    # 過濾平台，只保留fb/yt/ptt
    filtered_platforms = []
    filtered_counts = []
    filtered_sentiment = {}
    for p in PLATFORMS:
        count = platform_counts.get(p, 0)
        # fb<50則不顯示
        if p == 'fb' and count < 50:
            continue
        if count > 0:
            filtered_platforms.append(p)
            filtered_counts.append(count)
            if p in platform_sentiment:
                filtered_sentiment[p] = platform_sentiment[p]

    # 綜合數據
    total_count = sum(filtered_counts)
    total_pos = sum(filtered_sentiment[p]["正面數量"] for p in filtered_sentiment if "正面數量" in filtered_sentiment[p])
    total_neg = sum(filtered_sentiment[p]["負面數量"] for p in filtered_sentiment if "負面數量" in filtered_sentiment[p])
    total_ratio = total_pos + total_neg
    if total_ratio > 0:
        total_pos_ratio = round(total_pos / total_ratio * 100, 2)
        total_neg_ratio = round(total_neg / total_ratio * 100, 2)
    else:
        total_pos_ratio = total_neg_ratio = 0
    # 綜合勝負
    if total_pos > total_neg:
        total_winner = "正面勝"
    elif total_neg > total_pos:
        total_winner = "負面勝"
    else:
        total_winner = "平手"

    # 加入綜合到圖表
    plot_platforms = filtered_platforms + ['綜合']
    plot_counts = filtered_counts + [total_count]
    # 綜合情感
    plot_pos_ratios = [filtered_sentiment[p]["正面比率"] if p in filtered_sentiment else 0 for p in filtered_platforms] + [total_pos_ratio]
    plot_neg_ratios = [filtered_sentiment[p]["負面比率"] if p in filtered_sentiment else 0 for p in filtered_platforms] + [total_neg_ratio]

    # 畫圖
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
    # 上：評論數
    bars1 = ax1.bar(plot_platforms, plot_counts, color='#2196F3', alpha=0.7)
    ax1.set_ylabel('評論數')
    ax1.set_title(f'{legislator_name} 各平台評論數（含綜合）')
    for bar, count in zip(bars1, plot_counts):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + max(1, height*0.01), f'{count:,}', ha='center', va='bottom', fontweight='bold')
    # 下：正負面比率
    x = np.arange(len(plot_platforms))
    width = 0.35
    bars2_pos = ax2.bar(x - width/2, plot_pos_ratios, width, label='正面比率', color='#4CAF50', alpha=0.7)
    bars2_neg = ax2.bar(x + width/2, plot_neg_ratios, width, label='負面比率', color='#F44336', alpha=0.7)
    for bar, ratio in zip(bars2_pos, plot_pos_ratios):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1, f'{ratio:.1f}%', ha='center', va='bottom', fontweight='bold')
    for bar, ratio in zip(bars2_neg, plot_neg_ratios):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1, f'{ratio:.1f}%', ha='center', va='bottom', fontweight='bold')
    ax2.set_ylabel('比率(%)')
    ax2.set_title(f'{legislator_name} 各平台正負面比率（含綜合）')
    ax2.set_xticks(x)
    ax2.set_xticklabels(plot_platforms)
    ax2.legend()
    # 綜合勝負文字
    ax2.text(len(plot_platforms)-1, max(plot_pos_ratios[-1], plot_neg_ratios[-1]) + 5, f'綜合勝負: {total_winner}', ha='center', va='bottom', fontsize=14, color='purple', fontweight='bold')
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/{legislator_name}_平台分析.png", dpi=300, bbox_inches='tight')
    plt.close()

def main():
    with open(REPORT_PATH, 'r', encoding='utf-8') as f:
        report = json.load(f)
    for entry in report["立委詳細統計"]:
        name = entry["立委名稱"]
        platform_counts = entry["平台統計"].get("各平台評論數", {})
        platform_sentiment = entry["平台統計"].get("各平台情感分析", {})
        if platform_counts:
            plot_combined_platform_analysis(name, platform_counts, platform_sentiment)
            print(f"✅ 已輸出: {name} 的合併圖表")
    print(f"\n🎉 所有圖表已輸出到 {OUTPUT_DIR}/")

if __name__ == "__main__":
    main() 