#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
立委罷免數據爬蟲系統 - 完整功能版
包含原本main.py的所有功能：爬蟲、數據處理、MongoDB更新、備份管理等
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 導入功能模組
try:
    from crawler.simple_crawler_manager import SimpleCrawlerManager
    from backup_manager import BackupManager
    from data_integrator import integrate_legislator_data
    from gemini_analyzer import analyze_legislators_emotions
    from final_processor import process_all_legislators_final_data
    from smart_processor import update_mongodb_crawler_data_smart, update_sentiment_labels_from_gemini, generate_legislators_statistics
except ImportError as e:
    print(f"❌ 模組導入失敗: {e}")
    print("請確保所有必要的模組文件都在正確的位置")
    sys.exit(1)

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('main_new.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompleteSocialMediaAnalyzer:
    """完整社會媒體分析系統主控制器"""
    
    def __init__(self, base_dir: str = "backend"):
        self.base_dir = base_dir
        self.legislators = self.load_legislators()
        self.crawler_manager = None
        self.backup_manager = None
        
        logger.info(f"🚀 完整社會媒體分析系統初始化完成")
        logger.info(f"  - 基礎目錄: {self.base_dir}")
        logger.info(f"  - 立委數量: {len(self.legislators)}")
    
    def load_legislators(self) -> List[str]:
        """載入立委配置"""
        return [
            "林思銘", "陳超明", "邱鎮軍", "顏寬恒", "高虹安",
            "楊瓊瓔", "廖偉翔", "黃建豪", "羅廷瑋", "江啟臣", "馬文君", "游顥", "謝衣鳳", "丁學忠", "傅崐萁", "黃建賓",
            "洪孟楷", "葉元之", "張智倫", "林德福", "羅明才", "廖先翔", "王鴻薇", "李彥秀", "羅智強", 
            "徐巧芯", "賴士葆", "林沛祥", "牛煦庭", "涂權吉", "魯明哲", "萬美玲", "呂玉玲", "邱若華", "鄭正鈐", "徐欣瑩"
        ]
    
    def validate_setup(self) -> bool:
        """驗證系統設定"""
        try:
            # 檢查API keys
            has_api_key = False
            
            if os.getenv('GEMINI_API_KEY'):
                has_api_key = True
                logger.info("✅ 使用環境變數中的GEMINI_API_KEY")
            elif os.path.exists('api.json'):
                try:
                    with open('api.json', 'r', encoding='utf-8') as f:
                        api_data = json.load(f)
                        if api_data.get('api_keys') and len(api_data['api_keys']) > 0:
                            has_api_key = True
                            logger.info(f"✅ 使用api.json中的API keys ({len(api_data['api_keys'])}個)")
                except Exception as e:
                    logger.warning(f"⚠️ 讀取api.json失敗: {e}")
            
            if not has_api_key:
                logger.error("❌ 缺少GEMINI_API_KEY，請設置環境變數或配置api.json")
                return False
            
            # 檢查必要目錄
            required_dirs = ['crawler/processed', 'crawler/processed/alldata', 
                           'crawler/processed/user_data', 'crawler/processed/final_data']
            
            for dir_path in required_dirs:
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path, exist_ok=True)
                    logger.info(f"📁 創建目錄: {dir_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 驗證失敗: {e}")
            return False
    
    def run_crawling(self, days: int = 30, platforms: List[str] = None, 
                    legislators: List[str] = None, use_proxy: bool = True,
                    skip_url_collection: bool = False) -> bool:
        """
        執行爬蟲階段
        
        Args:
            days: 爬取天數
            platforms: 要爬取的平台列表
            legislators: 要爬取的立委列表
            use_proxy: 是否使用代理
            skip_url_collection: 是否跳過URL收集
            
        Returns:
            bool: 爬蟲是否成功
        """
        if platforms is None:
            platforms = ['ptt', 'youtube']
        
        if legislators is None:
            legislators = self.legislators
        
        logger.info(f"🕷️ 開始執行爬蟲階段...")
        logger.info(f"  - 天數: {days}")
        logger.info(f"  - 平台: {platforms}")
        logger.info(f"  - 立委: {len(legislators)} 位")
        logger.info(f"  - 代理: {'使用' if use_proxy else '不使用'}")
        logger.info(f"  - URL收集: {'跳過' if skip_url_collection else '執行'}")
        
        try:
            # 初始化爬蟲管理器
            self.crawler_manager = SimpleCrawlerManager()
            
            # 設置代理
            if not use_proxy:
                self.crawler_manager.set_proxy_disabled()
            
            # 執行爬蟲
            result = self.crawler_manager.crawl_all_legislators(
                legislators=legislators,
                days=days,
                platforms=platforms,
                skip_url_collection=skip_url_collection,
                use_proxy=use_proxy
            )
            
            if result:
                logger.info(f"🎉 爬蟲階段完成！")
                return True
            else:
                logger.error(f"❌ 爬蟲階段失敗！")
                return False
                
        except Exception as e:
            logger.error(f"❌ 爬蟲階段執行失敗: {e}")
            return False
    
    def run_data_processing(self, legislators: List[str] = None) -> bool:
        """
        執行數據處理階段
        
        Args:
            legislators: 要處理的立委列表
            
        Returns:
            bool: 處理是否成功
        """
        if legislators is None:
            legislators = self.legislators
        
        logger.info(f"🔄 開始執行數據處理階段...")
        logger.info(f"  - 立委數量: {len(legislators)}")
        
        success_count = 0
        
        for legislator in legislators:
            try:
                logger.info(f"🔄 處理 {legislator} 的數據...")
                
                result = integrate_legislator_data(legislator, self.base_dir)
                if result:
                    success_count += 1
                    logger.info(f"✅ {legislator} 數據處理完成")
                else:
                    logger.warning(f"⚠️ {legislator} 數據處理失敗或無數據")
                
            except Exception as e:
                logger.error(f"❌ {legislator} 數據處理失敗: {e}")
        
        logger.info(f"🎉 數據處理階段完成！成功處理 {success_count}/{len(legislators)} 位立委")
        return success_count > 0
    
    def generate_final_data(self, legislators: List[str] = None) -> bool:
        """
        生成最終數據（不重新進行Gemini分析）
        
        Args:
            legislators: 要處理的立委列表
            
        Returns:
            bool: 生成是否成功
        """
        if legislators is None:
            legislators = self.legislators
        
        logger.info(f"📄 開始生成最終數據...")
        logger.info(f"  - 立委數量: {len(legislators)}")
        
        try:
            success = process_all_legislators_final_data(self.base_dir)
            
            if success:
                logger.info(f"🎉 最終數據生成完成！")
            else:
                logger.warning(f"⚠️ 最終數據生成部分失敗")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 最終數據生成失敗: {e}")
            return False
    
    def run_gemini_analysis(self, legislators: List[str] = None, batch_size: int = 500,
                           youtube_only: bool = False) -> bool:
        """
        執行Gemini分析階段
        
        Args:
            legislators: 要分析的立委列表
            batch_size: 批次大小
            youtube_only: 是否只分析YouTube數據
            
        Returns:
            bool: 分析是否成功
        """
        if legislators is None:
            legislators = self.legislators
        
        logger.info(f"🧠 開始執行Gemini分析階段...")
        logger.info(f"  - 立委數量: {len(legislators)}")
        logger.info(f"  - 批次大小: {batch_size}")
        logger.info(f"  - YouTube專用: {'是' if youtube_only else '否'}")
        
        try:
            success = analyze_legislators_emotions(
                legislators=legislators,
                batch_size=batch_size,
                quiet=False,
                incremental=True,
                base_dir=self.base_dir,
                youtube_only=youtube_only
            )
            
            if success:
                logger.info(f"🎉 Gemini分析階段完成！")
            else:
                logger.warning(f"⚠️ Gemini分析階段部分失敗")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Gemini分析階段失敗: {e}")
            return False
    
    def update_mongodb_smart(self, legislators: List[str] = None) -> bool:
        """
        智能更新MongoDB（包含need_reRun邏輯）
        
        Args:
            legislators: 要更新的立委列表
            
        Returns:
            bool: 更新是否成功
        """
        if legislators is None:
            legislators = self.legislators
        
        logger.info(f"🗄️ 開始智能更新MongoDB...")
        logger.info(f"  - 立委數量: {len(legislators)}")
        
        try:
            # 1. 更新crawler_data（智能比對和更新）
            success1 = update_mongodb_crawler_data_smart(legislators, self.base_dir)
            
            if not success1:
                logger.error("❌ MongoDB crawler_data更新失敗")
                return False
            
            # 2. 更新情感標籤
            success2 = update_sentiment_labels_from_gemini(legislators, self.base_dir)
            
            if not success2:
                logger.warning("⚠️ 情感標籤更新失敗")
            
            # 3. 生成統計報告
            success3 = generate_legislators_statistics(legislators, self.base_dir)
            
            if not success3:
                logger.warning("⚠️ 統計報告生成失敗")
            
            logger.info(f"🎉 MongoDB智能更新完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ MongoDB智能更新失敗: {e}")
            return False
    
    def run_backup_management(self, no_backup: bool = False, reset_youtube: bool = False) -> bool:
        """
        執行備份管理
        
        Args:
            no_backup: 是否跳過備份
            reset_youtube: 是否重置YouTube數據
            
        Returns:
            bool: 備份是否成功
        """
        if no_backup:
            logger.info("⏭️ 跳過備份管理")
            return True
        
        logger.info(f"💾 開始執行備份管理...")
        
        try:
            self.backup_manager = BackupManager()
            
            if reset_youtube:
                logger.info("🔄 重置YouTube數據...")
                self.backup_manager.reset_youtube_data()
            
            # 執行備份
            backup_result = self.backup_manager.backup_all_data()
            
            if backup_result:
                logger.info(f"🎉 備份管理完成！")
                return True
            else:
                logger.warning(f"⚠️ 備份管理部分失敗")
                return False
                
        except Exception as e:
            logger.error(f"❌ 備份管理失敗: {e}")
            return False
    
    def run_complete_pipeline(self, days: int = 30, platforms: List[str] = None,
                            legislators: List[str] = None, use_proxy: bool = True,
                            skip_url_collection: bool = False, no_backup: bool = False,
                            reset_youtube: bool = False) -> bool:
        """
        執行完整流程
        
        Args:
            days: 爬取天數
            platforms: 要爬取的平台列表
            legislators: 要處理的立委列表
            use_proxy: 是否使用代理
            skip_url_collection: 是否跳過URL收集
            no_backup: 是否跳過備份
            reset_youtube: 是否重置YouTube數據
            
        Returns:
            bool: 流程是否成功
        """
        logger.info(f"🚀 開始執行完整流程...")
        
        try:
            # 1. 備份管理
            if not self.run_backup_management(no_backup, reset_youtube):
                logger.error("❌ 備份管理失敗，停止執行")
                return False
            
            # 2. 爬蟲階段
            if not self.run_crawling(days, platforms, legislators, use_proxy, skip_url_collection):
                logger.error("❌ 爬蟲階段失敗，停止執行")
                return False
            
            # 3. 數據處理階段
            if not self.run_data_processing(legislators):
                logger.error("❌ 數據處理階段失敗，停止執行")
                return False
            
            # 4. Gemini分析階段
            if not self.run_gemini_analysis(legislators):
                logger.error("❌ Gemini分析階段失敗，停止執行")
                return False
            
            # 5. 生成最終數據
            if not self.generate_final_data(legislators):
                logger.error("❌ 最終數據生成失敗，停止執行")
                return False
            
            # 6. MongoDB智能更新
            if not self.update_mongodb_smart(legislators):
                logger.error("❌ MongoDB智能更新失敗，停止執行")
                return False
            
            logger.info(f"🎉 完整流程執行成功！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 完整流程執行失敗: {e}")
            return False
    
    def show_status(self):
        """顯示系統狀態"""
        logger.info("📊 系統狀態報告")
        
        all_legislators = self.legislators
        
        # 檢查每個立委的狀態
        for legislator in all_legislators:
            logger.info(f"\n👤 {legislator}:")
            
            # 檢查gemini格式數據
            gemini_file = f"{self.base_dir}/crawler/processed/user_data/{legislator}_gemini_format.json"
            if os.path.exists(gemini_file):
                try:
                    with open(gemini_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    logger.info(f"  📁 Gemini格式: 有數據 ({len(data.get('users', {}))} 位用戶)")
                except:
                    logger.info(f"  📁 Gemini格式: 文件存在但讀取失敗")
            else:
                logger.info(f"  📁 Gemini格式: 無文件")
            
            # 檢查最終分析結果
            final_file = f"{self.base_dir}/crawler/processed/final_data/{legislator}_使用者分析.json"
            if os.path.exists(final_file):
                logger.info(f"  ✅ 最終分析: 已完成")
            else:
                logger.info(f"  ❌ 最終分析: 未完成")

def parse_arguments():
    """解析命令列參數"""
    parser = argparse.ArgumentParser(
        description='立委罷免數據爬蟲系統 - 完整功能版',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
範例用法：
  python main_new.py --days 30                           # 抓取最近30天資料 (先PTT後YouTube，已優化速度)
  python main_new.py --platforms ptt,youtube             # 指定爬取平台順序
  python main_new.py --legislators 高虹安 牛煦庭          # 指定立委  
  python main_new.py --days 400 --platforms ptt,youtube  # 400天PTT+YouTube資料 (YouTube已加速)
  python main_new.py --skip-url-collection               # 跳過URL收集，只抓數據和處理
  python main_new.py --process-only                      # 跳過所有爬蟲，只做數據處理
  python main_new.py --generate-final-data               # 只生成final_data，不重新進行Gemini分析
  python main_new.py --update-mongo-only                 # 只進行MongoDB更新 (包含最近7天一天一個統計點等)
  python main_new.py --youtube-only-analysis             # 只分析YouTube數據（與現有PTT合併但不重新分析PTT）
  python main_new.py --no-backup                         # 跳過備份
  python main_new.py --reset-youtube-data                # 重置YouTube數據
  python main_new.py --use-proxy                         # 使用代理
  python main_new.py --no-proxy                          # 不使用代理
  python main_new.py --max-workers 8                     # 設置並行工作數
        """
    )
    
    # 主要功能參數
    parser.add_argument('--days', type=int, default=30,
                       help='爬取天數（預設：30）')
    parser.add_argument('--platforms', nargs='+', 
                       choices=['ptt', 'youtube', 'threads'],
                       help='指定要爬取的平台順序')
    parser.add_argument('--legislators', nargs='+',
                       help='指定要處理的立委姓名')
    
    # 處理模式參數
    parser.add_argument('--process-only', action='store_true',
                       help='跳過所有爬蟲，只做數據處理')
    parser.add_argument('--generate-final-data', action='store_true',
                       help='只生成final_data，不重新進行Gemini分析')
    parser.add_argument('--update-mongo-only', action='store_true',
                       help='只進行MongoDB更新 (包含最近7天一天一個統計點等)')
    parser.add_argument('--youtube-only-analysis', action='store_true',
                       help='只分析YouTube數據（與現有PTT合併但不重新分析PTT）')
    
    # 備份管理參數
    parser.add_argument('--no-backup', action='store_true',
                       help='跳過備份')
    parser.add_argument('--reset-youtube-data', action='store_true',
                       help='重置YouTube數據')
    
    # 代理控制參數
    parser.add_argument('--use-proxy', action='store_true',
                       help='使用代理')
    parser.add_argument('--no-proxy', action='store_true',
                       help='不使用代理')
    
    # 其他參數
    parser.add_argument('--skip-url-collection', action='store_true',
                       help='跳過URL收集，只抓數據和處理')
    parser.add_argument('--max-workers', type=int, default=4,
                       help='並行工作數（預設：4）')
    parser.add_argument('--base-dir', default='backend',
                       help='基礎目錄（預設：backend）')
    parser.add_argument('--status', action='store_true',
                       help='顯示系統狀態')
    
    return parser.parse_args()

def main():
    """主函數"""
    args = parse_arguments()
    
    try:
        # 初始化分析器
        analyzer = CompleteSocialMediaAnalyzer(args.base_dir)
        
        # 顯示狀態
        if args.status:
            analyzer.show_status()
            return 0
        
        # 驗證設定
        logger.info("驗證系統設定...")
        if not analyzer.validate_setup():
            logger.error("系統設定驗證失敗，請檢查環境配置")
            input("按 Enter 鍵退出...")
            return 1
        
        # 處理 --update-mongo-only 模式
        if args.update_mongo_only:
            logger.info("🔄 --update-mongo-only 模式啟動...")
            legislators = args.legislators or analyzer.legislators
            success = analyzer.update_mongodb_smart(legislators)
            if success:
                logger.info("✅ MongoDB更新完成！")
                return 0
            else:
                logger.error("❌ MongoDB更新失敗！")
                return 1
        
        # 處理 --generate-final-data 模式
        if args.generate_final_data:
            logger.info("🔄 --generate-final-data 模式啟動...")
            legislators = args.legislators or analyzer.legislators
            success = analyzer.generate_final_data(legislators)
            if success:
                logger.info("✅ 最終數據生成完成！")
                return 0
            else:
                logger.error("❌ 最終數據生成失敗！")
                return 1
        
        # 處理 --process-only 模式
        if args.process_only:
            logger.info("🔄 --process-only 模式啟動...")
            legislators = args.legislators or analyzer.legislators
            logger.info(f"📋 --process-only 模式：處理 {len(legislators)} 位立委")
            
            # 執行數據處理流程
            if not analyzer.run_data_processing(legislators):
                logger.error("❌ 數據處理失敗，停止執行")
                return 1
            
            if not analyzer.run_gemini_analysis(legislators, youtube_only=args.youtube_only_analysis):
                logger.error("❌ Gemini分析失敗，停止執行")
                return 1
            
            if not analyzer.generate_final_data(legislators):
                logger.error("❌ 最終數據生成失敗，停止執行")
                return 1
            
            if not analyzer.update_mongodb_smart(legislators):
                logger.error("❌ MongoDB更新失敗，停止執行")
                return 1
            
            logger.info("✅ --process-only 模式完成！")
            return 0
        
        # 執行完整流程
        success = analyzer.run_complete_pipeline(
            days=args.days,
            platforms=args.platforms,
            legislators=args.legislators,
            use_proxy=not args.no_proxy,
            skip_url_collection=args.skip_url_collection,
            no_backup=args.no_backup,
            reset_youtube=args.reset_youtube_data
        )
        
        if success:
            logger.info("✅ 完整流程執行成功！")
            return 0
        else:
            logger.error("❌ 完整流程執行失敗！")
            return 1
            
    except KeyboardInterrupt:
        logger.info("⏹️ 用戶中斷操作")
        input("按 Enter 鍵退出...")
        return 1
    except Exception as e:
        logger.error(f"❌ 系統錯誤: {e}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        input("按 Enter 鍵退出...")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 