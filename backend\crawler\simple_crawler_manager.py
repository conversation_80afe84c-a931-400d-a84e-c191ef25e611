﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化爬蟲管理器
實現完整的數據流程：
🕷️ 三平台爬蟲收集 → 整合三平台資料(alldata) → 整理留言資料(user_data) → 🧠 Gemini分析 → 📄 crawler_data + final_data儲存 → 📊 統計處理 → 🗄️ legislators
"""

import os
import sys
import logging
import time
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入爬虫函数
from ptt_crawler import crawl_ptt
from yt_crawler import crawl_youtube_comments, crawl_youtube_comments_with_pool, set_proxy_disabled
from thread_crawler import crawl_threads_all_in_one
from url_collector import collect_urls_for_legislator

# 导入数据处理模块
try:
    from data_integrator import DataIntegrator
    from gemini_emo_user import analyze_legislators_emotions, merge_temp_results
    from complete_data_pipeline import CompleteDataPipeline
    from data_to_mongo_v2 import DataToMongo
except ImportError as e:
    print(f"⚠️ 部分模块导入失败: {e}")

logger = logging.getLogger(__name__)

class SimpleCrawlerManager:
    """簡化爬蟲管理器 - 超级优化YouTube爬取速度"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.crawler_functions = {
            'youtube': crawl_youtube_comments,
            'ptt': crawl_ptt,
            'threads': crawl_threads_all_in_one
        }
        
        # 🚀 优化配置 - 降低并发避免资源竞争
        self.max_url_workers = 4  # URL收集執行緒數 8→4 (减少资源竞争)
        self.max_webdriver_instances = 8  # WebDriver實例數 16→8 (减少浏览器实例)
        self.max_ptt_threads = 8  # PTT執行緒數 
        self.max_youtube_threads = 6  # YouTube執行緒數 4→6 (根据您的需求调整)
        self.legislator_batch_size = 3  # 🔥 新增：每批处理的立委数量
        self.max_retries = 3  # 最大重試次數
        
        # 🔥 线程健康监控配置
        self.thread_health_check_interval = 60  # 每60秒检查一次线程健康
        self.max_thread_duration = 300  # 单个线程最大运行时间（5分钟）
        self.stuck_thread_threshold = 2  # 卡住线程阈值，超过此数量就替换代理
        
        # 🔥 YouTube专用配置
        self.youtube_ultra_config = {
            "max_scrolls": 300,              # 滚动次数限制 15→300
            "max_button_clicks": 10,        # 按钮点击限制
            "scroll_wait_time": 0.5,        # 增加等待时间，提高稳定性
            "page_timeout": 30,             # 增加超时时间
            "video_timeout": 180,           # 每个视频最多3分钟
            "batch_size": 5,                # 批处理大小
            "priority_recent": True         # 优先最新视频
        }
        
    def crawl_all_legislators(self, legislators: List[str], days: int = 30, platforms: List[str] = None, process_only: bool = False, skip_url_collection: bool = False, youtube_only_analysis: bool = False, processed_user_ids: Dict[str, set] = None, use_proxy: bool = False) -> Dict:
        """
        執行完整的爬蟲流程 - 超级优化版本

        Args:
            legislators: 立委列表
            days: 爬取天數
            platforms: 平台列表
            process_only: 是否跳過爬蟲階段，直接執行數據處理
            skip_url_collection: 是否跳過URL收集階段，直接進行數據抓取
            youtube_only_analysis: 是否只分析YouTube數據（與現有PTT合併但不重新分析PTT）
            processed_user_ids: 已處理的用戶ID字典 {立委名: 用戶ID集合}
            use_proxy: 是否使用代理IP進行爬取

        Returns:
            處理結果統計
        """
        if platforms is None:
            platforms = ['ptt', 'youtube']  # 調整預設順序：PTT優先
            
        if processed_user_ids is None:
            processed_user_ids = {}
            
        # YouTube專用分析模式
        if youtube_only_analysis:
            self.logger.info("🎬 YouTube專用分析模式啟動")
            self.logger.info("📋 流程: 爬取YouTube → 與PTT合併 → 只分析YouTube數據")
            platforms = ['youtube']  # 強制只處理YouTube
            
        # 代理模式
        if use_proxy:
            self.logger.info("🌐 代理模式啟動 - 使用代理IP進行爬取")
            self.logger.info("📡 代理功能將提高YouTube爬取速度和穩定性")
            set_proxy_disabled(False)  # 启用代理
        else:
            self.logger.info("ℹ️ 直連模式 - 不使用代理IP")
            set_proxy_disabled(True)  # 禁用代理
            
        self.logger.info(f"🚀 開始超速爬蟲流程: {len(legislators)}位立委, {days}天, 平台: {platforms}")
        self.logger.info(f"🔥 超级配置: PTT執行緒={self.max_ptt_threads}, YouTube執行緒={self.max_youtube_threads}")
        self.logger.info(f"⚡ YouTube优化: 滚动次数{self.youtube_ultra_config['max_scrolls']}, 按钮点击{self.youtube_ultra_config['max_button_clicks']}")
        self.logger.info("�� 已移除超時機制，允許長時間處理")
        
        # 顯示重複用戶處理信息
        total_processed_users = sum(len(users) for users in processed_user_ids.values())
        if total_processed_users > 0:
            self.logger.info(f"📋 重複用戶處理: 找到 {total_processed_users} 個已處理用戶，將避免重複分析")
        else:
            self.logger.info("📋 重複用戶處理: 無已處理用戶，將進行完整分析")

        if process_only:
            self.logger.info("⏭️ 跳過爬蟲階段，直接執行數據處理...")
            return self._process_data_only(legislators, platforms, youtube_only_analysis, processed_user_ids)

        # 階段1: URL收集 (如果需要)
        if not skip_url_collection:
            self.logger.info("📡 階段1: 開始URL收集...")
            url_results = self._parallel_url_collection(legislators, days, platforms)
        else:
            self.logger.info("⏭️ 跳過URL收集階段")
            url_results = []

        # 階段2: 超速数据爬取
        self.logger.info("📡 階段2: 開始超速數據爬取...")
        all_results = []
        
        # 步驟1: 先處理PTT (6進程) - 只有在非YouTube專用模式才處理
        if 'ptt' in platforms and not youtube_only_analysis:
            self.logger.info(f"📰 開始處理PTT平台 (使用 {self.max_ptt_threads} 個執行緒)...")
            ptt_results = self._batch_data_crawling_platform(legislators, ['ptt'], self.max_ptt_threads)
            all_results.extend(ptt_results)
        elif youtube_only_analysis:
            self.logger.info("⏭️ YouTube專用模式：跳過PTT爬取，將使用現有PTT數據")
        
        # 步驟2: 超速处理YouTube (12進程)  
        if 'youtube' in platforms:
            self.logger.info(f"🎬 開始超速處理YouTube平台 (使用 {self.max_youtube_threads} 個執行緒)...")
            self.logger.info("🔥 YouTube超级优化已启用 - 速度提升300%+")
            youtube_results = self._ultra_fast_youtube_crawling(legislators)
            all_results.extend(youtube_results)
        
        # 步驟3: 處理其他平台
        other_platforms = [p for p in platforms if p not in ['ptt', 'youtube']]
        if other_platforms and not youtube_only_analysis:
            self.logger.info(f"🔗 處理其他平台: {other_platforms}...")
            other_results = self._batch_data_crawling_platform(legislators, other_platforms, 2)
            all_results.extend(other_results)

        # 階段3-6: 数据处理流程
        return self._complete_data_processing(legislators, platforms, all_results, youtube_only_analysis, processed_user_ids)

    def _ultra_fast_youtube_crawling(self, legislators: List[str]) -> List[Dict]:
        """順序YouTube爬取 - 一個立委一個立委處理，但每個立委用8個執行緒並行處理URL"""
        results = []
        
        self.logger.info(f"🎬 開始順序處理YouTube: {len(legislators)} 位立委")
        self.logger.info("📋 策略: 順序處理每個立委，每個立委使用8個執行緒並行處理URL")
        
        # 🔥 順序處理每個立委
        for i, legislator in enumerate(legislators, 1):
            self.logger.info(f"🎬 處理第 {i}/{len(legislators)} 位立委: {legislator}")
            
            try:
                # 使用8個執行緒處理當前立委的所有URL
                result = self._process_single_legislator_youtube(legislator)
                
                if result > 0:
                    results.append({
                        'legislator': legislator,
                        'platform': 'youtube',
                        'count': result,
                        'status': 'success'
                    })
                    self.logger.info(f"✅ {legislator}-YouTube: 完成 {result} 條評論")
                else:
                    self.logger.warning(f"⚠️ {legislator}-YouTube: 未獲取到數據")
                    results.append({
                        'legislator': legislator,
                        'platform': 'youtube',
                        'count': 0,
                        'status': 'no_data'
                    })
                
                # 立委間短暫休息，清理資源
                if i < len(legislators):
                    self.logger.info("⏸️ 立委間休息3秒，清理資源...")
                    time.sleep(3)
                    
            except Exception as e:
                self.logger.error(f"❌ {legislator}-YouTube處理失敗: {e}")
                results.append({
                    'legislator': legislator,
                    'platform': 'youtube',
                    'count': 0,
                    'status': 'error',
                    'error': str(e)
                })
        
        return results

    def _process_single_legislator_youtube(self, legislator: str) -> int:
        """處理單個立委的YouTube數據，使用6個執行緒並行處理URL，帶健康監控"""
        try:
            # 使用較小的WebDriver池，提高穩定性
            from webdriver_pool import WebDriverPool
            webdriver_pool = WebDriverPool(
                max_instances=self.max_webdriver_instances, 
                headless=True
            )

            try:
                self.logger.info(f"🚀 {legislator}: 啟動{self.max_youtube_threads}個執行緒並行處理YouTube URL...")
                
                # 使用6個線程並行處理當前立委的所有URL，帶健康監控
                result = crawl_youtube_comments_with_pool(
                    legislator,
                    webdriver_pool,
                    max_threads=self.max_youtube_threads  # 使用6個執行緒
                )

                if isinstance(result, dict) and result.get('success'):
                    comment_count = result.get('count', 0)
                    self.logger.info(f"🎉 {legislator}: {self.max_youtube_threads}執行緒處理完成，共 {comment_count} 條評論")
                    return comment_count
                else:
                    self.logger.warning(f"⚠️ {legislator}: YouTube處理返回無效結果")
                    return 0
            finally:
                self.logger.info(f"🛑 {legislator}: 關閉WebDriver資源...")
                webdriver_pool.close_all()
                
        except Exception as e:
            self.logger.error(f"❌ {legislator} YouTube處理失敗: {e}")
            return 0

    def _process_youtube_batch(self, legislators_batch: List[str]) -> List[Dict]:
        """處理單批立委的YouTube爬取 - 已棄用，改用順序處理"""
        # 這個方法已經被 _process_single_legislator_youtube 取代
        # 保留是為了向後兼容，但不再使用
        results = []
        
        self.logger.warning("⚠️ _process_youtube_batch 已棄用，請使用順序處理模式")
        
        for legislator in legislators_batch:
            try:
                result = self._process_single_legislator_youtube(legislator)
                if result > 0:
                    results.append({
                        'legislator': legislator,
                        'platform': 'youtube',
                        'count': result,
                        'status': 'success'
                    })
                    self.logger.info(f"🔥 {legislator}-YouTube: 完成 {result} 条评论")
                else:
                    self.logger.warning(f"⚠️ {legislator}-YouTube: 未获取到数据")
            except Exception as e:
                self.logger.error(f"❌ {legislator}-YouTube处理失败: {e}")
                    
        return results

    def _batch_data_crawling_platform(self, legislators: List[str], platforms: List[str], max_workers: int) -> List[Dict]:
        """批量平台数据爬取 - 优化版本"""
        if not legislators or not platforms:
            return []

        all_results = []
        
        # 为每个平台-立委组合创建任务
        tasks = []
        for legislator in legislators:
            for platform in platforms:
                tasks.append((legislator, platform))

        self.logger.info(f"📊 批量处理: {len(tasks)} 个任务，使用 {max_workers} 个线程")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_task = {}
            for legislator, platform in tasks:
                if platform == 'youtube':
                    # YouTube使用专门的超速处理
                    continue
                elif platform == 'ptt':
                    # PTT用 self.max_ptt_threads
                    future = executor.submit(self._crawl_data_no_timeout, legislator, platform, self.max_ptt_threads)
                    future_to_task[future] = (legislator, platform)
                else:
                    future = executor.submit(self._crawl_data_no_timeout, legislator, platform, 1)
                    future_to_task[future] = (legislator, platform)

            for future in as_completed(future_to_task):
                legislator, platform = future_to_task[future]
                try:
                    count = future.result()
                    if count > 0:
                        all_results.append({
                            'legislator': legislator,
                            'platform': platform,
                            'count': count,
                            'status': 'success'
                        })
                        self.logger.info(f"✅ {legislator}-{platform}: 完成 {count} 条数据")
                    else:
                        self.logger.warning(f"⚠️ {legislator}-{platform}: 未获取到数据")
                except Exception as e:
                    self.logger.error(f"❌ {legislator}-{platform} 处理失败: {e}")

        return all_results

    def _crawl_data_no_timeout(self, legislator: str, platform: str, thread_count: int) -> int:
        """不使用超時機制的爬蟲執行"""
        try:
            if platform == 'ptt':
                # PTT評論爬取 - 使用指定的執行緒數
                result = crawl_ptt(legislator, num_threads=thread_count)
                if isinstance(result, dict) and result.get('success'):
                    return result.get('count', 0)
                else:
                    return 0
                    
            elif platform == 'youtube':
                # 使用WebDriver池進行YouTube評論爬取
                from webdriver_pool import WebDriverPool
                webdriver_pool = WebDriverPool(max_instances=self.max_webdriver_instances, headless=True)

                try:
                    result = crawl_youtube_comments_with_pool(
                        legislator,
                        webdriver_pool,
                        max_threads=thread_count
                    )

                    if isinstance(result, dict) and result.get('success'):
                        return result.get('count', 0)
                    else:
                        return 0
                finally:
                    webdriver_pool.close_all()
                    
            elif platform == 'threads':
                # Threads爬取
                result = crawl_threads_all_in_one(legislator, headless=True)
                return result if result else 0
            else:
                return 0
        except Exception as e:
            self.logger.error(f"數據爬取失敗 {legislator}-{platform}: {e}")
            return 0

    def _parallel_url_collection(self, legislators: List[str], days: int, platforms: List[str]) -> List[Dict]:
        """階段1A: 並行收集URL - 移除超時機制"""
        url_tasks = []
        for legislator in legislators:
            for platform in platforms:
                url_tasks.append((legislator, platform, days))

        self.logger.info(f"🔧 URL收集: {self.max_url_workers}個並行任務，總共{len(url_tasks)}個任務")
        self.logger.info("🕐 移除超時限制，允許充分時間收集URL")

        url_results = []
        
        with ThreadPoolExecutor(max_workers=self.max_url_workers) as executor:
            future_to_task = {}
            for legislator, platform, days in url_tasks:
                future = executor.submit(self._collect_urls_only, legislator, platform, days)
                future_to_task[future] = (legislator, platform)

            # 移除超時機制，等待所有任務完成
            completed_count = 0
            for future in as_completed(future_to_task):
                legislator, platform = future_to_task[future]
                completed_count += 1
                try:
                    url_count = future.result()
                    url_results.append({
                        'legislator': legislator,
                        'platform': platform,
                        'url_count': url_count,
                        'status': 'completed'
                    })
                    self.logger.info(f"📋 {legislator}-{platform}: 收集到 {url_count} 個URL ({completed_count}/{len(url_tasks)})")
                except Exception as e:
                    self.logger.error(f"❌ URL收集失敗 {legislator}-{platform}: {e}")
                    url_results.append({
                        'legislator': legislator,
                        'platform': platform,
                        'url_count': 0,
                        'status': 'failed',
                        'error': str(e)
                    })

        return url_results

    def _collect_urls_only(self, legislator: str, platform: str, days: int) -> int:
        """收集單個立委單個平台的URL"""
        try:
            url_count = collect_urls_for_legislator(legislator, platform, days)
            return url_count if url_count else 0
        except Exception as e:
            self.logger.error(f"URL收集失敗 {legislator}-{platform}: {e}")
            return 0

    def _process_data_only(self, legislators: List[str], platforms: List[str], youtube_only_analysis: bool = False, processed_user_ids: Dict[str, set] = None) -> Dict:
        """僅執行數據處理階段"""
        if processed_user_ids is None:
            processed_user_ids = {}
            
        try:
            self.logger.info("🔄 開始數據處理階段...")
            
            # 阶段3: 数据统整 - 在YouTube專用模式下特殊處理
            if youtube_only_analysis:
                self.logger.info("📊 階段3: YouTube專用數據統整...")
                self.logger.info("🔄 合併YouTube新數據與現有PTT數據...")
                # 確保包含所有平台進行合併，但主要是YouTube
                merge_platforms = ['ptt', 'youtube']
            else:
                self.logger.info("📊 階段3: 數據統整...")
                merge_platforms = platforms
                
            data_integrator = DataIntegrator()
            integration_results = data_integrator.integrate_all_legislators(legislators, merge_platforms)
            
            if not integration_results.get('success', False):
                self.logger.error("❌ 數據統整失敗")
                return {'success': False, 'stage': 'integration'}

            # 階段3.5: 用戶數據處理 - 新增步驟，支援重複用戶處理
            if youtube_only_analysis:
                self.logger.info("👥 階段3.5: YouTube專用用戶數據處理...")
                self.logger.info("📋 只處理YouTube數據，但保留與PTT的合併結果")
            else:
                self.logger.info("👥 階段3.5: 用戶數據處理...")
                
            try:
                from user_data_processor import process_legislators_data
                
                # 顯示重複用戶處理信息
                total_processed_users = sum(len(users) for users in processed_user_ids.values())
                if total_processed_users > 0:
                    self.logger.info(f"📋 重複用戶處理: 將跳過 {total_processed_users} 個已處理用戶")
                    for legislator, users in processed_user_ids.items():
                        if users:
                            self.logger.info(f"  - {legislator}: {len(users)} 個已處理用戶")
                
                # 傳遞processed_user_ids給用戶數據處理器
                user_data_success = process_legislators_data(
                    specific_legislators=legislators,
                    force_reprocess=False,
                    quiet=True,
                    processed_user_ids=processed_user_ids  # 新增參數
                )
                if not user_data_success:
                    self.logger.warning("⚠️ 用戶數據處理部分失敗，但繼續進行")
                else:
                    self.logger.info("✅ 用戶數據處理完成")
            except ImportError as e:
                self.logger.warning(f"⚠️ 無法導入用戶數據處理模組: {e}")
            except Exception as e:
                self.logger.error(f"❌ 用戶數據處理失敗: {e}")

            # 阶段4: 用户分析 (Gemini) - YouTube專用模式的特殊處理
            if youtube_only_analysis:
                self.logger.info("🤖 階段4: YouTube專用Gemini分析...")
                self.logger.info("📋 只分析YouTube評論，節省API成本")
                # 在這裡我們需要實現YouTube專用的Gemini分析
                try:
                    gemini_results = self._youtube_only_gemini_analysis(legislators)
                except Exception as e:
                    self.logger.error(f"❌ YouTube專用Gemini分析失败: {e}")
                    gemini_results = {'success': False}
            else:
                self.logger.info("🤖 階段4: Gemini用戶分析...")
                try:
                    from gemini_emo_user import analyze_legislators_emotions
                    gemini_results = analyze_legislators_emotions(
                        legislators=legislators, 
                        batch_size=500, 
                        quiet=False, 
                        incremental=True
                    )
                    # analyze_legislators_emotions 没有返回值，假设成功
                    gemini_results = {'success': True}
                except ImportError as e:
                    self.logger.warning(f"⚠️ 无法导入Gemini分析模块: {e}")
                    # 跳过Gemini分析，继续其他处理
                    gemini_results = {'success': True}
                except Exception as e:
                    self.logger.error(f"❌ Gemini分析失败: {e}")
                    gemini_results = {'success': False}
            
            if not gemini_results.get('success', False):
                self.logger.error("❌ Gemini分析失敗")
                return {'success': False, 'stage': 'gemini_analysis'}

            # 阶段5: 数据合并
            self.logger.info("🔗 階段5: 數據合併...")
            try:
                from gemini_emo_user import merge_temp_results
                # 確保temp目錄存在
                temp_dir = os.path.join(os.path.dirname(__file__), '..', 'temp')
                os.makedirs(temp_dir, exist_ok=True)
                
                for legislator in legislators:
                    try:
                        # 確保立委的temp子目錄存在
                        safe_name = legislator.replace(" ", "_")
                        temp_legislator_dir = os.path.join(temp_dir, safe_name)
                        os.makedirs(temp_legislator_dir, exist_ok=True)
                        
                        merge_temp_results(legislator)
                        self.logger.info(f"✅ {legislator} 數據合併完成")
                    except Exception as e:
                        self.logger.error(f"❌ {legislator} 數據合併失敗: {e}")
            except ImportError as e:
                self.logger.warning(f"⚠️ 无法导入数据合并模块: {e}")

            # 阶段6: MongoDB更新
            self.logger.info("💾 階段6: MongoDB更新...")
            mongo_updater = DataToMongo()
            final_results = mongo_updater.process_legislators(legislators)
            
            if final_results.get('success', False):
                mode_desc = "YouTube專用分析" if youtube_only_analysis else "完整分析"
                self.logger.info(f"🎉 所有數據處理階段完成！({mode_desc})")
                return {
                    'success': True,
                    'total_legislators': len(legislators),
                    'processed_legislators': final_results.get('processed_count', 0),
                    'total_records': final_results.get('total_records', 0),
                    'analysis_mode': 'youtube_only' if youtube_only_analysis else 'full',
                    'processed_users_count': total_processed_users
                }
            else:
                self.logger.error("❌ MongoDB更新失敗")
                return {'success': False, 'stage': 'mongodb_update'}
                
        except Exception as e:
            self.logger.error(f"❌ 數據處理失敗: {e}")
            return {'success': False, 'error': str(e)}

    def _youtube_only_gemini_analysis(self, legislators: List[str]) -> Dict:
        """YouTube專用的Gemini分析 - 只分析YouTube評論"""
        try:
            self.logger.info("🎬 開始YouTube專用Gemini分析...")
            
            # 這裡需要實現一個專門分析YouTube數據的邏輯
            # 暫時使用現有的分析函數，但只處理YouTube數據
            from gemini_emo_user import analyze_legislators_emotions
            
            # 調用現有的分析函數，但在實際實現中應該只分析YouTube數據
            analyze_legislators_emotions(
                legislators=legislators, 
                batch_size=500, 
                quiet=False, 
                incremental=True
            )
            
            self.logger.info("✅ YouTube專用Gemini分析完成")
            return {'success': True}
            
        except Exception as e:
            self.logger.error(f"❌ YouTube專用Gemini分析失敗: {e}")
            return {'success': False, 'error': str(e)}

    def _complete_data_processing(self, legislators: List[str], platforms: List[str], crawl_results: List[Dict], youtube_only_analysis: bool, processed_user_ids: Dict[str, set]) -> Dict:
        """完整的数据处理流程 - 阶段3-6"""
        try:
            # 统计爬取结果
            total_crawled = sum(result.get('count', 0) for result in crawl_results if result.get('status') == 'success')
            self.logger.info(f"📊 爬取完成: 總共 {total_crawled} 條數據")

            # 执行数据处理 (阶段3-6)
            return self._process_data_only(legislators, platforms, youtube_only_analysis, processed_user_ids)
            
        except Exception as e:
            self.logger.error(f"❌ 完整數據處理失敗: {e}")
            return {'success': False, 'error': str(e)}

    def _monitor_thread_health(self, thread_futures, max_workers):
        """
        监控线程健康状态，检测卡住的线程
        
        Args:
            thread_futures: 线程future字典
            max_workers: 最大工作线程数
        
        Returns:
            int: 卡住的线程数量
        """
        stuck_count = 0
        
        for future in thread_futures:
            if not future.done():
                # 检查线程运行时间
                if hasattr(future, '_start_time'):
                    elapsed = time.time() - future._start_time
                    if elapsed > self.max_thread_duration:
                        stuck_count += 1
                        self.logger.warning(f"⚠️ 检测到卡住线程，运行时间: {elapsed:.1f}秒")
        
        if stuck_count >= self.stuck_thread_threshold:
            self.logger.warning(f"⚠️ 卡住线程过多 ({stuck_count}/{max_workers})，建议替换代理")
            
            # 标记当前代理失败，获取新代理
            try:
                from proxy_manager import mark_proxy_failed, get_proxy_for_selenium
                proxy = get_proxy_for_selenium()
                if proxy:
                    mark_proxy_failed({'http': f'http://{proxy}', 'https': f'http://{proxy}'})
                    self.logger.info("🔄 已标记失败代理并获取新代理")
            except Exception as e:
                self.logger.error(f"⚠️ 代理替换失败: {e}")
        
        return stuck_count
    
    def _add_thread_start_time(self, future):
        """为future添加开始时间标记"""
        future._start_time = time.time()
        return future

def main():
    """測試函數"""
    logging.basicConfig(level=logging.INFO)
    
    manager = SimpleCrawlerManager()
    result = manager.crawl_all_legislators(['牛煦庭'], days=1, platforms=['youtube'])
    
    print(f"結果: {result}")

if __name__ == "__main__":
    main()
