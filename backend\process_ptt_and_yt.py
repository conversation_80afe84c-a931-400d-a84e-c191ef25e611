#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PTT和YouTube數據處理腳本
專門處理：PTT所有立委 + YouTube未處理立委 + 更新MongoDB
"""

import os
import sys
import logging
from typing import List

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 導入功能模組
try:
    from main_new import SocialMediaAnalyzer
    from gemini_analyzer import analyze_legislators_emotions
    from data_integrator import integrate_legislator_data
    from final_processor import process_all_legislators_final_data
except ImportError as e:
    print(f"❌ 模組導入失敗: {e}")
    print("請確保所有必要的模組文件都在正確的位置")
    sys.exit(1)

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('ptt_yt_process.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def process_ptt_all_legislators(analyzer: SocialMediaAnalyzer) -> bool:
    """處理PTT所有立委的數據"""
    logger.info("🔄 開始處理PTT所有立委數據...")
    
    try:
        # 獲取所有立委
        all_legislators = [leg['name'] for leg in analyzer.legislators]
        logger.info(f"📋 將處理 {len(all_legislators)} 位立委的PTT數據")
        
        # 執行PTT數據整合
        success_count = 0
        for legislator in all_legislators:
            try:
                logger.info(f"🔄 整合 {legislator} 的PTT數據...")
                result = integrate_legislator_data(legislator, analyzer.base_dir)
                if result:
                    success_count += 1
                    logger.info(f"✅ {legislator} PTT數據整合完成")
                else:
                    logger.warning(f"⚠️ {legislator} PTT數據整合失敗或無數據")
            except Exception as e:
                logger.error(f"❌ {legislator} PTT數據整合失敗: {e}")
        
        logger.info(f"🎉 PTT數據整合完成！成功處理 {success_count}/{len(all_legislators)} 位立委")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"❌ PTT數據處理失敗: {e}")
        return False

def process_youtube_unprocessed(analyzer: SocialMediaAnalyzer) -> bool:
    """處理YouTube未處理立委的數據"""
    logger.info("🔄 開始處理YouTube未處理立委數據...")
    
    try:
        # 獲取未處理的立委
        unprocessed = analyzer.get_unprocessed_legislators('youtube')
        logger.info(f"📋 找到 {len(unprocessed)} 位未處理的YouTube立委")
        
        if not unprocessed:
            logger.info("✅ 所有YouTube立委都已處理完成")
            return True
        
        # 執行YouTube數據整合
        success_count = 0
        for legislator in unprocessed:
            try:
                logger.info(f"🔄 整合 {legislator} 的YouTube數據...")
                result = integrate_legislator_data(legislator, analyzer.base_dir)
                if result:
                    success_count += 1
                    logger.info(f"✅ {legislator} YouTube數據整合完成")
                else:
                    logger.warning(f"⚠️ {legislator} YouTube數據整合失敗或無數據")
            except Exception as e:
                logger.error(f"❌ {legislator} YouTube數據整合失敗: {e}")
        
        logger.info(f"🎉 YouTube數據整合完成！成功處理 {success_count}/{len(unprocessed)} 位立委")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"❌ YouTube數據處理失敗: {e}")
        return False

def run_gemini_analysis(analyzer: SocialMediaAnalyzer, batch_size: int = 500) -> bool:
    """執行Gemini情感分析"""
    logger.info("🧠 開始執行Gemini情感分析...")
    
    try:
        # 獲取所有立委（因為PTT要全部分析，YouTube要分析未處理的）
        all_legislators = [leg['name'] for leg in analyzer.legislators]
        logger.info(f"📋 將分析 {len(all_legislators)} 位立委")
        
        success = analyze_legislators_emotions(
            legislators=all_legislators,
            batch_size=batch_size,
            quiet=False,
            incremental=True,
            base_dir=analyzer.base_dir
        )
        
        if success:
            logger.info(f"🎉 Gemini情感分析完成！")
        else:
            logger.warning(f"⚠️ Gemini情感分析部分失敗")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Gemini情感分析失敗: {e}")
        return False

def update_mongodb(analyzer: SocialMediaAnalyzer) -> bool:
    """更新MongoDB數據庫"""
    logger.info("🗄️ 開始更新MongoDB數據庫...")
    
    try:
        # 執行最終處理（會更新MongoDB）
        success = process_all_legislators_final_data(analyzer.base_dir)
        
        if success:
            logger.info(f"🎉 MongoDB更新完成！")
        else:
            logger.warning(f"⚠️ MongoDB更新部分失敗")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ MongoDB更新失敗: {e}")
        return False

def main():
    """主函數"""
    try:
        logger.info("🚀 開始執行PTT和YouTube數據處理流程...")
        
        # 初始化分析器
        analyzer = SocialMediaAnalyzer()
        
        # 顯示當前狀態
        logger.info("📊 當前系統狀態:")
        analyzer.show_detailed_status()
        
        # 步驟1: 處理PTT所有立委
        if not process_ptt_all_legislators(analyzer):
            logger.error("❌ PTT數據處理失敗，停止執行")
            return 1
        
        # 步驟2: 處理YouTube未處理立委
        if not process_youtube_unprocessed(analyzer):
            logger.error("❌ YouTube數據處理失敗，停止執行")
            return 1
        
        # 步驟3: 執行Gemini情感分析
        if not run_gemini_analysis(analyzer):
            logger.error("❌ Gemini情感分析失敗，停止執行")
            return 1
        
        # 步驟4: 更新MongoDB
        if not update_mongodb(analyzer):
            logger.error("❌ MongoDB更新失敗，停止執行")
            return 1
        
        logger.info("🎉 所有流程執行完成！")
        return 0
        
    except KeyboardInterrupt:
        logger.info("⏹️ 用戶中斷操作")
        return 1
    except Exception as e:
        logger.error(f"❌ 系統錯誤: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 