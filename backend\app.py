from flask import Flask, jsonify, request, abort
import json
import os
import requests
import threading
import time
from flask_cors import CORS
from src.legislator import legislator_app, ensure_database_indexes
from dotenv import load_dotenv
from config import get_config
#from cls_data_to_mongo import main as cls_data_to_mongo
import logging
from datetime import datetime
from src.visitor_counter import visitor_counter_bp  # 導入訪問計數器藍圖
from src.ai_chat_assistant import ai_chat_bp  # 導入新的AI聊天助手藍圖

# 設定爬蟲排程的日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 先載入環境變數
load_dotenv()

# 檢測是否在雲端環境中運行，並在創建 Flask 應用前設置環境
if 'RENDER' in os.environ or 'IS_RENDER' in os.environ:
    os.environ['IS_RENDER'] = 'true'
    # 在雲端環境中強制使用 production 配置
    os.environ['FLASK_CONFIG'] = 'production'
    logger.info("🚀 檢測到 Render 環境，強制設置為 production 配置")
else:
    logger.info(f"使用 {os.environ.get('FLASK_CONFIG', 'development')} 配置")

# 確保生產環境使用正確的 MongoDB Atlas URI
if os.environ.get('FLASK_CONFIG') == 'production':
    if not os.environ.get('MONGODB_ATLAS_URI') and not os.environ.get('MONGODB_URI'):
        logger.warning("⚠️ 警告: 生產環境未設置 MONGODB_ATLAS_URI 或 MONGODB_URI 環境變數")
    else:
        logger.info("✅ 生產環境 MongoDB Atlas 連接配置正常")

# 創建 Flask 應用並載入配置
app = Flask(__name__)
config_class = get_config()
app.config.from_object(config_class)

# 修復CORS配置 - 允許所有來源
CORS(app,
     origins=[
         "*",  # 允許所有來源（開發環境）
         "https://public-opinion-index.netlify.app",  # 正式環境前端
     ],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     allow_headers=['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
     supports_credentials=False)  # 改為False避免credentials問題

# 移除重複的CORS標頭設置，flask_cors已經處理了
# @app.after_request
# def after_request(response):
#     response.headers.add('Access-Control-Allow-Origin', '*')
#     response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
#     response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
#     return response

app.register_blueprint(legislator_app, url_prefix='/api/legislators')

# 註冊訪問統計藍圖
app.register_blueprint(visitor_counter_bp, url_prefix='/api/visitor')

# 註冊AI聊天助手藍圖
app.register_blueprint(ai_chat_bp)

@app.route('/')
def health_check():
    return jsonify({
        "status": "healthy",
        "message": "Legislative Recall Backend API is running",
        "endpoints": {
            "legislators": "/api/legislators",
            "legislator_detail": "/api/legislators/<name>",
            "legislator_unified_data": "/api/legislators/<name>/data",
            "recall_list": "/api/legislators/recall",
            "visitor_count": "/api/visitor_counter/count",
            "ai_chat": "/api/ai/chat",
            "ai_explain_word": "/api/ai/explain-word"
        },
        "unified_api_docs": {
            "endpoint": "/api/legislators/<name>/data",
            "description": "統一的立委數據API，支持時間篩選和數據類型選擇",
            "parameters": {
                "days": "天數 (預設365天)",
                "start_date": "開始日期 (可選，與days互斥)",
                "end_date": "結束日期 (可選，與start_date配合使用)",
                "include_pie_chart": "是否包含圓餅圖數據 (預設true)",
                "include_time_series": "是否包含時間序列數據 (預設true)",
                "include_word_cloud": "是否包含詞雲數據 (預設true)",
                "include_emotion": "是否包含情緒分析數據 (預設true)"
            },
            "example": "/api/legislators/洪孟楷/data?days=30&include_pie_chart=true"
        }
    })

@app.route('/health')
def health():
    return jsonify({"status": "healthy"})

if __name__ == '__main__':
    app.run(debug=True, port=5001)
