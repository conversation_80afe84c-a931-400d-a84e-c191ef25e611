{"name": "legislative-recall", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.12", "@angular/cdk": "^19.0.0", "@angular/common": "^19.2.12", "@angular/compiler": "^19.2.12", "@angular/core": "^19.2.12", "@angular/forms": "^19.2.12", "@angular/material": "^19.0.0", "@angular/platform-browser": "^19.2.12", "@angular/platform-browser-dynamic": "^19.2.12", "@angular/router": "^19.2.12", "@coreui/angular": "^5.2.13", "@coreui/angular-chartjs": "^5.2.0", "@coreui/chartjs": "^4.0.0", "@coreui/coreui": "^5.0.0", "@coreui/icons": "^3.0.1", "@coreui/icons-angular": "^5.0.0", "@svg-maps/taiwan": "^1.1.0", "angular-tag-cloud-module": "^19.0.0", "bootstrap-icons": "^1.13.1", "chart.js": "^4.4.0", "marked": "^16.1.1", "ng2-charts": "^5.0.0", "rxjs": "^7.8.1", "tslib": "^2.6.2", "zone.js": "^0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.13", "@angular/cli": "^19.2.13", "@angular/compiler-cli": "^19.2.12", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}}