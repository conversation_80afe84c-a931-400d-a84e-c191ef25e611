:host {
  display: block;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  margin-top: 70px; // 為固定導航欄留出空間
  
  @media (max-width: 768px) {
    margin-top: 70px;
    margin-bottom: 80px; // 為底部導航欄留出空間
  }
}

.footer {
  background: #333;
  color: white;
  padding: 2rem 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
  
  p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.8;
  }
  
  .visitor-stats-footer {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.8rem;
    opacity: 0.7;
  }
}


  .header {
    width: 100%;
    background: #f9f9f9;
    padding: 10px 0;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
  }
  
  .header-content {
    max-width: 1080px;
    margin: 0 auto;
    padding: 0 16px;
    text-align: center;
  }
  
  .header h1 {
    font-size: 20px;
    font-weight: 500;
    color: #444;
    margin: 0;
  }
  
  
  /* 讓整頁占滿視窗高度 */
html, body {
    height: 100%;
    margin: 0;
  }
  
  /* 讓整個 Angular 應用使用 flex 排版 */
  app-root {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .visitor-stats {
    font-size: 0.85rem;
    color: #fff;
    margin-top: 0.5rem;
    display: flex;
    gap: 1rem;
    
    .stat-item {
      display: inline-flex;
      align-items: center;
      
      i {
        margin-right: 4px;
      }
    }
  }

  .visitor-stats-footer {
    font-size: 0.8rem;
    color: #aaa;
    margin-top: 0.5rem;
    text-align: center;
  }



