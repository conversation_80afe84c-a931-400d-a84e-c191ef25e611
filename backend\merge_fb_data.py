#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合併fb_data到final_data的腳本
將fb_data中的使用者分析.json合併到現有的final_data中
"""

import json
import os
import glob
from pathlib import Path
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_json_file(file_path, encoding='utf-8'):
    """載入JSON檔案，處理編碼問題"""
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return json.load(f)
    except UnicodeDecodeError:
        # 如果UTF-8失敗，嘗試其他編碼
        try:
            with open(file_path, 'r', encoding='big5') as f:
                return json.load(f)
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"無法讀取檔案 {file_path}: {e}")
                return None

def save_json_file(data, file_path):
    """儲存JSON檔案"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"無法儲存檔案 {file_path}: {e}")
        return False

def get_legislator_name_from_filename(filename):
    """從檔案名稱提取立委姓名"""
    # 移除路徑和副檔名
    name = os.path.basename(filename)
    name = name.replace('_使用者分析.json', '')
    return name

def merge_fb_data_to_final_data():
    """合併fb_data到final_data"""
    
    # 路徑設定
    fb_data_dir = "./crawler/fb_data"
    final_data_dir = "./crawler/processed/final_data"
    
    # 檢查目錄是否存在
    if not os.path.exists(fb_data_dir):
        logger.error(f"fb_data目錄不存在: {fb_data_dir}")
        return
    
    if not os.path.exists(final_data_dir):
        logger.error(f"final_data目錄不存在: {final_data_dir}")
        return
    
    # 取得所有fb_data檔案
    fb_files = glob.glob(os.path.join(fb_data_dir, "*_使用者分析.json"))
    logger.info(f"找到 {len(fb_files)} 個fb_data檔案")
    
    # 取得所有final_data檔案
    final_files = glob.glob(os.path.join(final_data_dir, "*_使用者分析.json"))
    logger.info(f"找到 {len(final_files)} 個final_data檔案")
    
    # 建立final_data檔案名稱對應
    final_data_map = {}
    for file_path in final_files:
        legislator_name = get_legislator_name_from_filename(file_path)
        final_data_map[legislator_name] = file_path
    
    # 處理每個fb_data檔案
    for fb_file in fb_files:
        legislator_name = get_legislator_name_from_filename(fb_file)
        logger.info(f"處理立委: {legislator_name}")
        
        # 載入fb_data
        fb_data = load_json_file(fb_file)
        if fb_data is None:
            logger.warning(f"無法載入fb_data: {fb_file}")
            continue
        
        # 檢查是否有對應的final_data檔案
        if legislator_name in final_data_map:
            # 合併到現有的final_data
            final_file = final_data_map[legislator_name]
            logger.info(f"合併到現有檔案: {final_file}")
            
            # 載入現有的final_data
            final_data = load_json_file(final_file)
            if final_data is None:
                logger.warning(f"無法載入final_data: {final_file}")
                continue
            
            # 合併資料
            original_count = len(final_data)
            final_data.extend(fb_data)
            merged_count = len(final_data)
            
            logger.info(f"立委 {legislator_name}: 原本 {original_count} 筆，新增 {len(fb_data)} 筆，合併後 {merged_count} 筆")
            
            # 儲存合併後的資料
            if save_json_file(final_data, final_file):
                logger.info(f"成功儲存合併後的資料: {final_file}")
            else:
                logger.error(f"儲存失敗: {final_file}")
                
        else:
            # 建立新的final_data檔案
            new_final_file = os.path.join(final_data_dir, f"{legislator_name}_使用者分析.json")
            logger.info(f"建立新檔案: {new_final_file}")
            
            if save_json_file(fb_data, new_final_file):
                logger.info(f"成功建立新檔案: {new_final_file}")
            else:
                logger.error(f"建立檔案失敗: {new_final_file}")
    
    # 統計結果
    logger.info("合併完成！")
    logger.info(f"處理了 {len(fb_files)} 個fb_data檔案")
    logger.info(f"最終有 {len(glob.glob(os.path.join(final_data_dir, '*_使用者分析.json')))} 個final_data檔案")

def backup_fb_data():
    """備份fb_data目錄"""
    import shutil
    from datetime import datetime
    
    fb_data_dir = "backend/crawler/fb_data"
    backup_dir = f"backend/crawler/fb_data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if os.path.exists(fb_data_dir):
        try:
            shutil.copytree(fb_data_dir, backup_dir)
            logger.info(f"已備份fb_data到: {backup_dir}")
        except Exception as e:
            logger.error(f"備份失敗: {e}")

def remove_fb_data():
    """移除fb_data目錄（在合併完成後）"""
    import shutil
    
    fb_data_dir = "backend/crawler/fb_data"
    
    if os.path.exists(fb_data_dir):
        try:
            shutil.rmtree(fb_data_dir)
            logger.info(f"已移除fb_data目錄: {fb_data_dir}")
        except Exception as e:
            logger.error(f"移除失敗: {e}")

if __name__ == "__main__":
    print("開始合併fb_data到final_data...")
    
    # 詢問是否要備份
    backup_choice = input("是否要備份fb_data目錄？(y/n): ").lower().strip()
    if backup_choice == 'y':
        backup_fb_data()
    
    # 執行合併
    merge_fb_data_to_final_data()
    
    # 詢問是否要移除fb_data
    remove_choice = input("合併完成後是否要移除fb_data目錄？(y/n): ").lower().strip()
    if remove_choice == 'y':
        remove_fb_data()
    
    print("合併程序完成！") 