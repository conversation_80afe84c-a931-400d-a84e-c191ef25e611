#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gemini API 測試腳本
驗證 API 配置是否正確
"""

import os
import json
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_gemini_api():
    """測試 Gemini API"""
    print("🔍 測試 Gemini API 配置...")
    
    try:
        # 測試 API key 加載
        print("\n📋 測試1: API Key 加載")
        from crawler.gemini_emo_user import load_api_keys
        
        api_keys = load_api_keys()
        if not api_keys:
            print("❌ 沒有找到任何 API keys")
            return False
        
        print(f"✅ 成功加載 {len(api_keys)} 個 API keys")
        
        # 測試 Gemini 初始化
        print("\n📋 測試2: Gemini 模型初始化")
        
        try:
            import google.generativeai as genai
            
            # 使用第一個 API key 測試
            test_api_key = api_keys[0]
            print(f"🔑 使用 API key: {test_api_key[:10]}...")
            
            genai.configure(api_key=test_api_key)
            model = genai.GenerativeModel('gemini-1.5-flash')
            
            print("✅ Gemini 模型初始化成功")
            
            # 測試簡單調用
            print("\n📋 測試3: 簡單 API 調用")
            
            test_prompt = "Hello, please respond with just 'API Working' if you can understand this message."
            
            response = model.generate_content(test_prompt)
            
            if response is None:
                print("❌ API 返回 None")
                return False
            
            if not hasattr(response, 'text'):
                print("❌ Response 沒有 text 屬性")
                return False
            
            if not response.text:
                print("❌ Response text 為空")
                return False
            
            response_text = response.text.strip()
            print(f"✅ API 調用成功")
            print(f"📤 回應: {response_text}")
            
            if "API Working" in response_text or "working" in response_text.lower():
                print("✅ API 功能正常")
                return True
            else:
                print("⚠️ API 有回應但內容不符預期")
                return True  # 仍然算成功，因為有回應
                
        except Exception as e:
            print(f"❌ Gemini 測試失敗: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 測試過程出錯: {e}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def test_multiple_keys():
    """測試多個 API keys"""
    print("\n🔍 測試多個 API keys...")
    
    try:
        from crawler.gemini_emo_user import load_api_keys
        import google.generativeai as genai
        
        api_keys = load_api_keys()
        working_keys = 0
        
        for i, api_key in enumerate(api_keys[:5]):  # 只測試前5個
            try:
                print(f"🔑 測試 API key {i+1}/5: {api_key[:10]}...")
                genai.configure(api_key=api_key)
                model = genai.GenerativeModel('gemini-1.5-flash')
                
                response = model.generate_content("Test")
                if response and hasattr(response, 'text') and response.text:
                    working_keys += 1
                    print(f"✅ API key {i+1} 可用")
                else:
                    print(f"❌ API key {i+1} 無回應")
                    
            except Exception as e:
                print(f"❌ API key {i+1} 失敗: {e}")
        
        print(f"\n📊 結果: {working_keys}/{min(5, len(api_keys))} 個 API keys 可用")
        return working_keys > 0
        
    except Exception as e:
        print(f"❌ 多 API key 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始 Gemini API 診斷...")
    print("=" * 50)
    
    # 基本測試
    basic_ok = test_gemini_api()
    
    # 多 API key 測試
    multi_ok = test_multiple_keys()
    
    # 總結
    print("\n" + "=" * 50)
    print("🎯 診斷總結:")
    print(f"✅ 基本 API 測試: {'通過' if basic_ok else '失敗'}")
    print(f"✅ 多 API key 測試: {'通過' if multi_ok else '失敗'}")
    
    if basic_ok and multi_ok:
        print("\n💡 結論: Gemini API 配置正常，可以繼續運行爬蟲")
        return True
    elif basic_ok:
        print("\n💡 結論: 基本功能正常，但部分 API key 可能有問題")
        return True
    else:
        print("\n💡 結論: Gemini API 配置有問題，需要檢查:")
        print("   1. API keys 是否有效")
        print("   2. 網路連線是否正常")
        print("   3. Gemini API 服務是否可用")
        return False

if __name__ == "__main__":
    main() 