#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
優化版 MongoDB 更新器
解決 MongoDB + multiprocessing 的性能瓶頸問題
"""

import os
import json
import sys
import traceback
import re
import jieba
import jieba.posseg as pseg
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from pymongo import MongoClient, UpdateOne, InsertOne
from typing import Dict, List, Any, Optional, Tuple
import logging
from multiprocessing import Pool, Manager
import threading
from functools import partial

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 設置日誌
logger = logging.getLogger(__name__)

class OptimizedMongoUpdater:
    """優化版 MongoDB 更新器 - 解決連線競爭和寫入瓶頸"""
    
    def __init__(self, use_connection_pool=True):
        """
        初始化優化版更新器
        
        Args:
            use_connection_pool: 是否使用連線池
        """
        self.use_connection_pool = use_connection_pool
        self._init_connections()
        self._initialize_optimization()
        
    def _init_connections(self):
        """初始化 MongoDB 連線 - 使用連線池"""
        try:
            # 本地 MongoDB 連線 - 使用連線池
            if self.use_connection_pool:
                self.local_client = MongoClient(
                    'mongodb://localhost:27017',
                    maxPoolSize=10,  # 連線池大小
                    minPoolSize=2,   # 最小連線數
                    maxIdleTimeMS=30000,  # 最大空閒時間
                    serverSelectionTimeoutMS=5000
                )
            else:
                self.local_client = MongoClient('mongodb://localhost:27017')
            
            self.local_db = self.local_client['legislator_recall']
            self.local_crawler_data = self.local_db.crawler_data
            self.local_legislators = self.local_db.legislators
            
            # Atlas MongoDB 連線
            atlas_uri = os.getenv('MONGODB_ATLAS_URI')
            atlas_dbname = os.getenv('MONGODB_ATLAS_DBNAME', 'legislator_recall')
            
            if atlas_uri:
                try:
                    atlas_uri = atlas_uri.strip()
                    if self.use_connection_pool:
                        self.atlas_client = MongoClient(
                            atlas_uri,
                            maxPoolSize=5,
                            minPoolSize=1,
                            maxIdleTimeMS=30000,
                            serverSelectionTimeoutMS=5000
                        )
                    else:
                        self.atlas_client = MongoClient(atlas_uri)
                    
                    self.atlas_db = self.atlas_client[atlas_dbname]
                    self.atlas_crawler_data = self.atlas_db.crawler_data
                    self.atlas_legislators = self.atlas_db.legislators
                    self.atlas_connected = True
                    logger.info("✅ Atlas MongoDB 連線池連接成功")
                except Exception as e:
                    logger.warning(f"⚠️ Atlas MongoDB 連接失敗: {e}")
                    self.atlas_connected = False
            else:
                logger.warning("⚠️ 未設置 MONGODB_ATLAS_URI，跳過 Atlas 更新")
                self.atlas_connected = False
            
            logger.info("✅ 本地 MongoDB 連線池連接成功")
            
        except Exception as e:
            logger.error(f"❌ MongoDB 連接失敗: {e}")
            raise
    
    def _initialize_optimization(self):
        """初始化優化功能"""
        # 載入停用詞
        self.stopwords = self._load_stopwords()
        
        # 載入人名詞典
        self.person_names = self._load_person_names()
        
        # 初始化 jieba
        self._initialize_jieba()
        
        # 初始化批量寫入緩衝區
        self.bulk_buffer_size = 1000  # 批量寫入大小
        self.bulk_operations = []
        
        logger.info("✅ 優化功能初始化完成")
    
    def _load_stopwords(self) -> set:
        """載入停用詞"""
        stopwords = set()
        
        # 從文件載入停用詞
        stopwords_file = os.path.join(current_dir, "data", "stopwords.txt")
        if os.path.exists(stopwords_file):
            try:
                with open(stopwords_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            stopwords.add(line)
                logger.info(f"✅ 從文件載入 {len(stopwords)} 個停用詞")
            except Exception as e:
                logger.warning(f"⚠️ 載入停用詞文件失敗: {e}")
        
        return stopwords
    
    def _load_person_names(self) -> set:
        """載入人名詞典"""
        person_names = set()
        
        # 從 user_dict.txt 載入人名
        user_dict_file = os.path.join(current_dir, "data", "user_dict.txt")
        if os.path.exists(user_dict_file):
            try:
                with open(user_dict_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            # 解析格式: "姓名 詞頻 詞性"
                            parts = line.split()
                            if len(parts) >= 1:
                                name = parts[0]
                                person_names.add(name)
                logger.info(f"✅ 從文件載入 {len(person_names)} 個人名")
            except Exception as e:
                logger.warning(f"⚠️ 載入人名文件失敗: {e}")
        
        return person_names
    
    def _initialize_jieba(self):
        """初始化 jieba 分詞"""
        # 添加人名到詞典
        for name in self.person_names:
            jieba.add_word(name)
    
    def _should_keep_word(self, word: str, flag: str) -> bool:
        """結合停用詞和詞性判斷是否保留詞彙 - 改進版"""
        # 基本長度檢查
        if len(word) < 2:
            return False
        
        # 停用詞檢查
        if word in self.stopwords:
            return False
        
        # 詞性檢查 - 保留名詞、動詞、形容詞
        valid_flags = {'n', 'nr', 'ns', 'nt', 'nz', 'vn', 'v', 'a', 'an'}
        if flag in valid_flags:
            return True
        
        return False
    
    def _is_meaningful_word(self, word: str, freq: int, total_words: int) -> bool:
        """判斷詞彙是否有意義 - 改進版智能過濾"""
        # 過於稀少的詞彙（少於2次）可能是噪音
        if freq < 2:
            return False
        
        # 過於通用的詞彙 - 只過濾真正無意義的詞彙
        generic_words = {
            '今天', '明天', '昨天', '現在', '時候', '時間', '地方', '事情',
            '問題', '情況', '方面', '部分', '內容', '結果', '原因', '目的',
            '同意', '反對', '支持', '反對', '認為', '覺得', '知道', '了解',
            '這個', '那個', '什麼', '怎麼', '為什麼', '因為', '所以', '但是',
            '然後', '最後', '開始', '結束', '完成', '進行', '繼續', '停止'
        }
        
        if word in generic_words:
            return False
        
        return True
    
    def _merge_similar_words(self, word_freq: Counter) -> Counter:
        """合併相似詞彙 - 新增智能合併"""
        merged_freq = Counter()
        processed_words = set()
        
        for word, freq in word_freq.items():
            if word in processed_words:
                continue
            
            # 檢查是否有相似的人名
            similar_words = []
            for other_word, other_freq in word_freq.items():
                if other_word != word and other_word not in processed_words:
                    # 檢查是否為人名的一部分
                    if word in other_word or other_word in word:
                        if len(word) >= 2 and len(other_word) >= 2:
                            similar_words.append(other_word)
            
            # 合併相似詞彙
            if similar_words:
                total_freq = freq
                for similar_word in similar_words:
                    total_freq += word_freq[similar_word]
                    processed_words.add(similar_word)
                
                # 使用最長的詞作為代表
                representative = max([word] + similar_words, key=len)
                merged_freq[representative] = total_freq
                processed_words.add(word)
            else:
                merged_freq[word] = freq
                processed_words.add(word)
        
        return merged_freq
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除特殊字符
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', text)
        # 移除多餘空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _analyze_word_semantics(self, word: str, freq: int, all_words: Counter) -> float:
        """分析詞彙語義豐富度 - 動態計算"""
        semantic_score = 0
        
        # 詞彙稀有度分析（在整個詞彙集中出現的頻率）
        total_unique_words = len(all_words)
        word_rank = sorted(all_words.values(), reverse=True).index(freq) + 1
        rarity_score = 1 - (word_rank / total_unique_words) if total_unique_words > 0 else 0
        semantic_score += rarity_score * 0.1
        
        # 詞彙長度分析（長詞通常語義更豐富）
        if len(word) >= 4:
            semantic_score += 0.05
        elif len(word) >= 3:
            semantic_score += 0.03
        
        # 詞彙穩定性分析（適中頻率的詞彙更有意義）
        if 2 <= freq <= 8:
            semantic_score += 0.05
        elif freq > 8:
            semantic_score += 0.02
        
        return semantic_score
    
    def _calculate_word_importance(self, word: str, freq: int, total_words: int, all_words: Counter) -> float:
        """計算詞彙重要性分數 - 智能評分"""
        # 基礎分數
        base_score = freq / total_words if total_words > 0 else 0
        
        # 詞彙長度加分（長詞通常更有意義）
        length_bonus = min(0.1, len(word) * 0.02)
        
        # 詞性加分
        pos_bonus = 0
        if word in self.person_names:
            pos_bonus = 0.2  # 人名加分
        elif len(word) >= 4:
            pos_bonus = 0.1  # 長詞加分
        
        # 詞彙多樣性加分（在停用詞中沒有的詞彙加分）
        diversity_bonus = 0
        if word not in self.stopwords and len(word) >= 3:
            diversity_bonus = 0.05
        
        # 詞彙穩定性加分（頻率適中的詞彙加分）
        stability_bonus = 0
        if 2 <= freq <= 10:  # 適中頻率
            stability_bonus = 0.05
        
        # 語義豐富度分析
        semantic_bonus = self._analyze_word_semantics(word, freq, all_words)
        
        return base_score + length_bonus + pos_bonus + diversity_bonus + stability_bonus + semantic_bonus
    
    def _generate_wordcloud_optimized(self, comments: List[str]) -> List[Dict[str, Any]]:
        """優化版詞雲生成 - 使用智能過濾和重要性評分"""
        if not comments:
            return []
        
        # 批量文本清理
        cleaned_comments = [self._clean_text(comment) for comment in comments if comment]
        
        # 批量分詞處理
        word_freq = Counter()
        for comment in cleaned_comments:
            if comment:
                words = pseg.cut(comment)
                for word, flag in words:
                    if self._should_keep_word(word, flag):
                        word_freq[word] += 1
        
        # 智能合併相似詞彙
        word_freq = self._merge_similar_words(word_freq)
        
        # 計算總詞數
        total_words = sum(word_freq.values())
        
        # 智能過濾無意義詞彙
        meaningful_words = {}
        for word, freq in word_freq.items():
            if self._is_meaningful_word(word, freq, total_words):
                meaningful_words[word] = freq
        
        # 計算重要性分數並排序
        word_scores = []
        for word, freq in meaningful_words.items():
            importance = self._calculate_word_importance(word, freq, total_words, word_freq)
            word_scores.append((word, freq, importance))
        
        # 按重要性分數排序
        word_scores.sort(key=lambda x: x[2], reverse=True)
        
        # 轉換為結果格式
        result = []
        for word, freq, importance in word_scores[:30]:  # 取前30個詞
            # 智能計算字體大小
            if importance > 0.1:
                size = min(80, max(20, int(importance * 400)))
            elif importance > 0.05:
                size = min(60, max(16, int(importance * 300)))
            else:
                size = min(40, max(12, int(importance * 200)))
            
            result.append({
                'word': word,
                'frequency': freq,
                'size': size,
                'importance': round(importance, 4)
            })
        
        return result
    
    def update_legislator_data(self, legislator_name: str) -> Dict[str, Any]:
        """更新單個立委數據 - 優化版"""
        try:
            start_time = datetime.now()
            
            # 1. 讀取 final_data
            final_data_file = f"crawler/processed/final_data/{legislator_name}_使用者分析.json"
            if not os.path.exists(final_data_file):
                return {
                    'success': False,
                    'error': f"找不到 {legislator_name} 的 final_data 文件"
                }
            
            with open(final_data_file, 'r', encoding='utf-8') as f:
                analysis_data = json.load(f)
            
            # 2. 批量更新 crawler_data
            crawler_result = self._update_crawler_data_bulk(legislator_name, analysis_data)
            
            # 3. 生成統計數據
            stats_result = self._generate_legislator_stats_optimized(legislator_name)
            
            # 4. 批量更新 legislators 集合
            legislators_result = self._update_legislators_collection_bulk(legislator_name, stats_result)
            
            # 5. 執行剩餘的批量操作
            self._flush_bulk_operations()
            
            total_time = datetime.now() - start_time
            
            return {
                'success': True,
                'legislator': legislator_name,
                'crawler_update': crawler_result,
                'stats_generation': stats_result,
                'legislators_update': legislators_result,
                'processing_time': total_time.total_seconds()
            }
            
        except Exception as e:
            logger.error(f"❌ 更新 {legislator_name} 失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
    
    def _update_crawler_data_bulk(self, legislator_name: str, analysis_data: List[Dict]) -> Dict[str, Any]:
        """批量更新 crawler_data - 優化版"""
        try:
            # 準備批量操作
            bulk_ops = []
            
            for record in analysis_data:
                # 確保記錄有基本字段
                if not isinstance(record, dict):
                    continue
                
                # 移除 wordcloud - 這應該只在 legislators 集合中
                if 'wordcloud' in record:
                    del record['wordcloud']
                
                # 確保必要字段存在
                record['legislator'] = legislator_name
                record['updated_at'] = datetime.now()
                
                # 安全設置數值字段
                record['likes'] = record.get('likes', 0) or 0
                record['shares'] = record.get('shares', 0) or 0
                record['sentiment_score'] = record.get('sentiment_score', 0) or 0
                record['date'] = record.get('date', datetime.now().strftime('%Y-%m-%d'))
                record['platform'] = record.get('platform', 'unknown')
                
                # 準備更新操作
                filter_criteria = {
                    'legislator': legislator_name,
                    'date': record.get('date'),
                    'platform': record.get('platform')
                }
                
                # 移除 None 值，避免 MongoDB 錯誤
                clean_record = {}
                for key, value in record.items():
                    if value is not None:
                        clean_record[key] = value
                
                bulk_ops.append(UpdateOne(
                    filter_criteria,
                    {'$set': clean_record},
                    upsert=True
                ))
            
            # 執行批量寫入
            if bulk_ops:
                result = self.local_crawler_data.bulk_write(bulk_ops, ordered=False)
                
                return {
                    'success': True,
                    'upserted_count': result.upserted_count,
                    'modified_count': result.modified_count,
                    'total_operations': len(bulk_ops)
                }
            else:
                return {
                    'success': True,
                    'upserted_count': 0,
                    'modified_count': 0,
                    'total_operations': 0
                }
                
        except Exception as e:
            logger.error(f"❌ 批量更新 {legislator_name} crawler_data 失敗: {e}")
            import traceback
            logger.error(f"詳細錯誤: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_legislator_stats_optimized(self, legislator_name: str) -> Dict[str, Any]:
        """優化版立委統計生成"""
        try:
            # 從 crawler_data 讀取數據
            pipeline = [
                {'$match': {'legislator': legislator_name}},
                {'$group': {
                    '_id': {
                        'date': '$date',
                        'platform': '$platform'
                    },
                    'total_comments': {'$sum': 1},
                    'total_likes': {'$sum': {'$ifNull': ['$likes', 0]}},
                    'total_shares': {'$sum': {'$ifNull': ['$shares', 0]}},
                    'avg_sentiment': {'$avg': {'$ifNull': ['$sentiment_score', 0]}},
                    'comments': {'$push': '$comments'}
                }},
                {'$sort': {'_id.date': 1}}
            ]
            
            stats_data = list(self.local_crawler_data.aggregate(pipeline))
            
            # 生成統計數據
            result = {
                'legislator': legislator_name,
                'total_records': len(stats_data),
                'date_stats': {},
                'platform_stats': {},
                'overall_stats': {
                    'total_comments': 0,
                    'total_likes': 0,
                    'total_shares': 0,
                    'avg_sentiment': 0
                }
            }
            
            # 處理統計數據
            for stat in stats_data:
                # 安全獲取日期和平台，避免 None 值
                date = stat['_id'].get('date')
                platform = stat['_id'].get('platform')
                
                # 如果日期或平台為 None，使用默認值
                if date is None:
                    date = 'unknown_date'
                if platform is None:
                    platform = 'unknown_platform'
                
                # 安全獲取數值，避免 None 值
                total_comments = stat.get('total_comments', 0) or 0
                total_likes = stat.get('total_likes', 0) or 0
                total_shares = stat.get('total_shares', 0) or 0
                avg_sentiment = stat.get('avg_sentiment', 0) or 0
                
                # 日期統計
                if date not in result['date_stats']:
                    result['date_stats'][date] = {
                        'total_comments': 0,
                        'total_likes': 0,
                        'total_shares': 0,
                        'platforms': {}
                    }
                
                result['date_stats'][date]['total_comments'] += total_comments
                result['date_stats'][date]['total_likes'] += total_likes
                result['date_stats'][date]['total_shares'] += total_shares
                result['date_stats'][date]['platforms'][platform] = {
                    'comments': total_comments,
                    'likes': total_likes,
                    'shares': total_shares,
                    'avg_sentiment': avg_sentiment
                }
                
                # 平台統計
                if platform not in result['platform_stats']:
                    result['platform_stats'][platform] = {
                        'total_comments': 0,
                        'total_likes': 0,
                        'total_shares': 0,
                        'avg_sentiment': 0
                    }
                
                result['platform_stats'][platform]['total_comments'] += total_comments
                result['platform_stats'][platform]['total_likes'] += total_likes
                result['platform_stats'][platform]['total_shares'] += total_shares
                
                # 總體統計
                result['overall_stats']['total_comments'] += total_comments
                result['overall_stats']['total_likes'] += total_likes
                result['overall_stats']['total_shares'] += total_shares
            
            # 安全計算平均值
            if result['overall_stats']['total_comments'] > 0:
                weighted_sum = 0
                total_weight = 0
                
                for stat in stats_data:
                    sentiment = stat.get('avg_sentiment', 0) or 0
                    comments = stat.get('total_comments', 0) or 0
                    
                    if sentiment is not None and comments is not None:
                        weighted_sum += sentiment * comments
                        total_weight += comments
                
                if total_weight > 0:
                    result['overall_stats']['avg_sentiment'] = weighted_sum / total_weight
                else:
                    result['overall_stats']['avg_sentiment'] = 0
            else:
                result['overall_stats']['avg_sentiment'] = 0
            
            # 計算各平台平均值
            for platform in result['platform_stats']:
                platform_comments = result['platform_stats'][platform]['total_comments']
                if platform_comments > 0:
                    weighted_sum = 0
                    total_weight = 0
                    
                    for stat in stats_data:
                        stat_platform = stat['_id'].get('platform')
                        if stat_platform is None:
                            stat_platform = 'unknown_platform'
                        
                        if stat_platform == platform:
                            sentiment = stat.get('avg_sentiment', 0) or 0
                            comments = stat.get('total_comments', 0) or 0
                            
                            if sentiment is not None and comments is not None:
                                weighted_sum += sentiment * comments
                                total_weight += comments
                    
                    if total_weight > 0:
                        result['platform_stats'][platform]['avg_sentiment'] = weighted_sum / total_weight
                    else:
                        result['platform_stats'][platform]['avg_sentiment'] = 0
                else:
                    result['platform_stats'][platform]['avg_sentiment'] = 0
            
            # 生成 _daily 時間序列數據
            result['time_series_stats'] = self._generate_daily_time_series_stats(legislator_name)
            
            result['updated_at'] = datetime.now()
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 生成 {legislator_name} 統計數據失敗: {e}")
            import traceback
            logger.error(f"詳細錯誤: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_daily_time_series_stats(self, legislator_name: str) -> Dict[str, Any]:
        """生成 _daily 時間序列統計數據 - 直接從 crawler_data 查詢"""
        try:
            # 生成不同時間範圍的統計
            time_series_stats = {}
            
            # 1. 最近7天 (累積) - 每天一個點，共7個點
            recent_7_days_cumulative = self._generate_time_range_stats(legislator_name, 7, 1, "最近7天 (累積)", cumulative=True)
            time_series_stats['recent_7_days_cumulative'] = recent_7_days_cumulative
            
            # 2. 最近14天 (累積) - 每2天一個點，共7個點
            recent_14_days_cumulative = self._generate_time_range_stats(legislator_name, 14, 2, "最近14天 (累積)", cumulative=True)
            time_series_stats['recent_14_days_cumulative'] = recent_14_days_cumulative
            
            # 3. 最近30天 (累積) - 每5天一個點，共6個點
            recent_30_days_cumulative = self._generate_time_range_stats(legislator_name, 30, 5, "最近30天 (累積)", cumulative=True)
            time_series_stats['recent_30_days_cumulative'] = recent_30_days_cumulative
            
            # 4. 最近90天 (累積) - 每14天一個點，共6-7個點
            recent_90_days_cumulative = self._generate_time_range_stats(legislator_name, 90, 14, "最近90天 (累積)", cumulative=True)
            time_series_stats['recent_90_days_cumulative'] = recent_90_days_cumulative
            
            # 5. 最近180天 (累積) - 每21天一個點，共8-9個點
            recent_180_days_cumulative = self._generate_time_range_stats(legislator_name, 180, 21, "最近180天 (累積)", cumulative=True)
            time_series_stats['recent_180_days_cumulative'] = recent_180_days_cumulative
            
            # 6. 最近365天 (累積) - 每30天一個點，共12個點
            recent_365_days_cumulative = self._generate_time_range_stats(legislator_name, 365, 30, "最近365天 (累積)", cumulative=True)
            time_series_stats['recent_365_days_cumulative'] = recent_365_days_cumulative
            
            # 每日統計 - 統計指定時間範圍內的每日數據
            # 7. 最近7天 (每日) - 統計這段時間的每日數據
            recent_7_days_daily = self._generate_time_range_stats(legislator_name, 7, 1, "最近7天 (每日)", cumulative=False)
            time_series_stats['recent_7_days_daily'] = recent_7_days_daily
            
            # 8. 最近14天 (每日) - 統計這段時間的每日數據
            recent_14_days_daily = self._generate_time_range_stats(legislator_name, 14, 2, "最近14天 (每日)", cumulative=False)
            time_series_stats['recent_14_days_daily'] = recent_14_days_daily
            
            # 9. 最近30天 (每日) - 統計這段時間的每日數據
            recent_30_days_daily = self._generate_time_range_stats(legislator_name, 30, 5, "最近30天 (每日)", cumulative=False)
            time_series_stats['recent_30_days_daily'] = recent_30_days_daily
            
            # 10. 最近90天 (每日) - 統計這段時間的每日數據
            recent_90_days_daily = self._generate_time_range_stats(legislator_name, 90, 14, "最近90天 (每日)", cumulative=False)
            time_series_stats['recent_90_days_daily'] = recent_90_days_daily
            
            # 11. 最近180天 (每日) - 統計這段時間的每日數據
            recent_180_days_daily = self._generate_time_range_stats(legislator_name, 180, 21, "最近180天 (每日)", cumulative=False)
            time_series_stats['recent_180_days_daily'] = recent_180_days_daily
            
            # 12. 最近365天 (每日) - 統計這段時間的每日數據
            recent_365_days_daily = self._generate_time_range_stats(legislator_name, 365, 30, "最近365天 (每日)", cumulative=False)
            time_series_stats['recent_365_days_daily'] = recent_365_days_daily
            
            return time_series_stats
            
        except Exception as e:
            logger.error(f"❌ 生成 {legislator_name} 時間序列統計失敗: {e}")
            return {}
    
    def _generate_time_range_stats(self, legislator_name: str, days: int, interval_days: int, description: str, cumulative: bool = True) -> Dict[str, Any]:
        """生成指定時間範圍的統計數據 - 直接從 crawler_data 查詢"""
        try:
            # 計算結束日期（今天）
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 直接從 crawler_data 查詢指定時間範圍的數據
            pipeline = [
                {
                    '$match': {
                        'legislator': legislator_name,
                        'date': {
                            '$gte': start_date.strftime('%Y-%m-%d'),
                            '$lte': end_date.strftime('%Y-%m-%d')
                        }
                    }
                },
                {
                    '$group': {
                        '_id': {
                            'date': '$date',
                            'platform': '$platform'
                        },
                        'total_comments': {'$sum': 1},
                        'total_likes': {'$sum': {'$ifNull': ['$likes', 0]}},
                        'total_shares': {'$sum': {'$ifNull': ['$shares', 0]}},
                        'avg_sentiment': {'$avg': {'$ifNull': ['$sentiment_score', 0]}}
                    }
                },
                {'$sort': {'_id.date': 1}}
            ]
            
            filtered_stats = list(self.local_crawler_data.aggregate(pipeline))
            
            # 按日期分組並統計
            date_groups = {}
            
            if cumulative:
                # 累積模式：累加所有數據
                cumulative_support = 0
                cumulative_oppose = 0
                
                for stat in filtered_stats:
                    date_str = stat['_id'].get('date')
                    if date_str:
                        # 計算情感分析
                        sentiment_score = stat.get('avg_sentiment', 0) or 0
                        comments_count = stat.get('total_comments', 0) or 0
                        
                        # 基於情感分數計算支持和反對
                        if sentiment_score > 0:
                            oppose_count = int(comments_count * 0.6)  # 正面情感 = 反對罷免
                            support_count = int(comments_count * 0.4)  # 負面情感 = 支持罷免
                        else:
                            oppose_count = int(comments_count * 0.4)  # 負面情感 = 反對罷免
                            support_count = int(comments_count * 0.6)  # 正面情感 = 支持罷免
                        
                        cumulative_support += support_count
                        cumulative_oppose += oppose_count
                        
                        date_groups[date_str] = {
                            'date': date_str,
                            'sentiment_counts': {
                                'support': cumulative_support,
                                'oppose': cumulative_oppose
                            }
                        }
            else:
                # 每日模式：統計每日的獨立數據
                for stat in filtered_stats:
                    date_str = stat['_id'].get('date')
                    if date_str:
                        # 計算情感分析
                        sentiment_score = stat.get('avg_sentiment', 0) or 0
                        comments_count = stat.get('total_comments', 0) or 0
                        
                        # 基於情感分數計算支持和反對
                        if sentiment_score > 0:
                            oppose_count = int(comments_count * 0.6)  # 正面情感 = 反對罷免
                            support_count = int(comments_count * 0.4)  # 負面情感 = 支持罷免
                        else:
                            oppose_count = int(comments_count * 0.4)  # 負面情感 = 反對罷免
                            support_count = int(comments_count * 0.6)  # 正面情感 = 支持罷免
                        
                        date_groups[date_str] = {
                            'date': date_str,
                            'sentiment_counts': {
                                'support': support_count,
                                'oppose': oppose_count
                            }
                        }
            
            # 生成完整的時間序列點（包括沒有數據的日期）
            stats_points = []
            
            # 生成從開始到結束的所有日期
            current_date = start_date
            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                
                if date_str in date_groups:
                    # 有數據的日期
                    stats_points.append(date_groups[date_str])
                else:
                    # 沒有數據的日期，使用前一個累積值或0
                    if cumulative and stats_points:
                        # 累積模式：使用前一個點的值
                        prev_point = stats_points[-1]
                        stats_points.append({
                            'date': date_str,
                            'sentiment_counts': {
                                'support': prev_point['sentiment_counts']['support'],
                                'oppose': prev_point['sentiment_counts']['oppose']
                            }
                        })
                    else:
                        # 每日模式或第一個點：使用0
                        stats_points.append({
                            'date': date_str,
                            'sentiment_counts': {
                                'support': 0,
                                'oppose': 0
                            }
                        })
                
                current_date += timedelta(days=1)
            
            # 按間隔取點（確保點數正確）
            if stats_points:
                # 計算間隔
                total_points = len(stats_points)
                if total_points <= 7:
                    # 如果總點數少於等於7，全部保留
                    final_points = stats_points
                else:
                    # 如果總點數多於7，按間隔取點
                    step = max(1, total_points // 7)
                    final_points = []
                    for i in range(0, total_points, step):
                        if len(final_points) < 7:  # 最多7個點
                            final_points.append(stats_points[i])
                    
                    # 確保包含最後一個點
                    if stats_points and stats_points[-1] not in final_points:
                        final_points.append(stats_points[-1])
                
                stats_points = final_points
            
            return {
                'description': description,
                'stats_points': stats_points,
                'total_points': len(stats_points)
            }
            
        except Exception as e:
            logger.error(f"❌ 生成時間範圍統計失敗: {e}")
            return {
                'description': description,
                'stats_points': [],
                'total_points': 0
            }
    
    def _update_legislators_collection_bulk(self, legislator_name: str, stats_data: Dict) -> Dict[str, Any]:
        """批量更新 legislators 集合 - 更新 emotion_analysis 和 time_series_stats 字段"""
        try:
            # 從 stats_data 中提取 emotion_analysis 數據
            # 這裡我們需要從 crawler_data 中重新計算 emotion_analysis
            emotion_analysis = self._generate_emotion_analysis(legislator_name)
            
            # 準備更新操作 - 更新 emotion_analysis、time_series_stats 和 updated_at 字段
            update_data = {
                'emotion_analysis': emotion_analysis,
                'updated_at': datetime.now()
            }
            
            # 如果 stats_data 包含 time_series_stats，也更新它
            if 'time_series_stats' in stats_data:
                update_data['time_series_stats'] = stats_data['time_series_stats']
            
            # 添加到批量操作緩衝區 - 使用 name 字段查詢，不創建新記錄
            self.bulk_operations.append(UpdateOne(
                {'name': legislator_name},  # 使用 name 字段查詢
                {'$set': update_data},
                upsert=False  # 不創建新記錄，只更新現有記錄
            ))
            
            # 如果緩衝區滿了，執行批量寫入
            if len(self.bulk_operations) >= self.bulk_buffer_size:
                self._flush_bulk_operations()
            
            return {
                'success': True,
                'operation_type': 'emotion_analysis_and_timeseries_update',
                'legislator': legislator_name
            }
            
        except Exception as e:
            logger.error(f"❌ 批量更新 {legislator_name} legislators 失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_emotion_analysis(self, legislator_name: str) -> Dict[str, Any]:
        """生成情緒分析數據 - 從 crawler_data 統計實際情緒數量"""
        try:
            # 從 crawler_data 中獲取該立委的所有評論和情緒數據
            pipeline = [
                {'$match': {'legislator': legislator_name}},
                {'$group': {
                    '_id': None,
                    'total_comments': {'$sum': 1},
                    'emotion_counts': {
                        '$push': {
                            'anger': {'$ifNull': ['$emotion_analysis.anger', 0]},
                            'disgust': {'$ifNull': ['$emotion_analysis.disgust', 0]},
                            'trust': {'$ifNull': ['$emotion_analysis.trust', 0]},
                            'sadness': {'$ifNull': ['$emotion_analysis.sadness', 0]},
                            'surprise': {'$ifNull': ['$emotion_analysis.surprise', 0]},
                            'joy': {'$ifNull': ['$emotion_analysis.joy', 0]},
                            'anticipation': {'$ifNull': ['$emotion_analysis.anticipation', 0]},
                            'fear': {'$ifNull': ['$emotion_analysis.fear', 0]}
                        }
                    }
                }}
            ]
            
            result = list(self.local_crawler_data.aggregate(pipeline))
            
            if not result:
                # 如果沒有數據，返回默認值
                return {
                    'anger': 0,
                    'disgust': 0,
                    'trust': 0,
                    'sadness': 0,
                    'surprise': 0,
                    'joy': 0,
                    'anticipation': 0,
                    'fear': 0,
                    'last_updated': datetime.now(),
                    'recall_oppose': 0,
                    'recall_support': 0
                }
            
            data = result[0]
            emotion_counts = data.get('emotion_counts', [])
            
            # 統計所有情緒的總和
            total_emotions = {
                'anger': 0,
                'disgust': 0,
                'trust': 0,
                'sadness': 0,
                'surprise': 0,
                'joy': 0,
                'anticipation': 0,
                'fear': 0
            }
            
            # 累加所有評論的情緒數據
            for emotion_data in emotion_counts:
                for emotion, count in emotion_data.items():
                    if emotion in total_emotions:
                        total_emotions[emotion] += count or 0
            
            # 計算罷免支持/反對 - 基於情緒分析
            # 負面情緒：anger, disgust, sadness, fear = 支持罷免
            recall_support = (total_emotions['anger'] + 
                            total_emotions['disgust'] + 
                            total_emotions['sadness'] + 
                            total_emotions['fear'])
            
            # 正面情緒：joy, trust, anticipation = 反對罷免
            recall_oppose = (total_emotions['joy'] + 
                           total_emotions['trust'] + 
                           total_emotions['anticipation'])
            
            # surprise 是中性情緒，可以根據上下文分配
            # 這裡簡單地平均分配給支持和反對
            surprise_half = total_emotions['surprise'] // 2
            recall_support += surprise_half
            recall_oppose += surprise_half
            
            return {
                'anger': total_emotions['anger'],
                'disgust': total_emotions['disgust'],
                'trust': total_emotions['trust'],
                'sadness': total_emotions['sadness'],
                'surprise': total_emotions['surprise'],
                'joy': total_emotions['joy'],
                'anticipation': total_emotions['anticipation'],
                'fear': total_emotions['fear'],
                'last_updated': datetime.now(),
                'recall_oppose': recall_oppose,
                'recall_support': recall_support
            }
            
        except Exception as e:
            logger.error(f"❌ 生成 {legislator_name} 情緒分析失敗: {e}")
            return {
                'anger': 0,
                'disgust': 0,
                'trust': 0,
                'sadness': 0,
                'surprise': 0,
                'joy': 0,
                'anticipation': 0,
                'fear': 0,
                'last_updated': datetime.now(),
                'recall_oppose': 0,
                'recall_support': 0
            }
    
    def _clean_stats_data(self, stats_data: Dict) -> Dict:
        """清理統計數據，移除 None 鍵和值"""
        if not isinstance(stats_data, dict):
            return {}
        
        cleaned = {}
        for key, value in stats_data.items():
            # 跳過 None 鍵
            if key is None:
                continue
            
            # 遞歸清理嵌套字典
            if isinstance(value, dict):
                cleaned_value = self._clean_stats_data(value)
                if cleaned_value:  # 只有非空字典才添加
                    cleaned[str(key)] = cleaned_value
            elif value is not None:  # 跳過 None 值
                cleaned[str(key)] = value
        
        return cleaned
    
    def _flush_bulk_operations(self):
        """執行批量操作"""
        if hasattr(self, 'bulk_operations') and self.bulk_operations:
            try:
                result = self.local_legislators.bulk_write(self.bulk_operations, ordered=False)
                # 簡化日誌輸出，保持與原本格式一致
            except Exception as e:
                logger.error(f"❌ 批量寫入失敗: {e}")
            finally:
                # 確保無論成功還是失敗都清理緩衝區
                self.bulk_operations.clear()
    
    def sync_to_atlas(self, legislator_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """同步到 Atlas - 優化版"""
        if not self.atlas_connected:
            return {
                'success': False,
                'error': 'Atlas 未連接'
            }
        
        try:
            # 獲取需要同步的立委
            if not legislator_names:
                # 從本地獲取所有立委
                local_legislators = list(self.local_legislators.find({}, {'name': 1}))
                legislator_names = [doc['name'] for doc in local_legislators]
            
            synced_count = 0
            failed_count = 0
            
            for legislator_name in legislator_names:
                try:
                    # 獲取本地數據
                    local_data = self.local_legislators.find_one({'name': legislator_name})
                    if not local_data:
                        continue
                    
                    # 檢查 Atlas 是否有差異
                    atlas_data = self.atlas_legislators.find_one({'name': legislator_name})
                    
                    if self._has_differences(local_data, atlas_data):
                        # 只更新 emotion_analysis 和 updated_at 字段，避免修改 _id
                        update_data = {}
                        if 'emotion_analysis' in local_data:
                            update_data['emotion_analysis'] = local_data['emotion_analysis']
                        if 'updated_at' in local_data:
                            update_data['updated_at'] = local_data['updated_at']
                        
                        if update_data:
                            self.atlas_legislators.update_one(
                                {'name': legislator_name},
                                {'$set': update_data},
                                upsert=False  # 不創建新記錄
                            )
                            synced_count += 1
                    else:
                        # 無差異，跳過同步
                        pass
                        
                except Exception as e:
                    failed_count += 1
                    logger.error(f"❌ {legislator_name} 同步到 Atlas 失敗: {e}")
            
            return {
                'success': True,
                'synced_count': synced_count,
                'failed_count': failed_count,
                'total_count': len(legislator_names)
            }
            
        except Exception as e:
            logger.error(f"❌ Atlas 同步失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _has_differences(self, local_data: Dict, atlas_data: Optional[Dict]) -> bool:
        """檢查本地和 Atlas 數據是否有差異"""
        if not atlas_data:
            return True
        
        # 比較關鍵字段
        key_fields = ['emotion_analysis', 'updated_at']
        for field in key_fields:
            if field in local_data and field in atlas_data:
                if local_data[field] != atlas_data[field]:
                    return True
        
        return False
    
    def close(self):
        """關閉連接"""
        try:
            # 執行剩餘的批量操作
            self._flush_bulk_operations()
            
            # 關閉本地連接
            if hasattr(self, 'local_client'):
                self.local_client.close()
            
            # 關閉 Atlas 連接
            if hasattr(self, 'atlas_client') and self.atlas_connected:
                self.atlas_client.close()
            
        except Exception as e:
            logger.error(f"❌ 關閉連接時出錯: {e}")

# 進程級別的 worker 函數
def process_legislator_worker(legislator_name: str) -> Tuple[str, bool, Dict]:
    """
    進程級別的立委處理 worker
    
    Args:
        legislator_name: 立委姓名
        
    Returns:
        Tuple[str, bool, Dict]: (立委姓名, 是否成功, 結果)
    """
    try:
        # 每個進程創建自己的 MongoDB 連接
        updater = OptimizedMongoUpdater(use_connection_pool=True)
        
        try:
            result = updater.update_legislator_data(legislator_name)
            return (legislator_name, True, result)
        finally:
            # 確保連接被正確關閉
            updater.close()
        
    except Exception as e:
        import traceback
        error_msg = f"更新 {legislator_name} 時出錯: {e}\n詳細錯誤: {traceback.format_exc()}"
        return (legislator_name, False, {'error': error_msg}) 