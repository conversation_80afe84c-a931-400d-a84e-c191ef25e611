#!/usr/bin/env python3
"""
立委罷免數據爬蟲系統 - 簡化版
只保留核心功能：指定立委、天數、平台進行爬蟲和數據處理
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 添加項目根目錄到 Python 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)
# 導入必要模組
from crawler.simple_crawler_manager import SimpleCrawlerManager
from backup_manager import BackupManager

#"洪孟楷", "葉元之", "張智倫", "林德福", "羅明才", "廖先翔", "王鴻薇", "李彥秀", "羅智強", 
#    "徐巧芯", "賴士葆", "林沛祥", "牛煦庭", "涂權吉", "魯明哲", "萬美玲", "呂玉玲", "邱若華", "鄭正鈐", "徐欣瑩", 
ALL_LEGISLATORS = [
    "林思銘", "陳超明", "邱鎮軍", "顏寬恒", "高虹安",
    "楊瓊瓔", "廖偉翔", "黃建豪", "羅廷瑋", "江啟臣", "馬文君", "游顥", "謝衣鳳", "丁學忠", "傅崐萁", "黃建賓"
 ]




def setup_logging():
    """設置日誌系統"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('crawler.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def validate_setup():
    """驗證系統設定"""
    try:
        # 檢查API keys - 優先檢查環境變數，其次檢查api.json
        has_api_key = False

        if os.getenv('GEMINI_API_KEY'):
            has_api_key = True
            logger.info("✅ 使用環境變數中的GEMINI_API_KEY")
        elif os.path.exists('api.json'):
            try:
                with open('api.json', 'r', encoding='utf-8') as f:
                    api_data = json.load(f)
                    if api_data.get('api_keys') and len(api_data['api_keys']) > 0:
                        has_api_key = True
                        logger.info(f"✅ 使用api.json中的API keys ({len(api_data['api_keys'])}個)")
            except Exception as e:
                logger.warning(f"⚠️ 讀取api.json失敗: {e}")

        if not has_api_key:
            logger.error("❌ 缺少GEMINI_API_KEY，請設置環境變數或配置api.json")
            return False
            
        # 檢查必要目錄
        required_dirs = ['crawler/processed', 'crawler/processed/alldata', 
                        'crawler/processed/user_data', 'crawler/processed/final_data']
        
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"📁 創建目錄: {dir_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 驗證失敗: {e}")
        return False

def parse_arguments():
    """解析命令列參數 - 簡化版"""
    parser = argparse.ArgumentParser(
        description='立委罷免數據爬蟲系統',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
範例用法：
  python main.py --days 30                           # 抓取最近30天資料 (先PTT後YouTube，已優化速度)
  python main.py --platforms ptt,youtube             # 指定爬取平台順序
  python main.py --legislators 高虹安 牛煦庭          # 指定立委  
  python main.py --days 400 --platforms ptt,youtube  # 400天PTT+YouTube資料 (YouTube已加速)
  python main.py --skip-url-collection               # 跳過URL收集，只抓數據和處理
  python main.py --process-only                      # 跳過所有爬蟲，只做數據處理
  python main.py --generate-final-data               # 只生成final_data，不重新進行Gemini分析
  python main.py --update-mongo-only                 # 只進行MongoDB更新 (包含最近7天一天一個統計點等)
  python main.py --use-proxy                         # 使用代理IP進行爬取
  python main.py --no-proxy                          # 強制不使用代理(直連模式，更穩定)
  python main.py --days 43 --platforms youtube --no-proxy  # YouTube不用代理，更快速
        """
    )
    
    # 基本參數
    parser.add_argument('--days', type=int, default=30,
                       help='抓取最近幾天的資料 (預設: 30)')
    parser.add_argument('--platforms', type=str, default='ptt,youtube',
                       help='指定爬取平台順序，用逗號分隔 (預設: ptt,youtube)')
    parser.add_argument('--legislators', nargs='+', default=[],
                       help='指定要爬取的立委名稱')
    parser.add_argument('--max-workers', type=int, default=4,
                       help='最大並行工作數 (預設: 4)')
    
    # 控制流程的參數
    parser.add_argument('--skip-url-collection', action='store_true',
                       help='跳過URL收集階段，直接使用現有URL進行爬取')
    parser.add_argument('--process-only', action='store_true',
                       help='跳過所有爬蟲，只進行數據處理')
    parser.add_argument('--generate-final-data', action='store_true',
                       help='只生成final_data，不重新進行Gemini分析')
    parser.add_argument('--update-mongo-only', action='store_true',
                       help='只進行MongoDB更新')
    parser.add_argument('--backup-only', action='store_true',
                       help='只進行數據備份')
    parser.add_argument('--no-backup', action='store_true',
                       help='跳過備份')
    parser.add_argument('--reset-youtube-data', action='store_true',
                       help='重置YouTube數據，清理舊的URL和評論數據')
    parser.add_argument('--youtube-only-analysis', action='store_true',
                       help='YouTube專用流程：爬取YT→合併現有PTT→只分析YT數據')
    
    # 代理支持 - 改進版
    proxy_group = parser.add_mutually_exclusive_group()
    proxy_group.add_argument('--use-proxy', action='store_true',
                           help='使用代理IP進行爬取（可能較慢但可繞過限制）')
    proxy_group.add_argument('--no-proxy', action='store_true',
                           help='強制不使用代理，直接連線（更快速穩定，推薦）')
    
    return parser.parse_args()

def get_date_range(args):
    """計算日期範圍"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=args.days)
    return start_date, end_date

def get_legislators_with_data():
    """自動檢測有數據的立委"""
    import os
    legislators_with_data = []

    # 檢查 YouTube 數據
    youtube_data_dir = "crawler/data/youtube"
    if os.path.exists(youtube_data_dir):
        for file in os.listdir(youtube_data_dir):
            if file.endswith('.json'):
                name = file.replace('.json', '')
                if name not in legislators_with_data:
                    legislators_with_data.append(name)

    # 檢查 PTT 數據
    ptt_data_dir = "crawler/data/ptt"
    if os.path.exists(ptt_data_dir):
        for file in os.listdir(ptt_data_dir):
            if file.endswith('.json'):
                name = file.replace('.json', '')
                if name not in legislators_with_data:
                    legislators_with_data.append(name)

    return legislators_with_data

def reset_youtube_data(legislators):
    """重置YouTube數據，清理舊的URL和評論數據"""
    import os
    import shutil
    
    for legislator in legislators:
        # 清理YouTube URL檔案
        youtube_href_file = f"crawler/href/youtube/{legislator}.json"
        if os.path.exists(youtube_href_file):
            try:
                os.remove(youtube_href_file)
                logger.info(f"🗑️ 已刪除 {legislator} 的YouTube URL檔案")
            except Exception as e:
                logger.warning(f"⚠️ 刪除 {legislator} URL檔案失敗: {e}")
        
        # 清理YouTube評論數據檔案
        youtube_data_file = f"crawler/data/youtube/{legislator}.json"
        if os.path.exists(youtube_data_file):
            try:
                os.remove(youtube_data_file)
                logger.info(f"🗑️ 已刪除 {legislator} 的YouTube評論數據")
            except Exception as e:
                logger.warning(f"⚠️ 刪除 {legislator} 評論數據失敗: {e}")
        
        # 清理已處理的數據
        alldata_file = f"crawler/processed/alldata/{legislator}.json"
        if os.path.exists(alldata_file):
            try:
                os.remove(alldata_file)
                logger.info(f"🗑️ 已刪除 {legislator} 的整合數據")
            except Exception as e:
                logger.warning(f"⚠️ 刪除 {legislator} 整合數據失敗: {e}")
                
        # 清理用戶數據
        user_files = [
            f"crawler/processed/user_data/{legislator}_users.json",
            f"crawler/processed/user_data/{legislator}_gemini_format.json"
        ]
        for user_file in user_files:
            if os.path.exists(user_file):
                try:
                    os.remove(user_file)
                    logger.info(f"🗑️ 已刪除 {legislator} 的用戶數據檔案")
                except Exception as e:
                    logger.warning(f"⚠️ 刪除 {legislator} 用戶數據失敗: {e}")

def update_legislator_parallel(legislator_name: str) -> tuple:
    """
    並行更新單個立委的數據 - 優化版
    
    Args:
        legislator_name: 立委姓名
        
    Returns:
        tuple: (legislator_name, success, result)
    """
    try:
        # 使用優化版的 MongoDB 更新器
        from optimized_mongo_updater import OptimizedMongoUpdater
        
        # 在每個進程中創建獨立的更新器實例
        updater = OptimizedMongoUpdater(use_connection_pool=True)
        
        try:
            result = updater.update_legislator_data(legislator_name)
            return (legislator_name, True, result)
        finally:
            # 確保連接被正確關閉
            updater.close()
        
    except Exception as e:
        import traceback
        error_msg = f"更新 {legislator_name} 時出錯: {e}\n詳細錯誤: {traceback.format_exc()}"
        return (legislator_name, False, {'error': error_msg})

def update_legislators_parallel(target_legislators: List[str], max_workers: int = 4) -> dict:
    """
    並行更新多個立委的數據 - 優化版
    
    Args:
        target_legislators: 要更新的立委列表
        max_workers: 最大並行進程數 (預設4個，一次處理4個立委)
        
    Returns:
        dict: 更新結果統計
    """
    success_count = 0
    failed_legislators = []
    results = {}
    total_legislators = len(target_legislators)
    
    logger.info(f"🚀 開始優化並行更新 {total_legislators} 位立委 (最大並行數: {max_workers})")
    logger.info(f"📊 使用 {max_workers} 個進程同時處理立委數據")
    logger.info(f"⏱️  預計處理時間: 約 {total_legislators // max_workers + 1} 批次")
    logger.info("-" * 60)
    
    start_time = datetime.now()
    
    # 使用 multiprocessing.Pool 而不是 ThreadPoolExecutor
    from multiprocessing import Pool
    with Pool(processes=max_workers) as pool:
        # 使用 imap_unordered 提高效率
        completed_count = 0
        for legislator_name, success, result in pool.imap_unordered(update_legislator_parallel, target_legislators):
            completed_count += 1
            
            # 計算進度
            progress = (completed_count / total_legislators) * 100
            elapsed_time = datetime.now() - start_time
            
            if success and result.get('success'):
                success_count += 1
                processing_time = result.get('processing_time', 0)
                logger.info(f"✅ [{completed_count:2d}/{total_legislators}] {legislator_name:<10} 更新完成 ({progress:.1f}%) - 耗時 {processing_time:.1f}秒")
            else:
                failed_legislators.append(legislator_name)
                error_msg = result.get('error', '未知錯誤') if isinstance(result, dict) else str(result)
                logger.error(f"❌ [{completed_count:2d}/{total_legislators}] {legislator_name:<10} 更新失敗 ({progress:.1f}%)")
                logger.error(f"   錯誤: {error_msg[:100]}{'...' if len(error_msg) > 100 else ''}")
            
            results[legislator_name] = result
            
            # 每10個或最後一個顯示進度統計
            if completed_count % 10 == 0 or completed_count == total_legislators:
                avg_time_per_leg = elapsed_time / completed_count if completed_count > 0 else timedelta(0)
                remaining_leg = total_legislators - completed_count
                estimated_remaining = avg_time_per_leg * remaining_leg
                logger.info(f"📈 進度統計: {completed_count}/{total_legislators} ({progress:.1f}%) | "
                      f"成功: {success_count} | 失敗: {len(failed_legislators)} | "
                      f"平均時間: {avg_time_per_leg.total_seconds():.1f}秒/立委 | "
                      f"預計剩餘: {estimated_remaining.total_seconds():.0f}秒")
    
    # 最終統計
    total_time = datetime.now() - start_time
    logger.info("-" * 60)
    logger.info(f"🎉 優化並行更新完成！")
    logger.info(f"📊 最終統計:")
    logger.info(f"   總立委數: {total_legislators}")
    logger.info(f"   成功更新: {success_count}")
    logger.info(f"   更新失敗: {len(failed_legislators)}")
    logger.info(f"   成功率: {(success_count/total_legislators)*100:.1f}%")
    logger.info(f"   總耗時: {total_time.total_seconds():.1f}秒")
    logger.info(f"   平均耗時: {total_time.total_seconds()/total_legislators:.1f}秒/立委")
    
    if failed_legislators:
        logger.warning(f"❌ 失敗的立委: {', '.join(failed_legislators)}")
    
    return {
        'success_count': success_count,
        'failed_count': len(failed_legislators),
        'failed_legislators': failed_legislators,
        'results': results,
        'total_time': total_time.total_seconds(),
        'avg_time_per_leg': total_time.total_seconds() / total_legislators if total_legislators > 0 else 0
    }

def main():
    """簡化主程式 - 只執行爬蟲和數據處理"""
    # 設置日誌
    global logger
    logger = setup_logging()
    logger.info("=== 立委罷免數據爬蟲系統啟動 ===")
    
    try:
        # 解析參數
        args = parse_arguments()
        logger.info(f"📋 解析參數完成: {vars(args)}")
        
        # 驗證設定
        logger.info("驗證系統設定...")
        if not validate_setup():
            logger.error("系統設定驗證失敗，請檢查環境配置")
            input("按 Enter 鍵退出...")  # 防止窗口立即关闭
            return 1
        
        # 處理 --update-mongo-only 模式
        if args.update_mongo_only:
            logger.info("🔄 --update-mongo-only 模式啟動...")
            try:
                import os
                
                # 獲取要處理的立委
                if args.legislators:
                    target_legislators = args.legislators
                    logger.info(f"📊 指定更新立委: {target_legislators}")
                else:
                    # 自動檢測有final_data文件的立委
                    final_data_dir = os.path.join(current_dir, "crawler", "processed", "final_data")
                    target_legislators = []
                    if os.path.exists(final_data_dir):
                        for file in os.listdir(final_data_dir):
                            if file.endswith('_使用者分析.json') or file.endswith('_ptt_使用者分析.json'):
                                name = file.replace('_使用者分析.json', '').replace('_ptt_使用者分析.json', '')
                                if name not in target_legislators:
                                    target_legislators.append(name)
                    
                    if not target_legislators:
                        logger.error(f"❌ 沒有找到任何final_data文件，檢查路徑: {final_data_dir}")
                        input("按 Enter 鍵退出...")
                        return 1
                    
                    logger.info(f"📊 自動檢測到 {len(target_legislators)} 位立委需要更新")
                
                # 使用優化版並行更新
                logger.info("🔄 開始智能並行更新本地MongoDB...")
                
                # 使用優化版的並行處理 V2
                try:
                    from optimized_mongo_updater_v2 import update_legislators_parallel_v2
                    update_result = update_legislators_parallel_v2(target_legislators, max_workers=args.max_workers)
                except ImportError:
                    # 如果V2不可用，回退到V1
                    logger.warning("⚠️ 優化版V2不可用，使用V1版本")
                    update_result = update_legislators_parallel(target_legislators, max_workers=args.max_workers)
                
                success_count = update_result['success_count']
                failed_legislators = update_result['failed_legislators']
                
                logger.info(f"✅ 本地MongoDB智能並行更新完成！成功更新 {success_count}/{len(target_legislators)} 位立委")
                
                if failed_legislators:
                    logger.warning(f"⚠️ 以下立委更新失敗: {failed_legislators}")
                
                # 2. 同步差異部分到Atlas (使用優化版更新器)
                logger.info("🔄 開始同步差異部分到Atlas...")
                
                try:
                    from optimized_mongo_updater import OptimizedMongoUpdater
                    atlas_updater = OptimizedMongoUpdater(use_connection_pool=True)
                    
                    if atlas_updater.atlas_connected:
                        # 只同步成功更新的立委
                        successful_legislators = [leg for leg in target_legislators if leg not in failed_legislators]
                        
                        if successful_legislators:
                            sync_result = atlas_updater.sync_to_atlas(successful_legislators)
                            if sync_result['success']:
                                logger.info(f"✅ Atlas同步完成！同步了 {sync_result['synced_count']}/{sync_result['total_count']} 位立委")
                                if sync_result['failed_count'] > 0:
                                    logger.warning(f"⚠️ {sync_result['failed_count']} 位立委同步失敗")
                            else:
                                logger.error(f"❌ Atlas同步失敗: {sync_result.get('error', '未知錯誤')}")
                        else:
                            logger.warning("⚠️ 沒有成功更新的立委，跳過Atlas同步")
                    else:
                        logger.info("⏭️ 跳過Atlas同步（未連接或未配置）")
                    
                    # 關閉連接
                    atlas_updater.close()
                    
                except Exception as e:
                    logger.error(f"❌ Atlas同步失敗: {e}")
                    logger.warning("⚠️ 本地更新成功，但Atlas同步失敗")
                
                # 3. 顯示最終結果
                if failed_legislators:
                    logger.warning(f"⚠️ 以下立委更新失敗: {failed_legislators}")
                
                logger.info(f"🎉 MongoDB更新流程完成！")
                logger.info(f"   本地更新: {success_count}/{len(target_legislators)} 成功")
                logger.info(f"   總耗時: {update_result['total_time']:.1f}秒")
                logger.info(f"   平均耗時: {update_result['avg_time_per_leg']:.1f}秒/立委")
                
                input("按 Enter 鍵退出...")
                return 0

            except ImportError as e:
                logger.error(f"❌ 導入優化版MongoDB更新器失敗: {e}")
                input("按 Enter 鍵退出...")
                return 1
            except Exception as e:
                logger.error(f"❌ MongoDB更新失敗: {e}")
                import traceback
                logger.error(f"詳細錯誤: {traceback.format_exc()}")
                input("按 Enter 鍵退出...")
                return 1
        
        # 處理 --backup-only 模式
        if args.backup_only:
            logger.info("🔄 --backup-only 模式啟動...")
            try:
                backup_manager = BackupManager()
                backup_manager.backup_all_legislators()
                logger.info("✅ 數據備份完成！")
                input("按 Enter 鍵退出...")
                return 0
            except Exception as e:
                logger.error(f"❌ 數據備份失敗: {e}")
                import traceback
                logger.error(f"詳細錯誤: {traceback.format_exc()}")
                input("按 Enter 鍵退出...")
            return 1

        # 處理 --generate-final-data 模式
        if args.generate_final_data:
            logger.info("🔄 --generate-final-data 模式啟動...")
            try:
                from crawler.gemini_emo_user import merge_temp_results
                
                # 獲取要處理的立委
                if args.legislators:
                    legislators = args.legislators
                    logger.info(f"📊 指定生成final_data的立委: {legislators}")
                else:
                    # 自動檢測有gemini_format.json文件的立委
                    import os
                    user_data_dir = "crawler/processed/user_data"
                    legislators = []
                    if os.path.exists(user_data_dir):
                        for file in os.listdir(user_data_dir):
                            if file.endswith('_gemini_format.json'):
                                name = file.replace('_gemini_format.json', '')
                                legislators.append(name)
                    
                    if not legislators:
                        logger.error("❌ 沒有找到任何gemini_format.json文件")
                        input("按 Enter 鍵退出...")
                        return 1
                    
                    logger.info(f"📊 自動檢測到 {len(legislators)} 位立委需要生成final_data")
                
                # 為每個立委生成final_data
                success_count = 0
                for legislator in legislators:
                    try:
                        logger.info(f"🔄 正在生成 {legislator} 的final_data...")
                        merge_temp_results(legislator)
                        success_count += 1
                        logger.info(f"✅ {legislator} final_data生成完成")
                    except Exception as e:
                        logger.error(f"❌ 生成 {legislator} final_data失敗: {e}")
                
                logger.info(f"✅ final_data生成完成！成功處理 {success_count}/{len(legislators)} 位立委")
                input("按 Enter 鍵退出...")
                return 0
                
            except ImportError as e:
                logger.error(f"❌ 導入gemini_emo_user模組失敗: {e}")
                input("按 Enter 鍵退出...")
                return 1
            except Exception as e:
                logger.error(f"❌ final_data生成失敗: {e}")
                import traceback
                logger.error(f"詳細錯誤: {traceback.format_exc()}")
                input("按 Enter 鍵退出...")
                return 1

        # 獲取參數
        start_date, end_date = get_date_range(args)

        # 當使用 --process-only 時，自動檢測有數據的立委和處理所有平台
        if args.process_only:
            logger.info("🔄 --process-only 模式啟動...")
            legislators_with_data = get_legislators_with_data()
            if legislators_with_data:
                legislators = legislators_with_data  # 只處理有數據的立委
                platforms = ['ptt', 'youtube']  # 處理所有平台，PTT優先
                logger.info(f"📋 --process-only 模式：自動檢測到 {len(legislators)} 位有數據的立委")
                logger.info(f"📋 有數據的立委: {legislators}")
            else:
                logger.warning("⚠️ 未檢測到任何立委數據，將處理所有立委")
                legislators = ALL_LEGISLATORS
                platforms = ['ptt', 'youtube']
        else:
            legislators = args.legislators if args.legislators else ALL_LEGISLATORS
            platforms = args.platforms.split(',') if isinstance(args.platforms, str) else args.platforms
        
        logger.info(f"📅 處理日期範圍: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} ({args.days}天)")
        logger.info(f"🌐 處理平台: {', '.join(platforms)}")
        logger.info(f"👥 處理立委: {len(legislators)} 位 - {legislators if len(legislators) <= 5 else legislators[:5] + ['...']}")
        
        # 顯示執行模式
        if args.process_only:
            logger.info("⏭️ 執行模式: 只進行數據處理 (跳過所有爬蟲)")
        elif args.skip_url_collection:
            logger.info("⏭️ 執行模式: 跳過URL收集，直接抓取數據和處理")
        elif args.youtube_only_analysis:
            logger.info("🎬 執行模式: YouTube專用流程 (爬取YT→合併現有PTT→只分析YT數據)")
            platforms = ['youtube']  # 強制只處理YouTube
        else:
            logger.info("🚀 執行模式: 完整爬蟲流程 (URL收集→數據抓取→處理)")

        # 🔄 數據備份處理
        backup_manager = None
        processed_user_ids = {}  # 儲存每個立委的已處理用戶ID
        
        if not args.no_backup and not args.update_mongo_only:
            logger.info("🔄 開始數據備份流程...")
            try:
                backup_manager = BackupManager()
                
                # 為每個立委進行備份
                for legislator in legislators:
                    logger.info(f"📁 備份 {legislator} 的現有資料...")
                    
                    # 1. 備份現有資料
                    backup_paths = backup_manager.backup_existing_data(legislator)
                    logger.info(f"✅ {legislator} 備份完成: {backup_paths}")
                    
                    # 2. 獲取已處理的用戶ID（用於避免重複處理）
                    processed_users = backup_manager.get_processed_user_ids(legislator)
                    processed_user_ids[legislator] = processed_users
                    logger.info(f"📋 {legislator} 找到 {len(processed_users)} 個已處理用戶")
                    
                    # 3. 清理現有資料（為新抓取做準備）
                    if not args.process_only:  # 只有在非process-only模式下才清理
                        backup_manager.clear_existing_data(legislator)
                        logger.info(f"🧹 {legislator} 現有資料已清理")
                
                logger.info("✅ 所有立委備份完成！")
                
            except Exception as e:
                logger.error(f"❌ 數據備份失敗: {e}")
                import traceback
                logger.error(f"詳細錯誤: {traceback.format_exc()}")
                if not args.process_only:  # 只有在非process-only模式下才中斷
                    input("按 Enter 鍵退出...")
                    return 1
        else:
            logger.info("⏭️ 跳過數據備份")
        
        # 重置YouTube數據 (如果需要)
        if args.reset_youtube_data:
            logger.info("🔄 重置YouTube數據...")
            reset_youtube_data(legislators)
            logger.info("✅ YouTube數據重置完成")
        
        # 確認繼續
        if not args.process_only and len(legislators) > 5:
            logger.info("即將開始處理大量立委，這可能需要較長時間...")
            logger.info("如需中止，請按 Ctrl+C")
            import time
            time.sleep(3)
        
        # 使用簡化爬蟲管理器執行完整流程
        logger.info("🚀 啟動簡化爬蟲管理器...")
        
        try:
            from crawler.simple_crawler_manager import SimpleCrawlerManager
            crawler_manager = SimpleCrawlerManager()
            logger.info("✅ 簡化爬蟲管理器初始化成功")
        except ImportError as e:
            logger.error(f"❌ 導入簡化爬蟲管理器失敗: {e}")
            input("按 Enter 鍵退出...")
            return 1
        except Exception as e:
            logger.error(f"❌ 初始化簡化爬蟲管理器失敗: {e}")
            input("按 Enter 鍵退出...")
            return 1

        try:
            # 處理代理設定邏輯
            if args.no_proxy:
                use_proxy = False
                logger.info("🚫 已強制禁用代理，使用直連模式")
            elif args.use_proxy:
                use_proxy = True
                logger.info("🌐 已強制啟用代理模式")
            else:
                # 預設自動檢測模式（通常不使用代理，更穩定）
                use_proxy = False
                logger.info("🔧 使用預設模式：直連（更穩定快速）")
            
            # 執行完整爬蟲流程：爬蟲 → 分析 → 存儲 → 統計
            logger.info("開始執行爬蟲流程...")
            result = crawler_manager.crawl_all_legislators(
                legislators=legislators,
                days=args.days,
                platforms=platforms,
                process_only=args.process_only,
                skip_url_collection=args.skip_url_collection,
                youtube_only_analysis=args.youtube_only_analysis,
                processed_user_ids=processed_user_ids,  # 傳遞已處理的用戶ID
                use_proxy=use_proxy  # 傳遞處理後的代理參數
            )

            if result and result.get('success', False):
                processed_users_count = result.get('processed_users_count', 0)
                logger.info(f"✅ 爬蟲流程完成: 處理了 {result.get('total_legislators', 0)} 位立委")
                logger.info(f"📊 統計: 爬取 {result.get('total_records', 0)} 筆資料")
                if processed_users_count > 0:
                    logger.info(f"📋 重複用戶處理: 跳過了 {processed_users_count} 個已處理用戶")
                logger.info("🎉 所有流程執行完成！")
                input("按 Enter 鍵退出...")
                return 0
            else:
                logger.error("❌ 爬蟲流程失敗")
                logger.error(f"錯誤詳情: {result}")
                input("按 Enter 鍵退出...")
                return 1

        except Exception as e:
            logger.error(f"❌ 爬蟲流程出錯: {e}")
            import traceback
            logger.error(f"詳細錯誤: {traceback.format_exc()}")
            input("按 Enter 鍵退出...")
            return 1
        
    except KeyboardInterrupt:
        logger.warning("程式被使用者中斷")
        input("按 Enter 鍵退出...")
        return 1
    except Exception as e:
        logger.error(f"程式執行失敗: {e}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        input("按 Enter 鍵退出...")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())

