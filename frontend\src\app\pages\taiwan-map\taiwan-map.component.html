<div *ngIf="isLoading" class="loading-container">
  <div class="loading-spinner">
    <div class="spinner"></div>
    <p>載入中...</p>
  </div>
</div>

<!-- 使用說明 Modal -->
<div class="usage-modal-backdrop" *ngIf="showUsageModal">
  <div class="usage-modal">
    <h2>使用說明與介紹</h2>
    <ul>
      <li>點擊地圖上的縣市可查看該地區立委資訊。</li>
      <li>可依罷免狀態、政黨等條件篩選。</li>
      <li>點擊立委姓名可進一步查看詳細資料。</li>
      <li>如需重設篩選，請點擊「清除所有篩選」。</li>
      <li>本研究是依照社群平台中發言人數進行網路聲量調查</li>
      <li>僅供學術參考使用，請勿用於商業用途</li>
    </ul>
    <button (click)="closeUsageModal()">我知道了</button>
  </div>
</div>

<div *ngIf="!isLoading" class="map-page-container">
    
    <!-- 罷免投票日期顯示 -->
    <div class="vote-date-display">
      罷免投票日期: 8/23
    </div>

    <div class="filter-panel">
        <div class="filter-layout">
            <div class="area-count-section">
                <div class="area-header">
                    <div>
                        <h1>2025立委罷免分析</h1>
                        <h4>各區立委席次</h4>
                    </div>
                </div>
                <div class="area-scroll-container">
                    <ul>
                        <li *ngFor="let area of filteredAreaCounts"
                            class="area-count-item"
                            (click)="onAreaListClick(area.area)"
                            tabindex="0"
                            [class.active]="selectedCounty === getCountyIdByAreaName(area.area)">
                            <span class="area-name">{{ area.area }}</span>
                            <span class="area-count">{{ area.count }}</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="status-filter-wrapper">
                <div class="filter-header" (click)="toggleStatusFilter()">
                    <h4>
                        <svg width="16" height="16" fill="currentColor" class="me-2 arrow-icon" [class.rotated]="showStatusFilter">
                            <path d="M6 3l3 3-3 3z"/>
                        </svg>
                        罷免狀態篩選
                    </h4>
                    <span class="filter-summary" *ngIf="!showStatusFilter && selectedStatuses.length > 0">
                        已選 {{ selectedStatuses.length }} 項
                    </span>
                </div>

                <div class="filter-content" *ngIf="showStatusFilter">
                    <div class="status-options">
                        <label class="status-option" *ngFor="let status of recallStatuses">
                            <input
                                type="checkbox"
                                [value]="status.key"
                                [checked]="selectedStatuses.includes(status.key)"
                                (change)="onStatusChange(status.key, $event)">
                            <span class="status-label-content">
                                <svg width="14" height="14" fill="currentColor" class="status-icon">
                                    <circle cx="7" cy="7" r="3" [attr.fill]="getStatusColor(status.key)"/>
                                </svg>
                                {{ status.label }}
                                <span class="status-count">({{ getStatusCount(status.key) }})</span>
                            </span>
                        </label>
                    </div>

                    <div class="filter-actions">
                        <button type="button" class="btn-clear" (click)="clearAllFilters()">清除全部</button>
                        <button type="button" class="btn-apply" (click)="applyStatusFilters()">套用篩選</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="map-container" style="flex:1;">
        <svg [attr.viewBox]="viewBox" class="taiwan-svg" preserveAspectRatio="xMidYMid meet">
            <g *ngFor="let location of taiwanMap.locations">
                <path
                    #countyPath
                    [attr.d]="location.path"
                    [attr.id]="location.id"
                    [attr.fill]="getCountyColor(location.id)"
                    [attr.data-color]="colorMap[location.id]"
                    [ngClass]="{selected: selectedCounty === location.id}"
                    (click)="onCountyClick(location.id, getCountyName(location.id))"
                    (mouseenter)="countyHover(location.id, true)"
                    (mouseleave)="countyHover(location.id, false)"
                    [attr.title]="getCountyName(location.id) + (shouldShowRecallCount(location.id) ? ' (' + getRecallCountByCountyId(location.id) + '位立委被罷免)' : '')"
                />
                <!-- 在地圖上顯示被罷免立委數量 -->
                <text *ngIf="shouldShowRecallCount(location.id) && countyLabelPositions[location.id]" 
                    class="county-recall-count"
                    [attr.x]="getCountyLabelPosition(location.id).x"
                    [attr.y]="getCountyLabelPosition(location.id).y"
                    text-anchor="middle"
                    dominant-baseline="central">
                    {{ getRecallCountByCountyId(location.id) }}
                </text>
                
                <title>{{ getCountyName(location.id) }}{{ shouldShowRecallCount(location.id) ? ' (' + getRecallCountByCountyId(location.id) + '位立委被罷免)' : '' }}</title>
            </g>
        </svg>
        <div *ngIf="!selectedCounty && !selectedParty" class="map-tip">
            點擊縣市可查看詳情
        </div>
    </div>

    <div *ngIf="selectedCounty" class="county-info-panel">
        <div class="panel-header">
            <button class="back-btn" (click)="resetFocus()">返回全部縣市</button>
            <h2>{{ getCountyName(selectedCounty) }}</h2>
        </div>
        <div class="legislators-scroll-container">
            <div *ngFor="let politician of politicians" class="legislator-card"
                [class.no-click]="politician.id.startsWith('no-recall')"
                (click)="!politician.id.startsWith('no-recall') && goToPolitician(politician.id)">
                <div class="legislator-image" *ngIf="politician.image_url && !politician.id.startsWith('no-recall')">
                    <img [src]="politician.image_url" [alt]="politician.name" onerror="this.src='assets/images/default-avatar.png';">
                </div>
                <div class="legislator-image" *ngIf="!politician.image_url && !politician.id.startsWith('no-recall')">
                    <div class="default-avatar">{{ politician.name.charAt(0) || 'N' }}</div>
                </div>
                <div class="legislator-info">
                    <h3>{{ politician.name }}</h3>
                    <p class="constituency" *ngIf="politician.constituency">{{ politician.constituency }}</p>
                    <p class="party" *ngIf="politician.party" [style.background]="getPartyColor(politician.party)">
                        {{ politician.party }}
                    </p>
                    <p class="recall-status" *ngIf="politician.recallStatus" [style.background]="getStatusColor(politician.recallStatus)">
                        {{ politician.recallStatus }}
                    </p>
                    <p class="recall-note" *ngIf="politician.recallNote">{{ politician.recallNote }}</p>
                    <p class="recall-vote-date" *ngIf="politician.recallVoteDate">投票日: {{ politician.recallVoteDate }}</p>
                </div>
            </div>
            <div *ngIf="politicians.length === 0" class="no-data">
                暫無資料
            </div>
        </div>
    </div>

    <div *ngIf="selectedParty" class="county-info-panel">
        <button class="back-btn" (click)="resetFocus()">返回地圖</button>
        <h2>{{ selectedParty }} 不分區立委</h2>
        <div class="legislators-container">
            <div *ngFor="let politician of politicians" class="legislator-card" (click)="goToPolitician(politician.id)">
                <div class="legislator-image" *ngIf="politician.image_url">
                    <img [src]="politician.image_url" [alt]="politician.name" onerror="this.src='assets/images/default-avatar.png';">
                </div>
                <div class="legislator-image" *ngIf="!politician.image_url">
                    <div class="default-avatar">{{ politician.name.charAt(0) || 'N' }}</div>
                </div>
                <div class="legislator-info">
                    <h3>{{ politician.name }}</h3>
                    <p class="constituency" *ngIf="politician.constituency">{{ politician.constituency }}</p>
                    <p class="party" *ngIf="politician.party"
                        style="background:#e74c3c;color:#fff;">
                        網路聲量調查
                    </p>
                </div>
            </div>
            <div *ngIf="politicians.length === 0" class="no-data">
                暫無資料
            </div>
        </div>
    </div>

    <!-- 右上角問號按鈕 -->
    <button class="usage-help-btn" (click)="showUsageModal = true" title="顯示使用說明">？</button>
</div>