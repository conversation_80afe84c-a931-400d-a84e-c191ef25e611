#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析final_data文件，生成立委统计报告
"""

import json
import os
from pathlib import Path
from collections import Counter
from datetime import datetime

class FinalDataAnalyzer:
    def __init__(self, data_dir="backend/crawler/processed/final_data"):
        self.data_dir = Path(data_dir)
        # 只分析這份白名單（用戶指定）
        self.target_legislators = set([
            "洪孟楷", "葉元之", "張智倫", "林德福", "羅明才", "廖先翔", "王鴻薇", "李彥秀", "羅智強", "徐巧芯", "賴士葆", "林沛祥", "牛煦庭", "涂權吉", "魯明哲", "萬美玲", "呂玉玲", "邱若華", "鄭正鈐", "徐欣瑩", "林思銘", "陳超明", "邱鎮軍", "顏寬恒", "楊瓊瓔", "廖偉翔", "黃健豪", "羅廷瑋", "江啟臣", "馬文君", "游顥", "謝衣鳳", "丁學忠", "傅崐萁", "黃建賓"
        ])
    
    def analyze_legislator_data(self, file_path):
        """分析单个立委的数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if not data:
                return None
            # 提取立委名称
            filename = file_path.stem
            legislator_name = filename.replace('_使用者分析', '')
            # 只分析白名單
            if legislator_name not in self.target_legislators:
                print(f"⏩ 跳過非目標立委: {legislator_name}")
                return None
            # 只統計正面/負面
            filtered_data = [item for item in data if item.get('情感標籤') in ('POSITIVE', 'NEGATIVE')]
            total_users = len(filtered_data)
            unique_users = len(set(item.get('使用者', item.get('用戶', '')) for item in filtered_data))
            # 情感標籤統計
            sentiment_counts = Counter(item['情感標籤'] for item in filtered_data)
            positive_count = sentiment_counts.get('POSITIVE', 0)
            negative_count = sentiment_counts.get('NEGATIVE', 0)
            # 勝負判斷
            if positive_count > negative_count:
                winner = "正面勝"
            elif negative_count > positive_count:
                winner = "負面勝"
            else:
                winner = "平手"
            # 情緒統計
            emotion_counts = Counter(item.get('情緒', '') for item in filtered_data)
            # 平台統計
            platform_counts = Counter(item.get('平台', '') for item in filtered_data)
            
            # 計算各平台的情感統計
            platform_sentiment = {}
            for platform in platform_counts.keys():
                platform_data = [item for item in filtered_data if item.get('平台') == platform]
                if platform_data:
                    pos_count = len([item for item in platform_data if item.get('情感標籤') == 'POSITIVE'])
                    neg_count = len([item for item in platform_data if item.get('情感標籤') == 'NEGATIVE'])
                    total_platform = pos_count + neg_count
                    if total_platform > 0:
                        pos_ratio = (pos_count / total_platform * 100)
                        neg_ratio = (neg_count / total_platform * 100)
                        platform_winner = "正面勝" if pos_count > neg_count else "負面勝" if neg_count > pos_count else "平手"
                        platform_sentiment[platform] = {
                            "正面數量": pos_count,
                            "負面數量": neg_count,
                            "正面比率": round(pos_ratio, 2),
                            "負面比率": round(neg_ratio, 2),
                            "勝負": platform_winner,
                            "總評論數": total_platform
                        }
            
            # 計算比率
            total_sentiment = positive_count + negative_count
            positive_ratio = (positive_count / total_sentiment * 100) if total_sentiment > 0 else 0
            negative_ratio = (negative_count / total_sentiment * 100) if total_sentiment > 0 else 0
            result = {
                "立委名稱": legislator_name,
                "總評論數": total_users,
                "獨立用戶數": unique_users,
                "情感標籤統計": {
                    "正面": {"數量": positive_count, "比率": round(positive_ratio, 2)},
                    "負面": {"數量": negative_count, "比率": round(negative_ratio, 2)}
                },
                "勝負": winner,
                "平台統計": {
                    "各平台評論數": dict(platform_counts),
                    "各平台情感分析": platform_sentiment
                },
                "情緒統計": dict(emotion_counts),
                "分析時間": datetime.now().isoformat()
            }
            print(f"✅ 分析完成: {legislator_name} - 總評論: {total_users}, 獨立用戶: {unique_users}, 勝負: {winner}")
            return result
        except Exception as e:
            print(f"❌ 分析文件失败 {file_path}: {e}")
            return None
    
    def analyze_all_legislators(self):
        """分析所有立委的数据"""
        print("🔍 开始分析所有立委数据...")
        
        # 查找所有final_data文件
        pattern = "*_使用者分析.json"
        files = list(self.data_dir.glob(pattern))
        
        if not files:
            print(f"❌ 在 {self.data_dir} 中未找到final_data文件")
            return
        
        print(f"📁 找到 {len(files)} 个final_data文件")
        
        all_results = []
        total_comments = 0
        total_unique_users = 0
        
        for file_path in files:
            result = self.analyze_legislator_data(file_path)
            if result:
                all_results.append(result)
                total_comments += result['總評論數']
                total_unique_users += result['獨立用戶數']
        
        # 生成汇总统计
        summary = {
            "分析摘要": {
                "總立委數": len(all_results),
                "總評論數": total_comments,
                "總獨立用戶數": total_unique_users,
                "平均每立委評論數": round(total_comments / len(all_results), 2) if all_results else 0,
                "平均每立委獨立用戶數": round(total_unique_users / len(all_results), 2) if all_results else 0
            },
            "立委詳細統計": all_results,
            "生成時間": datetime.now().isoformat()
        }
        
        # 保存结果
        output_file = "final_data_analysis_report.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 分析完成！结果已保存到: {output_file}")
        print(f"📊 统计摘要:")
        print(f"   - 总立委数: {len(all_results)}")
        print(f"   - 总评论数: {total_comments}")
        print(f"   - 总独立用户数: {total_unique_users}")
        
        return summary

def main():
    """主函数"""
    print("🔍 开始分析final_data文件...")
    
    # 创建分析器
    analyzer = FinalDataAnalyzer()
    
    # 分析所有立委数据
    summary = analyzer.analyze_all_legislators()
    
    if summary:
        print("\n🎉 分析完成！")
        print("📁 生成的文件:")
        print("   - final_data_analysis_report.json (详细统计)")
    
    print("\n🎉 分析完成！")

if __name__ == "__main__":
    main() 