<div class="politicians-container">
  <div class="page-header">
    <h1>政治人物分析</h1>
    <p>Political Figures Analysis</p>
  </div>

  <div class="category-tabs">
    <button 
      *ngFor="let category of categories" 
      [class.active]="selectedCategory === category.id"
      (click)="selectCategory(category.id)"
      class="tab-button">
      <i [class]="category.icon"></i>
      <span>{{ category.name }}</span>
    </button>
  </div>

  <div class="politicians-grid">
    <div 
      *ngFor="let politician of filteredPoliticians" 
      class="politician-card"
      (click)="viewPoliticianDetail(politician)">
      <div class="politician-image">
        <img [src]="politician.image" [alt]="politician.name" 
             (error)="onImageError($event)">
        <div class="politician-badge">
          <i [class]="getCategoryIcon(politician.category)"></i>
        </div>
      </div>
      <div class="politician-info">
        <h3>{{ politician.name }}</h3>
        <p class="politician-title">{{ politician.title }}</p>
        <p class="politician-region">{{ politician.region }}</p>
        <div class="politician-stats">
          <div class="stat-item">
            <i class="fas fa-chart-line"></i>
            <span>支持度: {{ politician.supportRate }}%</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-comments"></i>
            <span>{{ politician.commentCount }} 則評論</span>
          </div>
        </div>
      </div>
      <div class="politician-actions">
        <button class="btn-detail">
          <i class="fas fa-chart-bar"></i>
          詳細分析
        </button>
      </div>
    </div>
  </div>

  <div class="no-data" *ngIf="filteredPoliticians.length === 0">
    <i class="fas fa-search"></i>
    <h3>暫無數據</h3>
    <p>該類別目前沒有政治人物數據</p>
  </div>
</div>
