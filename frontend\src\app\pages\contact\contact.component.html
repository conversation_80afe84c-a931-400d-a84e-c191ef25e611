<div class="contact-container">
  <div class="contact-header">
    <h1>聯絡我們</h1>
    <p>如果您有任何問題、建議或合作提案，歡迎與我們聯繫</p>
  </div>

  <div class="contact-content">
    <div class="contact-info">
      <div class="info-card">
        <i class="fas fa-envelope"></i>
        <h3>學術研究合作</h3>
        <p>research&#64;poi.tw</p>
      </div>
      
      <div class="info-card">
        <i class="fas fa-users"></i>
        <h3>研究團隊</h3>
        <p>顏宏旭教授與學生江秉騏、李逸晨</p>
      </div>
    </div>

    <div class="contact-form">
      <h2>意見回饋表單</h2>
      <form [formGroup]="contactForm" (ngSubmit)="onSubmit()">
        <div class="form-group">
          <label for="name">姓名 *</label>
          <input 
            type="text" 
            id="name" 
            formControlName="name" 
            placeholder="請輸入您的姓名"
            [class.error]="isFieldInvalid('name')"
          >
          <div class="error-message" *ngIf="isFieldInvalid('name')">
            請輸入姓名
          </div>
        </div>

        <div class="form-group">
          <label for="email">電子郵件 *</label>
          <input 
            type="email" 
            id="email" 
            formControlName="email" 
            placeholder="請輸入您的電子郵件"
            [class.error]="isFieldInvalid('email')"
          >
          <div class="error-message" *ngIf="isFieldInvalid('email')">
            請輸入有效的電子郵件地址
          </div>
        </div>

        <div class="form-group">
          <label for="phone">聯絡電話</label>
          <input 
            type="tel" 
            id="phone" 
            formControlName="phone" 
            placeholder="請輸入您的聯絡電話"
          >
        </div>

        <div class="form-group">
          <label for="subject">主旨 *</label>
          <select id="subject" formControlName="subject" [class.error]="isFieldInvalid('subject')">
            <option value="">請選擇主旨</option>
            <option value="suggestion">建議與回饋</option>
            <option value="cooperation">學術合作</option>
            <option value="data">資料使用申請</option>
            <option value="technical">技術問題</option>
            <option value="other">其他</option>
          </select>
          <div class="error-message" *ngIf="isFieldInvalid('subject')">
            請選擇主旨
          </div>
        </div>

        <div class="form-group">
          <label for="message">意見內容 *</label>
          <textarea 
            id="message" 
            formControlName="message" 
            rows="6" 
            placeholder="請詳細描述您的意見、建議或問題"
            [class.error]="isFieldInvalid('message')"
          ></textarea>
          <div class="error-message" *ngIf="isFieldInvalid('message')">
            請輸入意見內容
          </div>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox" formControlName="agreeTerms" [class.error]="isFieldInvalid('agreeTerms')">
            <span class="checkmark"></span>
            我同意個人資料保護政策 *
          </label>
          <div class="error-message" *ngIf="isFieldInvalid('agreeTerms')">
            請同意個人資料保護政策
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn-submit" [disabled]="isSubmitting">
            <i class="fas fa-paper-plane"></i>
            {{ isSubmitting ? '發送中...' : '發送意見' }}
          </button>
          <button type="button" class="btn-reset" (click)="resetForm()">
            <i class="fas fa-undo"></i>
            重新填寫
          </button>
        </div>
      </form>
    </div>
  </div>

  <div class="contact-footer">
    <p>本平台僅供學術研究使用，所有資料僅供研究與學術展示使用</p>
    <p>© 2025 Public Opinion Index (POI). All rights reserved.</p>
  </div>
</div>
