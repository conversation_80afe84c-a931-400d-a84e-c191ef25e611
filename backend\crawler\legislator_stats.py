#!/usr/bin/env python3
"""
立委統計模組 - 簡化版本
"""

import os
import json
import logging
from datetime import datetime, timedelta
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import math
from pymongo import MongoClient

class LegislatorStatsCalculator:
    """立委統計計算器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def calculate_basic_stats(self, comments_data: List[Dict]) -> Dict:
        """計算基本統計"""
        total_comments = len(comments_data)
        
        # 計算平台分布
        platform_stats = defaultdict(int)
        for comment in comments_data:
            platform = comment.get('platform', 'unknown')
            platform_stats[platform] += 1
            
        # 計算時間分布
        time_stats = defaultdict(int)
        for comment in comments_data:
            date_str = comment.get('date', '')
            if date_str:
                try:
                    date_obj = datetime.strptime(date_str[:10], '%Y-%m-%d')
                    month_key = date_obj.strftime('%Y-%m')
                    time_stats[month_key] += 1
                except:
                    pass
        
        return {
            'total_comments': total_comments,
            'platform_distribution': dict(platform_stats),
            'time_distribution': dict(time_stats),
            'last_updated': datetime.now().isoformat()
        }

class LegislatorStatsUpdater:
    """立委統計數據更新器 - 支援 Atlas 和本地環境"""
    
    def __init__(self, database=None, mongodb_uri=None, database_name=None):
        """
        初始化統計更新器
        
        Args:
            database: 已連接的資料庫實例（優先使用）
            mongodb_uri: MongoDB 連接字串
            database_name: 資料庫名稱
        """
        if database is not None:
            # 使用已有的資料庫連接
            self.db = database
            self.client = database.client
            self.mongodb_uri = None
            self.database_name = database.name
        else:
            # 建立新連接，優先使用 Atlas 環境變數
            self.mongodb_uri = (mongodb_uri or 
                               os.getenv('MONGODB_ATLAS_URI') or 
                               os.getenv('MONGODB_URI') or 
                               "mongodb://localhost:27017/")
            self.database_name = (database_name or 
                                 os.getenv('MONGODB_ATLAS_DBNAME') or 
                                 os.getenv('MONGODB_DBNAME') or 
                                 "legislator_recall")
            self.client = MongoClient(self.mongodb_uri)
            self.db = self.client[self.database_name]
        
        self.legislators_collection = self.db.legislators
        self.crawler_data_collection = self.db.crawler_data
        self.calculator = LegislatorStatsCalculator()
        self.logger = logging.getLogger(__name__)
        
    def update_legislator_stats(self, legislator_name: str, force_recalculate: bool = False) -> bool:
        """更新單一立委統計 - 支援新的用戶合併結構"""
        try:
            print(f"📊 開始更新 {legislator_name} 的統計數據...")
            
            # 獲取立委的所有用戶資料（新結構：每個用戶一筆記錄）
            users_cursor = self.crawler_data_collection.find({
                'legislator_name': legislator_name
            })
            
            users_data = list(users_cursor)
            
            if not users_data:
                print(f"⚠️ 找不到 {legislator_name} 的資料")
                return False
            
            # 計算統計數據
            stats = self._calculate_comprehensive_stats(users_data, legislator_name)
            
            # 更新到 legislators collection
            update_doc = {
                'legislator_name': legislator_name,
                'last_updated': datetime.now(),
                'total_comments': stats['total_comments'],
                'total_users': stats['total_users'],
                'date_range': stats['date_range'],
                'platform_distribution': stats['platform_distribution'],
                'sentiment_distribution': stats['sentiment_distribution'],
                'emotion_distribution': stats['emotion_distribution'],
                'time_periods': stats['time_periods']
            }
            
            result = self.legislators_collection.update_one(
                {'legislator_name': legislator_name},
                {'$set': update_doc},
                upsert=True
            )
            
            print(f"✅ {legislator_name} 統計更新成功:")
            print(f"   📝 總留言數: {stats['total_comments']}")
            print(f"   👥 總用戶數: {stats['total_users']}")
            print(f"   📅 資料時間範圍: {stats['date_range']['start']} ~ {stats['date_range']['end']}")
            print(f"   📱 平台分佈: {stats['platform_distribution']}")
            
            return True
            
        except Exception as e:
            print(f"❌ {legislator_name} 統計更新失敗: {e}")
            import traceback
            print(traceback.format_exc())
            return False
    
    def _calculate_comprehensive_stats(self, users_data: List[Dict], legislator_name: str) -> Dict:
        """計算完整的統計數據"""
        
        # 基本統計
        total_users = len(users_data)
        total_comments = sum(user.get('total_comments', 0) for user in users_data)
        
        # 平台分佈統計
        platform_distribution = defaultdict(int)
        for user in users_data:
            platforms = user.get('platforms', {})
            for platform, count in platforms.items():
                platform_distribution[platform] += count
        
        # 情感分佈統計
        sentiment_distribution = defaultdict(int)
        for user in users_data:
            sentiment_summary = user.get('sentiment_summary', {})
            sentiment_dist = sentiment_summary.get('distribution', {})
            for sentiment, count in sentiment_dist.items():
                sentiment_distribution[sentiment] += count
        
        # 情緒分佈統計
        emotion_distribution = defaultdict(int)
        for user in users_data:
            emotion_summary = user.get('emotion_summary', {})
            emotion_dist = emotion_summary.get('distribution', {})
            for emotion, count in emotion_dist.items():
                emotion_distribution[emotion] += count
        
        # 時間範圍統計
        all_dates = []
        for user in users_data:
            latest_time = user.get('latest_comment_time', '')
            if latest_time:
                try:
                    if isinstance(latest_time, str):
                        date_obj = datetime.strptime(latest_time[:10], '%Y-%m-%d')
                    else:
                        date_obj = latest_time
                    all_dates.append(date_obj)
                except:
                    pass
        
        if all_dates:
            start_date = min(all_dates)
            end_date = max(all_dates)
        else:
            start_date = datetime.now()
            end_date = datetime.now()
        
        # 時間週期統計
        time_periods = self._calculate_time_periods(users_data)
        
        return {
            'total_comments': total_comments,
            'total_users': total_users,
            'date_range': {
                'start': start_date.strftime('%Y-%m-%d'),
                'end': end_date.strftime('%Y-%m-%d')
            },
            'platform_distribution': dict(platform_distribution),
            'sentiment_distribution': dict(sentiment_distribution),
            'emotion_distribution': dict(emotion_distribution),
            'time_periods': time_periods
        }
    
    def _calculate_time_periods(self, users_data: List[Dict]) -> Dict:
        """計算不同時間週期的統計"""
        now = datetime.now()
        periods = {
            '1w': {'start': now - timedelta(days=7)},
            '2w': {'start': now - timedelta(days=14)},
            '1m': {'start': now - timedelta(days=30)},
            '3m': {'start': now - timedelta(days=90)},
            '6m': {'start': now - timedelta(days=180)},
            '1y': {'start': now - timedelta(days=365)}
        }
        
        results = {}
        
        for period_name, period_info in periods.items():
            period_start = period_info['start']
            
            # 統計該時間週期內的數據
            period_users = 0
            period_comments = 0
            period_platforms = defaultdict(int)
            period_sentiments = defaultdict(int)
            period_emotions = defaultdict(int)
            
            for user in users_data:
                latest_time = user.get('latest_comment_time', '')
                if latest_time:
                    try:
                        if isinstance(latest_time, str):
                            user_date = datetime.strptime(latest_time[:10], '%Y-%m-%d')
                        else:
                            user_date = latest_time
                        
                        if user_date >= period_start:
                            period_users += 1
                            period_comments += user.get('total_comments', 0)
                            
                            # 平台分佈
                            platforms = user.get('platforms', {})
                            for platform, count in platforms.items():
                                period_platforms[platform] += count
                            
                            # 情感分佈
                            sentiment_summary = user.get('sentiment_summary', {})
                            sentiment_dist = sentiment_summary.get('distribution', {})
                            for sentiment, count in sentiment_dist.items():
                                period_sentiments[sentiment] += count
                            
                            # 情緒分佈
                            emotion_summary = user.get('emotion_summary', {})
                            emotion_dist = emotion_summary.get('distribution', {})
                            for emotion, count in emotion_dist.items():
                                period_emotions[emotion] += count
                    except:
                        pass
            
            results[period_name] = {
                'total_comments': period_comments,
                'total_users': period_users,
                'platform_distribution': dict(period_platforms),
                'sentiment_distribution': dict(period_sentiments),
                'emotion_distribution': dict(period_emotions)
            }
        
        return results
    
    def update_all_legislators_stats(self, force_recalculate: bool = False) -> Dict[str, bool]:
        """更新所有立委統計"""
        # 獲取所有有資料的立委
        legislators = self.crawler_data_collection.distinct('legislator_name')
        results = {}

        for legislator in legislators:
            if legislator:  # 過濾空值
                results[legislator] = self.update_legislator_stats(
                    legislator, force_recalculate
                )

        return results

    def update_all_legislators(self, force_recalculate: bool = False) -> Dict[str, bool]:
        """更新所有立委統計 - 別名方法"""
        return self.update_all_legislators_stats(force_recalculate)
    
    def get_legislator_stats(self, legislator_name: str) -> Optional[Dict]:
        """獲取立委統計"""
        doc = self.legislators_collection.find_one({
            'legislator_name': legislator_name
        })
        
        if doc:
            return doc.get('stats', {})
        return None
    
    def get_all_legislators_summary(self) -> List[Dict]:
        """獲取所有立委摘要"""
        docs = self.legislators_collection.find({}, {
            'legislator_name': 1,
            'stats.total_comments': 1,
            'last_updated': 1
        })
        
        results = []
        for doc in docs:
            stats = doc.get('stats', {})
            results.append({
                'legislator_name': doc.get('legislator_name'),
                'total_comments': stats.get('total_comments', 0),
                'last_updated': doc.get('last_updated')
            })
        
        # 按留言數量排序
        results.sort(key=lambda x: x['total_comments'], reverse=True)
        return results

class LegislatorStatsAPI:
    """立委統計數據 API"""
    
    def __init__(self, mongodb_uri=None, database_name=None):
        self.mongodb_uri = mongodb_uri or "mongodb://localhost:27017/"
        self.database_name = database_name or "legislator_recall"
        
        try:
            self.client = MongoClient(self.mongodb_uri)
            self.db = self.client[self.database_name]
            self.legislators_collection = self.db.legislators
            self.stats_updater = LegislatorStatsUpdater(self.client)
            self.logger = logging.getLogger(__name__)
            self.logger.info("✅ 統計API連接成功")
        except Exception as e:
            self.logger = logging.getLogger(__name__)
            self.logger.error(f"❌ 統計API連接失敗: {e}")
            self.client = None
            self.db = None
            self.legislators_collection = None
            self.stats_updater = None
    
    def get_legislator_chart_data(self, legislator_name: str, period: str = '1m') -> Optional[Dict]:
        """
        獲取立委的圖表數據（前端直接使用）
        
        Args:
            legislator_name: 立委姓名
            period: 時間週期 ('1y', '6m', '3m', '1m', '2w', '1w')
            
        Returns:
            前端圖表所需的數據格式
        """
        if not self.legislators_collection:
            return None
        
        try:
            stats = self.legislators_collection.find_one({
                'legislator_name': legislator_name
            })
            
            if not stats or 'time_periods' not in stats:
                return None
            
            period_data = stats['time_periods'].get(period)
            if not period_data:
                return None
            
            # 轉換為前端圖表格式
            chart_data = {
                'legislator_name': legislator_name,
                'period': period,
                'period_name': period_data.get('period_name', period),
                'last_updated': stats.get('last_updated'),
                'summary': period_data.get('summary', {}),
                'chart_points': []
            }
            
            # 轉換數據點格式
            for point in period_data.get('data_points', []):
                chart_point = {
                    'date': point['date'],
                    'timestamp': point['timestamp'],
                    'comments': point['cumulative_comments'],
                    'users': point['cumulative_users'],
                    'platforms': point.get('platform_breakdown', {}),
                    'sentiments': point.get('sentiment_breakdown', {}),
                    'progress': point.get('period_progress', 0)
                }
                chart_data['chart_points'].append(chart_point)
            
            return chart_data
            
        except Exception as e:
            self.logger.error(f"❌ 獲取 {legislator_name} 圖表數據時出錯: {e}")
            return None
    
    def get_all_legislators_overview(self) -> List[Dict]:
        """
        獲取所有立委的概覽數據
        
        Returns:
            所有立委的摘要列表
        """
        if not self.legislators_collection:
            return []
        
        try:
            legislators = []
            cursor = self.legislators_collection.find({}, {
                'legislator_name': 1,
                'summary': 1,
                'last_updated': 1,
                'time_periods.1m.summary': 1
            })
            
            for doc in cursor:
                overview = {
                    'legislator_name': doc.get('legislator_name'),
                    'last_updated': doc.get('last_updated'),
                    'total_comments': doc.get('summary', {}).get('total_comments', 0),
                    'total_users': doc.get('summary', {}).get('total_users', 0),
                    'platforms': list(doc.get('summary', {}).get('platform_distribution', {}).keys()),
                    'recent_activity': doc.get('time_periods', {}).get('1m', {}).get('summary', {})
                }
                legislators.append(overview)
            
            # 按總留言數排序
            legislators.sort(key=lambda x: x['total_comments'], reverse=True)
            return legislators
            
        except Exception as e:
            self.logger.error(f"❌ 獲取立委概覽時出錯: {e}")
            return []
    
    def get_platform_breakdown(self, legislator_name: str) -> Optional[Dict]:
        """
        獲取立委的平台分佈詳細數據
        
        Args:
            legislator_name: 立委姓名
            
        Returns:
            平台分佈數據
        """
        if not self.legislators_collection:
            return None
        
        try:
            stats = self.legislators_collection.find_one({
                'legislator_name': legislator_name
            }, {
                'summary.platform_distribution': 1,
                'time_periods': 1
            })
            
            if not stats:
                return None
            
            platform_data = {
                'legislator_name': legislator_name,
                'overall_distribution': stats.get('summary', {}).get('platform_distribution', {}),
                'platforms': []
            }
            
            # 獲取各週期的平台數據
            time_periods = stats.get('time_periods', {})
            for platform in platform_data['overall_distribution'].keys():
                platform_stats = {
                    'platform': platform,
                    'total_comments': platform_data['overall_distribution'][platform],
                    'periods': {}
                }
                
                for period, period_data in time_periods.items():
                    period_platforms = period_data.get('summary', {}).get('platform_distribution', {})
                    platform_stats['periods'][period] = period_platforms.get(platform, 0)
                
                platform_data['platforms'].append(platform_stats)
            
            return platform_data
            
        except Exception as e:
            self.logger.error(f"❌ 獲取 {legislator_name} 平台分佈時出錯: {e}")
            return None
    
    def get_period_comparison(self, legislator_name: str) -> Optional[Dict]:
        """
        獲取立委的時間週期比較數據
        
        Args:
            legislator_name: 立委姓名
            
        Returns:
            週期比較數據
        """
        if not self.legislators_collection:
            return None
        
        try:
            stats = self.legislators_collection.find_one({
                'legislator_name': legislator_name
            }, {
                'time_periods': 1
            })
            
            if not stats or 'time_periods' not in stats:
                return None
            
            comparison = {
                'legislator_name': legislator_name,
                'periods': {}
            }
            
            for period, period_data in stats['time_periods'].items():
                summary = period_data.get('summary', {})
                comparison['periods'][period] = {
                    'period_name': period_data.get('period_name', period),
                    'total_comments': summary.get('total_comments', 0),
                    'total_users': summary.get('total_users', 0),
                    'avg_comments_per_user': summary.get('avg_comments_per_user', 0),
                    'platform_count': len(summary.get('platform_distribution', {})),
                    'date_range': period_data.get('date_range')
                }
            
            return comparison
            
        except Exception as e:
            self.logger.error(f"❌ 獲取 {legislator_name} 週期比較時出錯: {e}")
            return None

def test_api():
    """測試 API 功能"""
    api = LegislatorStatsAPI()
    logger = logging.getLogger(__name__)
    
    # 測試獲取立委列表
    legislators = api.get_all_legislators_overview()
    logger.info(f"找到 {len(legislators)} 位立委")
    
    if legislators:
        # 測試第一位立委的數據
        first_legislator = legislators[0]['legislator_name']
        logger.info(f"測試立委: {first_legislator}")
        
        # 測試各種查詢
        chart_data = api.get_legislator_chart_data(first_legislator, '1m')
        if chart_data:
            logger.info(f"圖表數據點數: {len(chart_data['chart_points'])}")
        
        platform_data = api.get_platform_breakdown(first_legislator)
        if platform_data:
            logger.info(f"平台數量: {len(platform_data['platforms'])}")
        
        comparison = api.get_period_comparison(first_legislator)
        if comparison:
            logger.info(f"週期比較: {list(comparison['periods'].keys())}")

# 主程式保持向後兼容
def update_legislator_stats(force_recalc=False):
    """更新立委統計資料（向後兼容函數）"""
    logger = logging.getLogger(__name__)
    try:
        # 使用環境變數或本地預設值
        mongodb_uri = (os.getenv('MONGODB_ATLAS_URI') or 
                       os.getenv('MONGODB_URI') or 
                       "mongodb://localhost:27017/")
        client = MongoClient(mongodb_uri)
        db = client["legislator_recall"]
        
        if mongodb_uri.startswith('mongodb+srv://'):
            print("連接到 MongoDB Atlas")
        else:
            print("連接到本地 MongoDB")
        
        updater = LegislatorStatsUpdater(db)
        updater.update_all_legislators()
        logger.info("✅ 統計資料更新完成")
    except Exception as e:
        logger.error(f"❌ 統計資料更新失敗: {e}")
        raise

if __name__ == "__main__":
    # 測試功能
    logging.basicConfig(level=logging.INFO)
    result = update_legislator_stats()
    print(f"統計更新結果: {result}")
