#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化代理管理器 - 按需获取，动态替换
"""

import requests
import random
import time
import threading
import logging
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)

class ProxyManager:
    """优化代理管理器 - 按需获取，动态替换"""
    
    def __init__(self, target_count: int = 8, max_workers: int = 6, disabled: bool = False):
        self.target_count = target_count  # 目标代理数量
        self.max_workers = max_workers    # 最大工作线程数
        self.disabled = disabled          # 是否禁用代理功能
        self.working_proxies = []
        self.failed_proxies = []
        self.lock = threading.Lock()
        self.last_update = 0
        self.update_interval = 300  # 5分钟更新一次
        self.min_proxy_count = max(3, target_count // 2)  # 最小代理数量
        
        # 修复：只有在未禁用时才初始化代理池
        if not self.disabled:
            self._initialize_proxy_pool()
        else:
            logger.info("代理功能已禁用，跳过代理池初始化")
    
    def _initialize_proxy_pool(self):
        """初始化代理池"""
        logger.info("初始化代理池...")
        self.update_proxy_pool(force=True)
        
        # 如果初始化后仍然没有代理，添加一些备用代理
        if len(self.working_proxies) == 0:
            logger.warning("初始化后没有可用代理，添加备用代理...")
            backup_proxies = [
                {'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'},
                {'http': 'http://127.0.0.1:1080', 'https': 'http://127.0.0.1:1080'},
                {'http': 'http://127.0.0.1:3128', 'https': 'http://127.0.0.1:3128'},
            ]
            self.working_proxies.extend(backup_proxies)
            logger.info(f"已添加 {len(backup_proxies)} 个备用代理")
        
        logger.info(f"代理池初始化完成，可用代理: {len(self.working_proxies)}/{self.target_count}")
    
    def get_free_proxies(self, count: int = None) -> List[Dict[str, str]]:
        """获取指定数量的免费代理IP - 增强版本"""
        if count is None:
            count = self.target_count
            
        proxy_list = []
        
        try:
            # 优先使用新的代理爬取器
            try:
                from proxy_scraper import ProxyScraper
                scraper = ProxyScraper()
                scraped_proxies = scraper.get_working_proxies(max_proxies=count)
                
                for proxy_dict in scraped_proxies:
                    proxy = {
                        'http': proxy_dict['proxy'],
                        'https': proxy_dict['proxy']
                    }
                    proxy_list.append(proxy)
                
                logger.info(f"从代理爬取器获取到 {len(proxy_list)} 个代理")
                
            except ImportError:
                logger.warning("代理爬取器不可用，使用备用方法")
            except Exception as e:
                logger.warning(f"代理爬取器失败: {e}")
            
            # 如果爬取器获取的代理不够，使用备用方法
            if len(proxy_list) < count:
                logger.info(f"代理数量不足 ({len(proxy_list)}/{count})，使用备用方法...")
                
                # 备用代理源
                urls = [
                    "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all",
                    "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
                    "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt",
                    "https://raw.githubusercontent.com/fate0/proxylist/master/proxy.list",
                    "https://raw.githubusercontent.com/a2u/free-proxy-list/master/free-proxy-list.txt"
                ]
                
                for url in urls:
                    if len(proxy_list) >= count:
                        break
                        
                    try:
                        logger.info(f"从 {url} 获取代理...")
                        response = requests.get(url, timeout=10)
                        if response.status_code == 200:
                            lines = response.text.strip().split('\n')
                            for line in lines:
                                if len(proxy_list) >= count:
                                    break
                                if ':' in line:
                                    host, port = line.split(':')[:2]
                                    # 验证端口号
                                    try:
                                        port_int = int(port)
                                        if 1 <= port_int <= 65535:
                                            proxy = {
                                                'http': f'http://{host}:{port}',
                                                'https': f'http://{host}:{port}'
                                            }
                                            proxy_list.append(proxy)
                                    except ValueError:
                                        continue
                            logger.info(f"从 {url} 获取到代理")
                    except Exception as e:
                        logger.warning(f"获取代理失败 {url}: {e}")
                        continue
                        
        except Exception as e:
            logger.error(f"获取免费代理失败: {e}")
        
        # 如果获取的代理不够，添加一些备用代理
        if len(proxy_list) < count:
            logger.warning(f"获取的代理数量不足 ({len(proxy_list)}/{count})，添加备用代理...")
            backup_proxies = [
                {'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'},
                {'http': 'http://127.0.0.1:1080', 'https': 'http://127.0.0.1:1080'},
                {'http': 'http://127.0.0.1:3128', 'https': 'http://127.0.0.1:3128'},
            ]
            proxy_list.extend(backup_proxies)
            logger.info(f"已添加 {len(backup_proxies)} 个备用代理")
        
        return proxy_list[:count]
    
    def update_proxy_pool(self, force: bool = False):
        """更新代理池 - 增强版本"""
        with self.lock:
            current_time = time.time()
            if not force and current_time - self.last_update < self.update_interval:
                return
            
            # 检查当前可用代理数量
            current_count = len(self.working_proxies)
            needed_count = max(0, self.target_count - current_count)
            
            if needed_count == 0 and not force:
                logger.info(f"代理池充足，当前可用: {current_count}/{self.target_count}")
                return
            
            logger.info(f"更新代理池... (需要: {needed_count}, 当前: {current_count})")
            
            # 获取新代理
            new_proxies = self.get_free_proxies(needed_count + 5)  # 多获取几个作为备用
            
            if not new_proxies:
                logger.warning("无法获取新代理，使用现有代理")
                return
            
            # 测试代理 - 使用更少的并发数，避免资源竞争
            working_proxies = []
            max_test_workers = min(3, len(new_proxies))
            
            with ThreadPoolExecutor(max_workers=max_test_workers) as executor:
                future_to_proxy = {
                    executor.submit(self._test_proxy, proxy): proxy 
                    for proxy in new_proxies
                }
                
                for future in as_completed(future_to_proxy):
                    proxy = future_to_proxy[future]
                    try:
                        if future.result():
                            working_proxies.append(proxy)
                            if len(working_proxies) >= needed_count:
                                break
                    except Exception as e:
                        logger.warning(f"代理测试失败: {e}")
            
            # 更新代理池
            if working_proxies:
                self.working_proxies.extend(working_proxies)
                logger.info(f"✅ 代理池更新完成，新增 {len(working_proxies)} 个可用代理")
            else:
                logger.warning("⚠️ 没有获取到可用代理")
            
            self.last_update = current_time
    
    def _test_proxy(self, proxy: Dict[str, str]) -> bool:
        """测试代理是否可用 - 优化版"""
        try:
            response = requests.get(
                'https://httpbin.org/ip',
                proxies=proxy,
                timeout=3  # 快速失败
            )
            
            if response.status_code == 200:
                data = response.json()
                origin_ip = data.get('origin', '')
                
                # 检查是否是匿名代理
                if origin_ip != proxy.get('http', '').split('://')[1].split(':')[0]:
                    logger.info(f"匿名代理可用: {proxy}")
                    return True
                else:
                    logger.info(f"透明代理可用: {proxy}")
                    return True
            else:
                logger.warning(f"代理响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            logger.debug(f"代理测试失败: {e}")
            return False
    
    def get_proxy(self) -> Optional[Dict[str, str]]:
        """获取一个可用代理"""
        with self.lock:
            if not self.working_proxies:
                self.update_proxy_pool(force=True)
            
            if self.working_proxies:
                proxy = random.choice(self.working_proxies)
                logger.info(f"使用代理: {proxy}")
                return proxy
            else:
                logger.warning("没有可用代理")
                return None
    
    def mark_proxy_failed(self, proxy: Dict[str, str]):
        """标记代理失败"""
        with self.lock:
            if proxy in self.working_proxies:
                self.working_proxies.remove(proxy)
                self.failed_proxies.append(proxy)
                logger.warning(f"代理失败，已移除: {proxy}")
                
                # 如果可用代理太少，尝试更新
                if len(self.working_proxies) < self.min_proxy_count:
                    logger.info("可用代理不足，尝试更新代理池...")
                    self.update_proxy_pool(force=True)
    
    def get_proxy_stats(self) -> Dict[str, int]:
        """获取代理统计信息"""
        with self.lock:
            return {
                'working': len(self.working_proxies),
                'failed': len(self.failed_proxies),
                'total': len(self.working_proxies) + len(self.failed_proxies)
            }
    
    def force_change_ip_for_thread(self, thread_id: int):
        """强制为指定线程更换IP"""
        with self.lock:
            if self.working_proxies:
                # 随机选择一个新代理
                new_proxy = random.choice(self.working_proxies)
                logger.info(f"线程 {thread_id} 强制更换IP: {new_proxy}")
                return new_proxy
            else:
                logger.warning(f"线程 {thread_id} 无法更换IP，没有可用代理")
                return None

# 全局代理管理器实例 - 针对6-8个线程优化
_proxy_manager = None

def get_proxy_manager(disabled: bool = False) -> ProxyManager:
    """获取代理管理器实例"""
    global _proxy_manager
    if _proxy_manager is None:
        _proxy_manager = ProxyManager(target_count=8, max_workers=6, disabled=disabled)
    return _proxy_manager

def get_proxy_for_selenium() -> Optional[Dict[str, str]]:
    """获取用于Selenium的代理"""
    manager = get_proxy_manager()
    return manager.get_proxy()

def mark_proxy_failed(proxy: Dict[str, str]):
    """标记代理失败"""
    manager = get_proxy_manager()
    manager.mark_proxy_failed(proxy)

def force_change_ip_for_thread(thread_id: int):
    """强制为指定线程更换IP"""
    manager = get_proxy_manager()
    return manager.force_change_ip_for_thread(thread_id)

def get_proxy_stats() -> Dict[str, int]:
    """获取代理统计信息"""
    manager = get_proxy_manager()
    return manager.get_proxy_stats() 