import os
import shutil
import json
import datetime
from pathlib import Path
from typing import Dict, List, Set, Optional
import logging

class BackupManager:
    """
    備份管理器：負責在抓取前備份現有資料到歷史記錄
    """
    
    def __init__(self, base_dir: str = "backend"):
        # 確定當前工作目錄
        current_dir = Path.cwd()
        
        # 檢查是否在backend目錄下執行
        if (current_dir / "crawler").exists():
            # 在backend目錄下執行
            self.base_dir = current_dir
            self.crawler_dir = current_dir / "crawler"
            self.backup_dir = current_dir / "backups"
            self.original_temp_dir = current_dir / "temp"
        else:
            # 在專案根目錄下執行
            self.base_dir = Path(base_dir)
            self.crawler_dir = self.base_dir / "crawler"
            self.backup_dir = self.base_dir / "backups"
            self.original_temp_dir = self.base_dir / "temp"
        
        # 備份目錄結構
        self.history_dir = self.backup_dir / "CRAWLER_HISTORY"
        self.data_history_dir = self.history_dir / "data_history"
        self.href_history_dir = self.history_dir / "href_history"
        self.processed_history_dir = self.history_dir / "processed_history"
        self.temp_history_dir = self.history_dir / "temp_history"
        
        # 原始資料目錄
        self.original_data_dir = self.crawler_dir / "data"
        self.original_href_dir = self.crawler_dir / "href"
        self.original_processed_dir = self.crawler_dir / "processed"
        
        print(f"🔍 備份管理器初始化:")
        print(f"  - 當前目錄: {current_dir}")
        print(f"  - 基礎目錄: {self.base_dir}")
        print(f"  - 爬蟲目錄: {self.crawler_dir}")
        print(f"  - 資料目錄: {self.original_data_dir}")
        print(f"  - 備份目錄: {self.backup_dir}")
        
        self._ensure_directories()
        
    def _ensure_directories(self):
        """確保所有必要的目錄存在"""
        directories = [
            self.backup_dir,
            self.history_dir,
            self.data_history_dir,
            self.href_history_dir,
            self.processed_history_dir,
            self.temp_history_dir  # 新增temp備份目錄
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ 確保目錄存在: {directory}")
    
    def _get_timestamp_filename(self, legislator_name: str) -> str:
        """生成時間戳檔案名：執行年月日_{立委}.json"""
        today = datetime.datetime.now().strftime("%Y%m%d")
        return f"{today}_{legislator_name}.json"
    
    def backup_all_legislators(self):
        """備份所有立委的資料"""
        print("🔄 開始備份所有立委資料...")
        
        # 獲取所有立委列表
        legislators = self._get_all_legislators()
        
        if not legislators:
            print("✅ 未找到任何立委資料，跳過備份步驟")
            return
        
        print(f"📋 找到 {len(legislators)} 位立委，開始備份...")
        
        for legislator in legislators:
            try:
                backup_paths = self.backup_existing_data(legislator)
                print(f"✅ {legislator} 備份完成")
            except Exception as e:
                print(f"❌ {legislator} 備份失敗: {e}")
        
        print("🎉 所有立委備份完成！")
    
    def _get_all_legislators(self) -> List[str]:
        """獲取所有立委列表"""
        legislators = set()
        
        print(f"🔍 快速檢查是否有現有資料...")
        
        # 快速檢查是否有任何數據
        has_data = False
        
        # 從data目錄獲取
        if self.original_data_dir.exists():
            for platform_dir in self.original_data_dir.iterdir():
                if platform_dir.is_dir():
                    json_files = list(platform_dir.glob("*.json"))
                    if json_files:
                        has_data = True
                        for json_file in json_files:
                            legislator_name = json_file.stem
                            legislators.add(legislator_name)
        
        # 從href目錄獲取
        if self.original_href_dir.exists():
            for platform_dir in self.original_href_dir.iterdir():
                if platform_dir.is_dir():
                    json_files = list(platform_dir.glob("*.json"))
                    if json_files:
                        has_data = True
                        for json_file in json_files:
                            legislator_name = json_file.stem
                            legislators.add(legislator_name)
        
        # 從processed目錄獲取
        if self.original_processed_dir.exists():
            for subdir in ["alldata", "user_data", "final_data"]:
                subdir_path = self.original_processed_dir / subdir
                if subdir_path.exists():
                    json_files = list(subdir_path.glob("*.json"))
                    if json_files:
                        has_data = True
                        for json_file in json_files:
                            legislator_name = json_file.stem
                            legislators.add(legislator_name)
        
        if not has_data:
            print("✅ 未發現任何現有資料，無需備份")
            return []
        
        legislators_list = list(legislators)
        print(f"🎯 找到 {len(legislators_list)} 位立委資料需要備份: {legislators_list}")
        return legislators_list
    
    def backup_existing_data(self, legislator_name: str) -> Dict[str, str]:
        """
        備份現有資料到歷史記錄，並刪除原檔案
        
        Args:
            legislator_name: 立委姓名
            
        Returns:
            Dict: 備份的檔案路徑
        """
        backup_paths = {}
        timestamp_filename = self._get_timestamp_filename(legislator_name)
        
        print(f"🔄 開始備份 {legislator_name} 的現有資料...")
        
        # 1. 備份 data 目錄
        backup_paths['data'] = self._backup_platform_data(
            self.original_data_dir, 
            self.data_history_dir, 
            legislator_name, 
            timestamp_filename
        )
        
        # 2. 備份 href 目錄
        backup_paths['href'] = self._backup_platform_data(
            self.original_href_dir, 
            self.href_history_dir, 
            legislator_name, 
            timestamp_filename
        )
        
        # 3. 備份 processed 目錄
        backup_paths['processed'] = self._backup_processed_data(
            legislator_name, 
            timestamp_filename
        )
        
        # 4. 備份 temp 目錄
        backup_paths['temp'] = self._backup_temp_data(
            legislator_name, 
            timestamp_filename
        )
        
        # 5. 備份完成後，刪除原檔案
        print(f"🧹 備份完成，開始清理原檔案...")
        self.clear_existing_data(legislator_name)
        
        print(f"✅ 備份完成！備份檔案：{backup_paths}")
        return backup_paths
    
    def _backup_platform_data(self, source_dir: Path, target_dir: Path, 
                             legislator_name: str, timestamp_filename: str) -> str:
        """備份平台資料（data 和 href）"""
        backup_files = []
        
        # 遍歷所有平台目錄
        for platform_dir in source_dir.iterdir():
            if platform_dir.is_dir():
                platform_name = platform_dir.name
                legislator_file = platform_dir / f"{legislator_name}.json"
                
                if legislator_file.exists():
                    # 🆕 PTT URL文件不需要备份，保持累积
                    if platform_name == "ptt" and source_dir.name == "href":
                        print(f"  📁 跳过PTT URL备份: {platform_name}/{legislator_name}.json (持续累积)")
                        continue
                    
                    # 創建備份檔案路徑
                    backup_file = target_dir / f"{platform_name}_{timestamp_filename}"
                    
                    try:
                        # 複製檔案
                        shutil.copy2(legislator_file, backup_file)
                        backup_files.append(str(backup_file))
                        print(f"  📁 備份 {platform_name}/{legislator_name}.json → {backup_file.name}")
                    except Exception as e:
                        print(f"  ❌ 備份 {platform_name}/{legislator_name}.json 失敗: {e}")
        
        return ", ".join(backup_files) if backup_files else "無資料"
    
    def _backup_processed_data(self, legislator_name: str, timestamp_filename: str) -> str:
        """備份處理後的資料（processed）"""
        backup_files = []
        
        # 備份 alldata
        alldata_file = self.original_processed_dir / "alldata" / f"{legislator_name}.json"
        if alldata_file.exists():
            backup_file = self.processed_history_dir / f"alldata_{timestamp_filename}"
            try:
                shutil.copy2(alldata_file, backup_file)
                backup_files.append(str(backup_file))
                print(f"  📁 備份 alldata/{legislator_name}.json → {backup_file.name}")
            except Exception as e:
                print(f"  ❌ 備份 alldata/{legislator_name}.json 失敗: {e}")
        
        # 備份 user_data
        user_data_file = self.original_processed_dir / "user_data" / f"{legislator_name}.json"
        if user_data_file.exists():
            backup_file = self.processed_history_dir / f"user_data_{timestamp_filename}"
            try:
                shutil.copy2(user_data_file, backup_file)
                backup_files.append(str(backup_file))
                print(f"  📁 備份 user_data/{legislator_name}.json → {backup_file.name}")
            except Exception as e:
                print(f"  ❌ 備份 user_data/{legislator_name}.json 失敗: {e}")
        
        # 備份 final_data
        final_data_file = self.original_processed_dir / "final_data" / f"{legislator_name}.json"
        if final_data_file.exists():
            backup_file = self.processed_history_dir / f"final_data_{timestamp_filename}"
            try:
                shutil.copy2(final_data_file, backup_file)
                backup_files.append(str(backup_file))
                print(f"  📁 備份 final_data/{legislator_name}.json → {backup_file.name}")
            except Exception as e:
                print(f"  ❌ 備份 final_data/{legislator_name}.json 失敗: {e}")
        
        return ", ".join(backup_files) if backup_files else "無資料"
    
    def _backup_temp_data(self, legislator_name: str, timestamp_filename: str) -> str:
        """備份temp目錄資料"""
        backup_files = []
        
        if not self.original_temp_dir.exists():
            return "temp目錄不存在"
        
        # 備份立委的temp子目錄
        safe_name = legislator_name.replace(" ", "_")
        temp_legislator_dir = self.original_temp_dir / safe_name
        
        if temp_legislator_dir.exists():
            # 創建備份目錄
            backup_legislator_dir = self.temp_history_dir / f"{safe_name}_{timestamp_filename}"
            
            try:
                # 複製整個目錄
                import shutil
                shutil.copytree(temp_legislator_dir, backup_legislator_dir)
                backup_files.append(str(backup_legislator_dir))
                print(f"  📁 備份 temp/{safe_name}/ → {backup_legislator_dir.name}")
            except Exception as e:
                print(f"  ❌ 備份 temp/{safe_name}/ 失敗: {e}")
        
        return ", ".join(backup_files) if backup_files else "無temp資料"
    
    def clear_existing_data(self, legislator_name: str):
        """
        清理現有資料（備份後執行）
        
        Args:
            legislator_name: 立委姓名
        """
        print(f"🧹 清理 {legislator_name} 的現有資料...")
        
        # 清理 data 目錄
        self._clear_platform_data(self.original_data_dir, legislator_name)
        
        # 清理 href 目錄
        self._clear_platform_data(self.original_href_dir, legislator_name)
        
        # 清理 processed 目錄
        self._clear_processed_data(legislator_name)
        
        # 清理 temp 目錄
        self._clear_temp_data(legislator_name)
        
        print(f"✅ 清理完成！")
    
    def _clear_platform_data(self, source_dir: Path, legislator_name: str):
        """清理平台資料"""
        for platform_dir in source_dir.iterdir():
            if platform_dir.is_dir():
                platform_name = platform_dir.name
                legislator_file = platform_dir / f"{legislator_name}.json"
                
                if legislator_file.exists():
                    # 🆕 PTT URL文件不需要清理，保持累积
                    if platform_name == "ptt" and source_dir.name == "href":
                        print(f"  📁 保留PTT URL文件: {platform_name}/{legislator_name}.json (持续累积)")
                        continue
                    
                    try:
                        legislator_file.unlink()
                        print(f"  🗑️ 刪除 {platform_name}/{legislator_name}.json")
                    except Exception as e:
                        print(f"  ❌ 刪除 {platform_name}/{legislator_name}.json 失敗: {e}")
    
    def _clear_processed_data(self, legislator_name: str):
        """清理處理後的資料"""
        processed_subdirs = ["alldata", "user_data", "final_data"]
        
        for subdir in processed_subdirs:
            file_path = self.original_processed_dir / subdir / f"{legislator_name}.json"
            if file_path.exists():
                try:
                    file_path.unlink()
                    print(f"  🗑️ 刪除 {subdir}/{legislator_name}.json")
                except Exception as e:
                    print(f"  ❌ 刪除 {subdir}/{legislator_name}.json 失敗: {e}")
    
    def _clear_temp_data(self, legislator_name: str):
        """清理temp目錄資料"""
        safe_name = legislator_name.replace(" ", "_")
        temp_legislator_dir = self.original_temp_dir / safe_name
        
        if temp_legislator_dir.exists():
            try:
                import shutil
                shutil.rmtree(temp_legislator_dir)
                print(f"  🗑️ 刪除 temp/{safe_name}/")
            except Exception as e:
                print(f"  ❌ 刪除 temp/{safe_name}/ 失敗: {e}")
    
    def get_processed_user_ids(self, legislator_name: str) -> Dict[str, Dict]:
        """
        獲取已處理的用戶ID和最新分析時間，用於增量更新
        
        Args:
            legislator_name: 立委姓名
            
        Returns:
            Dict: {用戶ID: {'last_analysis_time': '2025-07-20', 'comment_count': 5}}
        """
        user_info = {}
        
        # 從歷史記錄中讀取所有已處理的用戶ID
        for backup_file in self.processed_history_dir.glob(f"*_{legislator_name}.json"):
            try:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # 提取用戶ID和最新分析時間
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            # 嘗試不同的用戶ID欄位
                            user_id = (item.get('用戶ID') or 
                                     item.get('user_id') or 
                                     item.get('username') or 
                                     item.get('用戶名') or
                                     item.get('id') or
                                     item.get('user_name'))
                            
                            if user_id:
                                user_id = str(user_id)
                                # 獲取最新分析時間
                                last_analysis_time = item.get('最新分析時間') or item.get('last_analysis_time')
                                comment_count = item.get('留言數量') or item.get('comment_count', 0)
                                
                                if user_id not in user_info:
                                    user_info[user_id] = {
                                        'last_analysis_time': last_analysis_time,
                                        'comment_count': comment_count,
                                        'last_backup_file': backup_file.name
                                    }
                                else:
                                    # 如果有多個備份，取最新的
                                    current_file_time = backup_file.stat().st_mtime
                                    existing_file_time = Path(self.processed_history_dir / user_info[user_id]['last_backup_file']).stat().st_mtime
                                    
                                    if current_file_time > existing_file_time:
                                        user_info[user_id] = {
                                            'last_analysis_time': last_analysis_time,
                                            'comment_count': comment_count,
                                            'last_backup_file': backup_file.name
                                        }
                    
            except Exception as e:
                print(f"  ⚠️ 讀取備份檔案 {backup_file.name} 失敗: {e}")
        
        print(f"📋 找到 {len(user_info)} 個已處理的用戶ID")
        return user_info
    
    def get_latest_processed_data(self, legislator_name: str) -> Optional[Dict]:
        """
        獲取最新的處理後資料，用於增量更新
        
        Args:
            legislator_name: 立委姓名
            
        Returns:
            Optional[Dict]: 最新的處理後資料
        """
        # 找到最新的備份檔案
        backup_files = list(self.processed_history_dir.glob(f"*_{legislator_name}.json"))
        if not backup_files:
            return None
        
        # 按修改時間排序，取最新的
        latest_file = max(backup_files, key=lambda x: x.stat().st_mtime)
        
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"📄 讀取最新備份資料: {latest_file.name}")
            return data
        except Exception as e:
            print(f"❌ 讀取最新備份資料失敗: {e}")
            return None
    
    def cleanup_old_backups(self, days_to_keep: int = 30):
        """
        清理舊的備份檔案
        
        Args:
            days_to_keep: 保留天數
        """
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)
        deleted_count = 0
        
        for history_dir in [self.data_history_dir, self.href_history_dir, self.processed_history_dir]:
            for backup_file in history_dir.iterdir():
                if backup_file.is_file():
                    file_time = datetime.datetime.fromtimestamp(backup_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        try:
                            backup_file.unlink()
                            deleted_count += 1
                            print(f"🗑️ 刪除舊備份: {backup_file.name}")
                        except Exception as e:
                            print(f"❌ 刪除舊備份失敗: {backup_file.name} - {e}")
        
        print(f"✅ 清理完成，刪除了 {deleted_count} 個舊備份檔案")

# 使用範例
if __name__ == "__main__":
    backup_manager = BackupManager()
    
    # 備份現有資料
    backup_paths = backup_manager.backup_existing_data("丁學忠")
    
    # 清理現有資料
    backup_manager.clear_existing_data("丁學忠")
    
    # 獲取已處理的用戶ID
    processed_users = backup_manager.get_processed_user_ids("丁學忠")
    
    # 清理舊備份
    backup_manager.cleanup_old_backups(30) 