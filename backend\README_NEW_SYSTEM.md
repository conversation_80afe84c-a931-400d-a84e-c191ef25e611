# 🚀 社會媒體分析系統 - 重新整理版

## 📋 系統概述

這是一個重新整理的社會媒體分析系統，保留了所有原有功能，但簡化為7個核心檔案，實現了更清晰的模組化架構。

## 🗂️ 檔案結構

### **核心模組檔案**

1. **`yt_crawler.py`** - YouTube爬蟲模組
   - 爬取YouTube評論數據
   - 支援多執行緒爬取
   - 自動處理反爬蟲機制

2. **`ptt_crawler.py`** - PTT爬蟲模組
   - 爬取PTT政治版文章和留言
   - 支援多執行緒爬取
   - 自動處理PTT特殊格式

3. **`thread_crawler.py`** - Threads爬蟲模組
   - 爬取Threads平台數據
   - 支援多執行緒爬取
   - 處理Instagram Threads格式

4. **`fb_crawler.py`** - Facebook爬蟲模組
   - 爬取Facebook頁面貼文和留言
   - 支援多執行緒爬取
   - 處理Facebook動態內容

5. **`data_integrator.py`** - 數據整合模組
   - 整合所有平台的爬取數據
   - 轉換為Gemini分析格式
   - 生成數據統計報告

6. **`gemini_analyzer.py`** - Gemini分析模組
   - 使用Google Gemini API進行情感分析
   - 支援批次處理和並行分析
   - 自動重試和錯誤處理

7. **`final_processor.py`** - 最終處理模組
   - 處理最終分析結果
   - 生成統計報告和驗證
   - 合併所有數據並清理

### **主控制檔案**

8. **`main_new.py`** - 主控制模組
   - 整合所有功能模組
   - 提供命令列介面
   - 管理完整工作流程

## 🔄 工作流程

```
🕷️ 爬蟲階段 → 🔄 數據整合 → 🧠 Gemini分析 → 🔧 最終處理
```

### **1. 爬蟲階段**
- 支援四個平台：YouTube、PTT、Threads、Facebook
- 可選擇性爬取特定平台
- 支援多執行緒並行爬取
- 自動處理反爬蟲和錯誤重試

### **2. 數據整合階段**
- 整合所有平台的原始數據
- 標準化數據格式
- 轉換為Gemini分析所需的格式
- 生成數據統計報告

### **3. Gemini分析階段**
- 使用Google Gemini API進行情感分析
- 支援批次處理（預設500用戶/批次）
- 真正的並行處理（批次間和批次內）
- 自動重試和錯誤處理

### **4. 最終處理階段**
- 處理分析結果
- 生成統計報告
- 驗證數據完整性
- 合併所有數據
- 清理臨時文件

## 🚀 使用方法

### **完整流程**
```bash
python main_new.py --full-pipeline
```

### **單獨執行各階段**
```bash
# 只執行爬蟲
python main_new.py --crawl-only

# 只執行數據整合
python main_new.py --integrate-only

# 只執行Gemini分析
python main_new.py --analyze-only

# 只執行最終處理
python main_new.py --process-only
```

### **指定平台和立委**
```bash
# 只爬取特定平台
python main_new.py --crawl-only --platforms youtube ptt

# 只處理特定立委
python main_new.py --full-pipeline --legislators 丁學忠 傅崐萁

# 設定批次大小
python main_new.py --analyze-only --batch-size 300
```

### **查看幫助**
```bash
python main_new.py
```

## 📊 數據格式

### **輸入數據**
- 各平台爬取的原始數據
- 支援JSON格式
- 自動處理不同平台的數據結構

### **中間數據**
- `gemini_format.json` - Gemini分析格式
- 標準化的用戶留言數據
- 包含標題、內容、日期、平台等信息

### **輸出數據**
- `使用者分析.json` - 最終分析結果
- `statistics.json` - 統計報告
- `enhanced_analysis.json` - 增強分析結果
- `all_data.json` - 合併後的完整數據

## 🔧 配置要求

### **Python版本**
- Python 3.7+

### **必要套件**
```bash
pip install selenium
pip install webdriver-manager
pip install google-generativeai
pip install requests
pip install beautifulsoup4
```

### **API配置**
- 在 `api.json` 中配置Google Gemini API keys
- 支援多個API keys輪流使用

## 📁 目錄結構

```
backend/
├── yt_crawler.py          # YouTube爬蟲
├── ptt_crawler.py         # PTT爬蟲
├── thread_crawler.py      # Threads爬蟲
├── fb_crawler.py          # Facebook爬蟲
├── data_integrator.py     # 數據整合
├── gemini_analyzer.py     # Gemini分析
├── final_processor.py     # 最終處理
├── main_new.py            # 主控制
├── api.json               # API配置
├── requirements.txt       # 依賴套件
├── README_NEW_SYSTEM.md  # 本文件
├── crawler/               # 爬蟲數據目錄
│   ├── data/             # 原始爬取數據
│   ├── processed/        # 處理後數據
│   │   ├── user_data/    # 用戶數據
│   │   ├── final_data/   # 最終結果
│   │   └── alldata/      # 合併數據
│   └── href/             # URL收集
└── temp/                  # 臨時文件
```

## ⚡ 性能優化

### **並行處理**
- 批次間並行（使用不同API keys）
- 批次內並行（最多8個並行）
- 真正的並行處理，非順序處理

### **批次處理**
- 預設批次大小：500用戶
- 可自定義批次大小
- 自動分批和負載均衡

### **錯誤處理**
- 自動重試機制
- 錯誤日誌記錄
- 失敗用戶標記

## 🐛 故障排除

### **常見問題**
1. **模組導入失敗**：檢查檔案路徑和依賴套件
2. **API key錯誤**：檢查 `api.json` 配置
3. **爬蟲失敗**：檢查網路連接和目標網站狀態
4. **分析失敗**：檢查Gemini API配額和網路

### **日誌文件**
- `main.log` - 主程序日誌
- 各模組的詳細日誌
- 錯誤追蹤和調試信息

## 🔄 升級說明

### **從舊系統升級**
1. 備份現有數據
2. 安裝新的依賴套件
3. 測試新系統功能
4. 遷移現有數據（如需要）

### **功能對比**
- ✅ 保留所有原有功能
- ✅ 更清晰的模組化架構
- ✅ 更好的錯誤處理
- ✅ 真正的並行處理
- ✅ 更簡潔的檔案結構

## 📞 支援

如有問題或建議，請檢查：
1. 日誌文件中的錯誤信息
2. 依賴套件版本
3. API配置和配額
4. 網路連接狀態

---

**版本**: 2.0 (重新整理版)  
**更新日期**: 2025年1月  
**維護者**: 社會媒體分析系統團隊 