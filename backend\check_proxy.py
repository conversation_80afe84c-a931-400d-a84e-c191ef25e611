#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单代理检查脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_proxy():
    """检查代理状态"""
    print("🔍 检查代理状态...")
    
    try:
        # 导入代理管理器
        from proxy_manager import proxy_manager
        
        # 获取代理统计
        stats = proxy_manager.get_proxy_stats()
        print(f"📊 代理池状态:")
        print(f"   可用代理: {stats['working']}")
        print(f"   失败代理: {stats['failed']}")
        print(f"   目标数量: {stats['target']}")
        print(f"   最小要求: {stats['min_required']}")
        
        # 检查是否健康
        is_healthy = stats['working'] >= stats['min_required']
        print(f"   健康状态: {'✅ 健康' if is_healthy else '❌ 不健康'}")
        
        # 尝试获取代理
        proxy = proxy_manager.get_random_proxy()
        if proxy:
            print(f"🔄 获取到代理: {proxy}")
        else:
            print("❌ 无法获取代理")
        
        return True
        
    except Exception as e:
        print(f"❌ 代理检查失败: {e}")
        return False

if __name__ == "__main__":
    check_proxy() 