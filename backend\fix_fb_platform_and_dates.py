#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正所有立委的平台名稱為"fb"並根據fb_data更新日期
"""

import json
import os
import re
from datetime import datetime

def load_json_file(file_path, encoding='utf-8'):
    """載入JSON檔案"""
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return json.load(f)
    except Exception as e:
        print(f"無法讀取檔案 {file_path}: {e}")
        return None

def save_json_file(data, file_path):
    """儲存JSON檔案"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"無法儲存檔案 {file_path}: {e}")
        return False

def extract_platform_info(content):
    """從整合留言內容中提取平台資訊"""
    platforms = []
    
    if "平台：ptt" in content:
        platforms.append("ptt")
    if "平台：youtube" in content:
        platforms.append("youtube")
    if "平台：facebook" in content or "粉絲專頁" in content:
        platforms.append("fb")  # 統一為fb
    
    return platforms

def extract_date_from_content(content):
    """從內容中提取日期"""
    date_patterns = [
        r'(\d{4}-\d{2}-\d{2})',
        r'(\d{4}/\d{2}/\d{2})',
        r'(\d{2}/\d{2}/\d{4})'
    ]
    
    for pattern in date_patterns:
        match = re.search(pattern, content)
        if match:
            date_str = match.group(1)
            if '/' in date_str:
                parts = date_str.split('/')
                if len(parts[0]) == 4:
                    return f"{parts[0]}-{parts[1]}-{parts[2]}"
                else:
                    return f"{parts[2]}-{parts[0]}-{parts[1]}"
            return date_str
    
    return datetime.now().strftime('%Y-%m-%d')

def fix_platform_names(legislator_name):
    """修正平台名稱為fb"""
    final_data_file = f"crawler/processed/final_data/{legislator_name}_使用者分析.json"
    
    if not os.path.exists(final_data_file):
        print(f"❌ {legislator_name}: final_data檔案不存在")
        return False
    
    data = load_json_file(final_data_file)
    if not data:
        return False
    
    fixed_count = 0
    for item in data:
        if not isinstance(item, dict):
            continue
        
        # 修正平台名稱
        if '平台' in item:
            platforms = item['平台'].split(',')
            # 將facebook改為fb
            platforms = ['fb' if p.strip() == 'facebook' else p.strip() for p in platforms]
            item['平台'] = ','.join(platforms)
            fixed_count += 1
        else:
            # 如果沒有平台欄位，根據內容添加
            content = item.get('整合留言內容', '')
            platforms = extract_platform_info(content)
            if platforms:
                item['平台'] = ','.join(platforms)
            else:
                item['平台'] = 'fb'  # 預設為fb
            fixed_count += 1
    
    if save_json_file(data, final_data_file):
        print(f"✅ {legislator_name}: 修正了 {fixed_count} 筆資料的平台名稱")
        return True
    else:
        print(f"❌ {legislator_name}: 儲存失敗")
        return False

def update_dates_from_fb_data(legislator_name):
    """根據fb_data更新日期"""
    final_data_file = f"crawler/processed/final_data/{legislator_name}_使用者分析.json"
    fb_data_file = f"crawler/fb_data/{legislator_name}_使用者分析.json"
    
    if not os.path.exists(final_data_file):
        print(f"❌ {legislator_name}: final_data檔案不存在")
        return False
    
    if not os.path.exists(fb_data_file):
        print(f"❌ {legislator_name}: fb_data檔案不存在")
        return False
    
    # 載入資料
    final_data = load_json_file(final_data_file)
    fb_data = load_json_file(fb_data_file)
    
    if not final_data or not fb_data:
        return False
    
    # 建立fb_data用戶名稱索引
    fb_users = {item['使用者']: item for item in fb_data if isinstance(item, dict)}
    
    updated_count = 0
    for item in final_data:
        if not isinstance(item, dict):
            continue
        
        user_name = item.get('使用者', '')
        if user_name in fb_users:
            fb_item = fb_users[user_name]
            
            # 如果final_data的日期為空或不存在，使用fb_data的日期
            if not item.get('日期') or item['日期'] == "":
                if fb_item.get('日期'):
                    item['日期'] = fb_item['日期']
                    updated_count += 1
            # 如果final_data的日期存在但為空字串，也更新
            elif item.get('日期') == "":
                if fb_item.get('日期'):
                    item['日期'] = fb_item['日期']
                    updated_count += 1
    
    if save_json_file(final_data, final_data_file):
        print(f"✅ {legislator_name}: 更新了 {updated_count} 筆資料的日期")
        return True
    else:
        print(f"❌ {legislator_name}: 儲存失敗")
        return False

def merge_fb_data_to_final_data(legislator_name):
    """合併fb_data到final_data"""
    final_data_file = f"crawler/processed/final_data/{legislator_name}_使用者分析.json"
    fb_data_file = f"crawler/fb_data/{legislator_name}_使用者分析.json"
    
    # 檢查檔案是否存在
    if not os.path.exists(final_data_file):
        print(f"❌ {legislator_name}: final_data檔案不存在")
        return False
    
    if not os.path.exists(fb_data_file):
        print(f"❌ {legislator_name}: fb_data檔案不存在")
        return False
    
    # 載入資料
    final_data = load_json_file(final_data_file)
    fb_data = load_json_file(fb_data_file)
    
    if not final_data or not fb_data:
        return False
    
    # 建立final_data用戶名稱索引
    final_users = {item['使用者']: item for item in final_data if isinstance(item, dict)}
    
    # 處理fb_data
    merged_count = 0
    new_users = 0
    
    for fb_item in fb_data:
        if not isinstance(fb_item, dict):
            continue
        
        fb_user = fb_item.get('使用者', '')
        
        if fb_user in final_users:
            # 更新現有用戶的資料
            final_item = final_users[fb_user]
            
            # 更新平台資訊
            if '平台' in final_item:
                platforms = final_item['平台'].split(',')
                if 'fb' not in platforms:
                    platforms.append('fb')
                final_item['平台'] = ','.join(platforms)
            else:
                final_item['平台'] = 'fb'
            
            # 更新日期（如果fb_data的日期更完整）
            if fb_item.get('日期') and (not final_item.get('日期') or final_item['日期'] == ''):
                final_item['日期'] = fb_item['日期']
            
            merged_count += 1
        else:
            # 添加新用戶
            final_data.append(fb_item)
            new_users += 1
    
    if save_json_file(final_data, final_data_file):
        print(f"✅ {legislator_name}: 更新了 {merged_count} 個用戶，新增了 {new_users} 個用戶")
        return True
    else:
        print(f"❌ {legislator_name}: 儲存失敗")
        return False

def create_final_data_from_fb(legislator_name):
    """從fb_data創建final_data"""
    fb_data_file = f"crawler/fb_data/{legislator_name}_使用者分析.json"
    final_data_file = f"crawler/processed/final_data/{legislator_name}_使用者分析.json"
    
    if not os.path.exists(fb_data_file):
        print(f"❌ {legislator_name}: fb_data檔案不存在")
        return False
    
    fb_data = load_json_file(fb_data_file)
    if not fb_data:
        return False
    
    # 直接複製fb_data到final_data
    if save_json_file(fb_data, final_data_file):
        print(f"✅ {legislator_name}: 從fb_data創建了final_data ({len(fb_data)} 筆)")
        return True
    else:
        print(f"❌ {legislator_name}: 創建final_data失敗")
        return False

def process_all_legislators():
    """處理所有立委"""
    print("=== 修正所有立委的fb平台名稱和日期 ===")
    
    final_dir = 'crawler/processed/final_data'
    fb_dir = 'crawler/fb_data'
    
    if not os.path.exists(fb_dir):
        print("❌ fb_data目錄不存在")
        return
    
    if not os.path.exists(final_dir):
        print("❌ final_data目錄不存在")
        return
    
    final_files = os.listdir(final_dir)
    fb_files = os.listdir(fb_dir)
    
    # 找出需要處理的立委
    fb_legislators = []
    for fb_file in fb_files:
        if fb_file.endswith('_使用者分析.json'):
            name = fb_file.replace('_使用者分析.json', '')
            fb_legislators.append(name)
    
    print(f"📊 找到 {len(fb_legislators)} 位有fb_data的立委")
    
    success_count = 0
    
    for legislator in fb_legislators:
        print(f"\n🔄 處理 {legislator}...")
        
        final_file = f'{legislator}_使用者分析.json'
        
        if final_file in final_files:
            # 有final_data，需要修正和合併
            print(f"📝 {legislator}: 修正平台名稱和合併fb_data")
            
            # 1. 修正平台名稱為fb
            fix_platform_names(legislator)
            
            # 2. 根據fb_data更新日期
            update_dates_from_fb_data(legislator)
            
            # 3. 合併fb_data
            if merge_fb_data_to_final_data(legislator):
                success_count += 1
        else:
            # 沒有final_data，直接創建
            print(f"📝 {legislator}: 從fb_data創建final_data")
            if create_final_data_from_fb(legislator):
                success_count += 1
    
    print(f"\n✅ 完成！成功處理 {success_count}/{len(fb_legislators)} 位立委")

def main():
    """主函數"""
    process_all_legislators()

if __name__ == "__main__":
    main() 