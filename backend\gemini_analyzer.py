#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gemini 情感分析模組
使用 Google Gemini API 進行用戶留言的情感分析
"""

import json
import os
import re
import time
import logging
from concurrent.futures import ThreadPoolExecutor
from itertools import cycle
import google.generativeai as genai
from typing import Dict, List, Any

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class GeminiAnalyzer:
    """Gemini情感分析器"""
    
    def __init__(self, base_dir="backend"):
        self.base_dir = base_dir
        self.api_keys = self.load_api_keys()
        self.temp_dir = os.path.join(base_dir, "temp")
        self.final_data_dir = os.path.join(base_dir, "crawler", "processed", "final_data")
        
        # 確保目錄存在
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(self.final_data_dir, exist_ok=True)
        
        logger.info(f"🧠 Gemini分析器初始化完成")
        logger.info(f"  - 載入 {len(self.api_keys)} 個API keys")
        logger.info(f"  - 臨時目錄: {self.temp_dir}")
        logger.info(f"  - 最終數據目錄: {self.final_data_dir}")
    
    def load_api_keys(self, path=None) -> List[str]:
        """加載API keys，支持多個路徑"""
        if path is None:
            # 嘗試多個可能的路徑
            possible_paths = [
                'api.json',  # 當前目錄
                '../api.json',  # 上一級目錄
                '../../api.json',  # 上兩級目錄
                os.path.join(os.path.dirname(__file__), '../api.json'),  # 相對於當前文件的上級目錄
                os.path.join(os.path.dirname(os.path.dirname(__file__)), 'api.json'),  # backend/api.json
            ]
            
            for test_path in possible_paths:
                if os.path.exists(test_path):
                    path = test_path
                    break
            
            if path is None:
                logger.error(f"❌ 無法找到 api.json 文件")
                return []
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                api_keys = data.get('api_keys', [])
                logger.info(f"✅ 成功加載 {len(api_keys)} 個API keys from {os.path.abspath(path)}")
                return api_keys
        except Exception as e:
            logger.error(f"❌ 加載API keys失敗: {e}")
            return []
    
    def build_prompt(self, combined_content: str, user_name: str, person_name: str, 
                     positive_party: str, negative_party: str) -> str:
        """構建Gemini分析提示詞"""
        return f"""
你是一位專業的語意理解與輿情分析員，任務是根據政治留言的語意、情緒與立場，判斷其對於「立法委員{person_name}罷免案的情緒與立場判斷」，並回傳格式正確的 JSON 結果。
支持罷免{person_name}是負面(NEGATIVE)
反對罷免{person_name}是正面(POSITIVE)

【分析任務】
請根據下列使用者的所有留言內容，判斷：
1. 該使用者的每一則留言，判斷整體立場（Label）：
   - 是否支持罷免{person_name}（{positive_party}立委）NEGATIVE？
   - 或是反對罷免（即支持{person_name}或批評{negative_party}）POSITIVE？

2. 該使用者留言情緒（Emotion）：從以下八種中選擇最接近的情緒以下是判斷時些微加分原則:假如用戶偏向支持罷免則情緒請往負面情緒為優先反之用戶反對罷免情緒則是正面情緒為優先。
3. 最後說明評判的原因

【立場分類規則】
- NEGATIVE：留言表達支持罷免{person_name}，或批評{positive_party}、{person_name}本人，支持{negative_party}或其立場。
- POSITIVE：留言反對罷免{person_name}，或批評{negative_party}、支持{positive_party}、或{person_name}本人。
- 若留言內容模糊或無法判斷，請以語氣傾向與上下文意圖推論最可能立場。
- 反對或批評{negative_party}為POSITIVE
- 國民黨是藍營，民進黨是綠營，依據立委所屬政黨判斷是否支持罷免
- 如果政黨為國民黨那批評民進黨、賴清德總統、相關民進黨議員為POSITIVE，反之亦然
- 如果政黨為民進黨那批評國民黨、陳水扁、馬英九、相關民進黨議員為POSITIVE，反之亦然

【強化判讀重點】
- 若出現嘲諷、反話、雙關語、鄉民語氣，需判斷是否為反語，並根據語境還原真實立場。
- 留言若批評對象是"{negative_party}"、"總統"、"綠營"傾向 POSITIVE，批評"{positive_party}"、"{person_name}"、"藍營"傾向 NEGATIVE。
- 若內容太模糊，可根據語氣合理推測立場，但不強行猜測。
- 強調{positive_party}與共產黨合作的為負面
- 如果有表達對政治人物的反感，則根據反感的陣營，判別是否支持罷免
- 忽略新聞網站來源文字

【情緒分類規則】
請從以下八種英文小寫情緒中選擇最符合者：
- anger: 氣、火大、無恥、去死、惱怒、抓狂、氣死人
- disgust: 噁、垃圾、爛、快吐、髒、惡心、臭
- fear: 怕、恐、好可怕、擔憂、不安、害怕
- surprise: 竟然、傻眼、沒想到、驚呆、嚇到、不可思議
- joy: 太好了、爽翻、開心、歡呼、開心到不行
- trust: 支持、相信、挺你、信任、為你加油
- anticipation: 期待、快點、拭目以待、等著瞧、有感覺
- sadness: 可憐、難過、失望、痛心、心碎、悲傷

請務必以以下 JSON Schema 格式回傳：
{{
  "Label": "POSITIVE" | "NEGATIVE",
  "Emotion": "joy" | "trust" | "anticipation" | "sadness" | "surprise" | "disgust" | "fear" | "anger"
}}

JSON Response:
整合留言內容：{combined_content}
說明評判的原因:
"""
    
    def analyze_user_comments(self, model, user_name: str, combined_content: str, 
                             person_name: str, positive_party: str, negative_party: str,
                             output_path: str, log_path: str, max_retries: int = 6) -> Dict[str, Any]:
        """分析單一使用者的留言內容"""
        prompt = self.build_prompt(combined_content, user_name, person_name, positive_party, negative_party)
        
        safety_settings = [
            {
                "category": "HARM_CATEGORY_HATE_SPEECH",
                "threshold": "BLOCK_NONE"
            },
            {
                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "threshold": "BLOCK_NONE"
            },
            {
                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                "threshold": "BLOCK_NONE"
            },
            {
                "category": "HARM_CATEGORY_HARASSMENT",
                "threshold": "BLOCK_NONE"
            }
        ]

        # 初始化平台变量
        platform = 'unknown'
        
        # 從留言內容中提取平台信息（格式：平台：xxx）
        platform_match = re.search(r'平台：([^\n]+)', combined_content)
        if platform_match:
            platform = platform_match.group(1).strip()
            # 標準化平台名稱
            if platform.lower() in ['youtube', 'yt']:
                platform = 'yt'
            elif platform.lower() in ['ptt']:
                platform = 'ptt'
            elif platform.lower() in ['threads', 'thread']:
                platform = 'threads'
            elif platform.lower() in ['facebook', 'fb']:
                platform = 'fb'

        for attempt in range(max_retries):
            try:
                response = model.generate_content(
                    prompt,
                    safety_settings=safety_settings
                )
                
                # 檢查回應是否為空
                if response is None:
                    raise ValueError("Response is None")
                
                if not hasattr(response, 'text') or not response.text:
                    raise ValueError("Response has no text content")
                    
                text = response.text.strip()
                if not text:
                    raise ValueError("Empty response text")
                
                # 嚴格檢查Label/Emotion格式
                label = re.search(r'"Label"\s*:\s*"(POSITIVE|NEGATIVE)"', text)
                emotion = re.search(r'"Emotion"\s*:\s*"(joy|trust|anticipation|sadness|surprise|disgust|fear|anger)"', text)
                
                result = {
                    "使用者": user_name,
                    "情感標籤": label.group(1) if label else "Unknown",
                    "情緒": emotion.group(1) if emotion else "Unknown",
                    "整合留言內容": combined_content,
                    "平台": platform
                }
                
                # 保存結果到JSON檔案
                if os.path.exists(output_path):
                    with open(output_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                else:
                    data = []
                
                data.append(result)
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                # 保存詳細記錄到TXT檔案
                with open(log_path, 'a', encoding='utf-8') as f:
                    f.write(f"=== {time.strftime('%Y-%m-%d %H:%M:%S')} ===\n")
                    f.write(f"使用者: {user_name}\n平台: {platform}\n整合留言: {combined_content}\nResponse: {text}\n\n")
                
                return result
                
            except Exception as e:
                logger.warning(f"Retry {attempt+1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
        
        # 標記失敗用戶
        fail_result = {
            "使用者": user_name,
            "情感標籤": "Failed",
            "情緒": "Failed",
            "整合留言內容": combined_content,
            "平台": platform
        }
        
        if os.path.exists(output_path):
            with open(output_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        else:
            data = []
        
        data.append(fail_result)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        return fail_result
    
    def run_batch(self, batch_index: int, batch_data: Dict[str, Any], api_key: str, 
                  person_name: str, positive_party: str, negative_party: str) -> None:
        """處理一個批次的用戶數據"""
        safe_name = person_name.replace(" ", "_")
        temp_dir = os.path.join(self.temp_dir, safe_name)
        os.makedirs(temp_dir, exist_ok=True)

        output_path = os.path.join(temp_dir, f'batch_{batch_index}.json')
        log_path = os.path.join(temp_dir, f'log_{batch_index}.txt')

        # 配置Gemini API
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-1.5-flash')
        logger.info(f"[{person_name}][Batch {batch_index}] 開始處理，共 {len(batch_data)} 位使用者")

        # 🚀 並行處理批次內的所有用戶
        with ThreadPoolExecutor(max_workers=min(8, len(batch_data))) as executor:
            futures = []
            for user_name, user_info in batch_data.items():
                # 準備用戶數據
                comments = user_info.get('comments', [])
                combined_content = ""
                for comment in comments:
                    title = comment.get('標題', '')
                    content = comment.get('留言內容', '')
                    if title:
                        combined_content += f"標題: {title}\n"
                    if content:
                        combined_content += f"內容: {content}\n"
                    combined_content += "---\n"
                
                if combined_content.strip():
                    future = executor.submit(
                        self.analyze_user_comments,
                        model, user_name, combined_content, person_name, 
                        positive_party, negative_party, output_path, log_path
                    )
                    futures.append((user_name, future))
            
            # 等待所有用戶處理完成
            for user_name, future in futures:
                try:
                    result = future.result()
                    logger.info(f"[{person_name}][Batch {batch_index}] ✅ {user_name} 處理完成")
                except Exception as e:
                    logger.error(f"[{person_name}][Batch {batch_index}] ❌ {user_name} 處理失敗: {e}")
    
    def split_user_batches(self, user_data: Dict[str, Any], size: int) -> List[Dict[str, Any]]:
        """將使用者資料分批"""
        users = list(user_data.items())
        batches = []
        for i in range(0, len(users), size):
            batch_dict = dict(users[i:i + size])
            batches.append(batch_dict)
        return batches
    
    def analyze_legislator_emotions(self, legislator_name: str, user_data: Dict[str, Any], 
                                  positive_party: str = "國民黨", negative_party: str = "民進黨",
                                  batch_size: int = 500) -> bool:
        """
        分析特定立委的情感資料
        
        Args:
            legislator_name: 立委姓名
            user_data: 用戶數據
            positive_party: 正面政黨
            negative_party: 負面政黨
            batch_size: 批次大小
            
        Returns:
            bool: 分析是否成功
        """
        try:
            logger.info(f"🧠 開始分析 {legislator_name} 的情感資料...")
            logger.info(f"📊 總用戶數: {len(user_data)}")
            
            if not user_data:
                logger.warning(f"⚠️ {legislator_name} 沒有用戶數據需要分析")
                return False
            
            # 將用戶分批
            batches = self.split_user_batches(user_data, batch_size)
            logger.info(f"📦 分為 {len(batches)} 個批次")
            
            # 並行處理所有批次
            api_key_cycle = cycle(self.api_keys)
            
            with ThreadPoolExecutor(max_workers=min(len(self.api_keys), len(batches))) as executor:
                futures = []
                for batch_index, batch_data in enumerate(batches):
                    api_key = next(api_key_cycle)
                    future = executor.submit(
                        self.run_batch,
                        batch_index,
                        batch_data,
                        api_key,
                        legislator_name,
                        positive_party,
                        negative_party
                    )
                    futures.append(future)
                
                # 等待所有批次完成
                for future in futures:
                    try:
                        future.result()
                    except Exception as e:
                        logger.error(f"❌ 批次處理失敗: {e}")
            
            logger.info(f"✅ {legislator_name} 情感分析完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ {legislator_name} 情感分析失敗: {e}")
            return False
    
    def merge_temp_results(self, person_name: str) -> bool:
        """合併temp目錄中的分析結果到final_data"""
        try:
            safe_name = person_name.replace(" ", "_")
            temp_dir = os.path.join(self.temp_dir, safe_name)
            output_file = os.path.join(self.final_data_dir, f'{person_name}_使用者分析.json')
            log_output = os.path.join(self.final_data_dir, f'{person_name}_使用者分析_log.txt')
            
            # 檢查temp目錄是否存在
            if not os.path.exists(temp_dir):
                logger.warning(f"⚠️ temp目錄不存在: {temp_dir}")
                return False
            
            all_results = []
            with open(log_output, 'w', encoding='utf-8') as log_out:
                for fname in sorted(os.listdir(temp_dir)):
                    path = os.path.join(temp_dir, fname)
                    if fname.endswith(".json"):
                        try:
                            with open(path, 'r', encoding='utf-8') as f:
                                batch_data = json.load(f)
                                all_results.extend(batch_data)
                                logger.info(f"📄 讀取 {fname}: {len(batch_data)} 筆資料")
                        except Exception as e:
                            logger.error(f"❌ 讀取 {fname} 失敗: {e}")
                    elif fname.endswith(".txt"):
                        try:
                            with open(path, 'r', encoding='utf-8') as log_in:
                                log_out.write(f"\n=== {fname} ===\n")
                                log_out.write(log_in.read())
                        except Exception as e:
                            logger.error(f"❌ 讀取日誌 {fname} 失敗: {e}")

            # 保存合併結果
            if all_results:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(all_results, f, ensure_ascii=False, indent=2)
                logger.info(f"✅ 合併完成：{output_file}（共 {len(all_results)} 位使用者）")
                
                # 统计平台分布
                platform_stats = {}
                for result in all_results:
                    platform = result.get('平台', 'unknown')
                    platform_stats[platform] = platform_stats.get(platform, 0) + 1
                
                logger.info("📊 平台分布统计：")
                for platform, count in platform_stats.items():
                    logger.info(f"  {platform}: {count} 位使用者")
                
                return True
            else:
                logger.warning(f"⚠️ 沒有找到任何分析結果: {temp_dir}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 合併結果失敗: {e}")
            return False

def analyze_legislators_emotions(legislators: List[str] = None, batch_size: int = 500, 
                                quiet: bool = False, incremental: bool = True, 
                                platform_filter: str = None, base_dir: str = "backend") -> bool:
    """
    分析多位立委的情感資料
    
    Args:
        legislators: 立委列表，預設為 None（處理所有立委）
        batch_size: 批次大小
        quiet: 是否安靜模式
        incremental: 是否增量分析
        platform_filter: 平台過濾器
        base_dir: 基礎目錄
        
    Returns:
        bool: 分析是否成功
    """
    analyzer = GeminiAnalyzer(base_dir)
    
    if not legislators:
        # 如果沒有指定立委，從 user_data 目錄讀取所有立委
        user_data_dir = os.path.join(base_dir, "crawler", "processed", "user_data")
        if os.path.exists(user_data_dir):
            user_files = [f for f in os.listdir(user_data_dir) if f.endswith('_gemini_format.json')]
            legislators = [f.replace('_gemini_format.json', '') for f in user_files]
        else:
            logger.error(f"ERROR: {user_data_dir} 目錄不存在")
            return False

    if not quiet:
        mode_text = "增量分析" if incremental else "完整分析"
        platform_text = f"（僅{platform_filter}平台）" if platform_filter else "（全平台）"
        logger.info(f"🤖 開始{mode_text} {len(legislators)} 位立委的情感資料{platform_text}...")

    success_count = 0
    for legislator in legislators:
        try:
            # 載入用戶數據
            user_data_file = os.path.join(base_dir, "crawler", "processed", "user_data", f"{legislator}_gemini_format.json")
            if not os.path.exists(user_data_file):
                logger.warning(f"⚠️ {user_data_file} 不存在，跳過")
                continue
            
            with open(user_data_file, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
            
            if not user_data:
                logger.warning(f"⚠️ {legislator} 沒有用戶數據，跳過")
                continue
            
            # 執行情感分析
            if analyzer.analyze_legislator_emotions(legislator, user_data, batch_size=batch_size):
                # 合併結果
                if analyzer.merge_temp_results(legislator):
                    success_count += 1
                    logger.info(f"✅ {legislator} 分析完成")
                else:
                    logger.error(f"❌ {legislator} 結果合併失敗")
            else:
                logger.error(f"❌ {legislator} 分析失敗")
                
        except Exception as e:
            logger.error(f"❌ 處理 {legislator} 時出錯: {e}")
    
    if not quiet:
        logger.info(f"🎉 情感分析完成！成功處理 {success_count}/{len(legislators)} 位立委")
    
    return success_count > 0

if __name__ == "__main__":
    # 測試Gemini分析器
    print("🧪 測試Gemini分析器...")
    
    try:
        # 測試API keys載入
        analyzer = GeminiAnalyzer()
        if analyzer.api_keys:
            print(f"✅ API keys載入成功: {len(analyzer.api_keys)} 個")
            
            # 測試提示詞構建
            prompt = analyzer.build_prompt("測試留言", "測試用戶", "測試立委", "國民黨", "民進黨")
            print(f"✅ 提示詞構建成功，長度: {len(prompt)}")
            
        else:
            print("❌ API keys載入失敗")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}") 