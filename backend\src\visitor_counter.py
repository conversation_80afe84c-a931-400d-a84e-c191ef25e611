"""
網站訪問計數器 API
處理網站訪問統計，追蹤使用者數量
"""

from datetime import datetime
from pymongo import MongoClient, DESCENDING
import logging
from config import get_mongo_config
from flask import jsonify, request, Blueprint
import os

# 創建藍圖
visitor_counter_bp = Blueprint('visitor_counter', __name__)

# 初始化 MongoDB 連接
mongo_config = get_mongo_config()
client = MongoClient(mongo_config['MONGODB_URI'], **mongo_config.get('MONGODB_OPTIONS', {}))
db = client[mongo_config['MONGODB_DBNAME']]
visits_col = db['site_visits']

# 確保索引
visits_col.create_index([('date', DESCENDING)])
visits_col.create_index([('page', 1)])

# 設置日誌
logger = logging.getLogger('visitor_counter')

def ensure_visitor_counter_initialized():
    """
    確保訪問計數器已初始化，如果不存在則創建必要的記錄
    每次調用API前自動檢查並初始化site_visits集合和文檔
    
    返回:
        bool: 初始化操作是否成功
    """
    try:
        # 檢查是否已有總訪問記錄
        total_doc = visits_col.find_one({'page': 'total'})
        if not total_doc:
            # 如果沒有，創建一個初始值為0的總訪問記錄
            visits_col.insert_one({
                'page': 'total',
                'count': 0,  # 初始值為0
                'created_at': datetime.now()
            })
            logger.info('已初始化訪問計數器 - 總訪問記錄')
            
        # 檢查今日訪客記錄
        today = datetime.now().strftime('%Y-%m-%d')
        today_doc = visits_col.find_one({'date': today, 'page': 'daily_visitors'})
        if not today_doc:
            # 創建今日訪客記錄
            visits_col.insert_one({
                'date': today,
                'page': 'daily_visitors',
                'count': 0,
                'created_at': datetime.now()
            })
            logger.info(f'已初始化今日({today})訪客記錄')
            
        return True

    except Exception as e:
        logger.error(f'初始化訪問計數器時出錯: {e}')
        return False

def _record_visit(page=None, user_agent=None, ip=None):
    """
    記錄一次網站訪問
    - 總訪問：每次進入頁面/刷新網頁都 +1 (不過濾相同用戶)
    - 今日訪客：每天歸0重新計算，每次訪問都 +1 (不過濾相同用戶)
    
    參數:
        page: 訪問的頁面路徑 (僅用於日誌)
        user_agent: 瀏覽器 User-Agent
        ip: 用戶 IP 地址 (已匿名化)
    
    返回:
        dict: 包含當日訪客數和總訪問數
    """
    # 確保訪問計數器已初始化
    ensure_visitor_counter_initialized()
    
    try:
        # 當前日期 (YYYY-MM-DD 格式)
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 1. 總訪問數：每次訪問都 +1 (不管是否同一用戶)
        visits_col.update_one(
            {'page': 'total'},
            {'$inc': {'count': 1}},
            upsert=True
        )
        
        # 2. 今日訪客數：每天歸0重新計算，每次訪問都 +1 (不過濾相同用戶)
        # 檢查今天是否已有訪客記錄
        today_doc = visits_col.find_one({'date': today, 'page': 'daily_visitors'})
        
        if not today_doc:
            # 今天第一個訪客
            visits_col.insert_one({
                'date': today,
                'page': 'daily_visitors',
                'count': 1,
                'created_at': datetime.now()
            })
            today_visitors = 1
        else:
            # 每次訪問都增加計數
            visits_col.update_one(
                {'date': today, 'page': 'daily_visitors'},
                {'$inc': {'count': 1}}
            )
            today_visitors = today_doc.get('count', 0) + 1
        
        # 獲取總訪問數
        total_stats = visits_col.find_one({'page': 'total'})
        total_visits = total_stats.get('count', 0) if total_stats else 0
        
        logger.info(f"記錄訪問: {ip} - {page} | 總訪問: {total_visits} | 今日訪客: {today_visitors}")
        
        return {
            'today_visitors': today_visitors,
            'total_visits': total_visits
        }
    except Exception as e:
        logger.error(f"記錄訪問數據時出錯: {e}")
        return {
            'today_visitors': 0,
            'total_visits': 0
        }

def _get_visit_stats():
    """
    獲取網站訪問統計
    
    返回:
        dict: 包含訪問統計數據
    """
    # 確保訪問計數器已初始化
    ensure_visitor_counter_initialized()
    
    try:
        # 獲取總訪問次數
        total_stats = visits_col.find_one({'page': 'total'})
        total_count = total_stats.get('count', 0) if total_stats else 0
        
        # 獲取當日訪客數
        today = datetime.now().strftime('%Y-%m-%d')
        today_doc = visits_col.find_one({'date': today, 'page': 'daily_visitors'})
        
        if not today_doc:
            # 今天還沒有訪客
            today_visitors = 0
        else:
            today_visitors = today_doc.get('count', 0)
        
        return {
            'total_visits': total_count,
            'today_visitors': today_visitors,
            'status': 'success',
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    except Exception as e:
        logger.error(f"獲取訪問統計時出錯: {e}")
        return {
            'total_visits': 0,
            'today_visitors': 0,
            'status': 'error',
            'error': str(e)
        }

# 路由處理函數 - 記錄訪問
@visitor_counter_bp.route('/record', methods=['POST'])
def record_visitor():
    """處理記錄訪問請求"""
    data = request.json or {}
    page = data.get('page', 'home')
    user_agent = request.headers.get('User-Agent')
    # 獲取IP但僅保留前兩段，保護隱私
    ip = request.remote_addr
    if ip and '.' in ip:
        parts = ip.split('.')
        if len(parts) >= 2:
            ip = f"{parts[0]}.{parts[1]}.*.*"
    
    # 調用內部記錄函數
    result = _record_visit(page, user_agent, ip)
    return jsonify(result)

# 路由處理函數 - 獲取訪問統計
@visitor_counter_bp.route('/stats', methods=['GET'])
def visitor_stats():
    """處理獲取訪問統計請求"""
    # 調用內部統計函數
    stats = _get_visit_stats()
    return jsonify(stats)

# 路由處理函數 - 初始化訪問計數
@visitor_counter_bp.route('/init', methods=['GET'])
def init_visitor_stats():
    """初始化訪問統計，設置基礎計數"""
    try:
        # 確保訪問統計初始化
        result = ensure_visitor_counter_initialized()
        if result:
            return jsonify({'status': 'success', 'message': '已初始化訪問計數', 'initialized': True})
        else:
            # 檢查是否已有總訪問記錄
            total_doc = visits_col.find_one({'page': 'total'})
            if not total_doc:
                # 如果沒有，創建一個初始值為1的總訪問記錄
                visits_col.insert_one({
                    'page': 'total',
                    'count': 1,  # 初始值
                    'created_at': datetime.now()
                })
                return jsonify({'status': 'success', 'message': '已初始化訪問計數', 'count': 1})
            else:
                return jsonify({'status': 'success', 'message': '訪問計數已存在', 'count': total_doc.get('count', 1)})
    except Exception as e:
        logger.error(f"初始化訪問計數時出錯: {e}")
        return jsonify({'status': 'error', 'message': f'初始化失敗: {str(e)}'})
