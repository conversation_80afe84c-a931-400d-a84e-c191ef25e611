#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能數據處理模組
處理MongoDB智能更新、need_reRun邏輯、情感標籤更新等
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import pymongo
from pymongo import MongoClient

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('smart_processor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SmartDataProcessor:
    """智能數據處理器"""
    
    def __init__(self, base_dir: str = "backend"):
        self.base_dir = base_dir
        self.mongo_client = None
        self.db = None
        self.crawler_data_collection = None
        
        # 初始化MongoDB連接
        self.init_mongodb()
    
    def init_mongodb(self):
        """初始化MongoDB連接"""
        try:
            # 從配置文件讀取MongoDB連接信息
            config_file = os.path.join(self.base_dir, "config.py")
            if os.path.exists(config_file):
                import importlib.util
                spec = importlib.util.spec_from_file_location("config", config_file)
                config = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(config)
                
                mongo_uri = getattr(config, 'MONGO_URI', 'mongodb://localhost:27017/')
                db_name = getattr(config, 'DB_NAME', 'social_media_analysis')
            else:
                mongo_uri = 'mongodb://localhost:27017/'
                db_name = 'social_media_analysis'
            
            self.mongo_client = MongoClient(mongo_uri)
            self.db = self.mongo_client[db_name]
            self.crawler_data_collection = self.db['crawler_data']
            
            logger.info(f"✅ MongoDB連接成功: {db_name}")
            
        except Exception as e:
            logger.error(f"❌ MongoDB連接失敗: {e}")
            self.mongo_client = None
    
    def update_mongodb_crawler_data_smart(self, legislators: List[str] = None) -> bool:
        """
        智能更新MongoDB crawler_data
        
        Args:
            legislators: 要更新的立委列表
            
        Returns:
            bool: 更新是否成功
        """
        if not self.mongo_client:
            logger.error("❌ MongoDB未連接，無法更新")
            return False
        
        if legislators is None:
            legislators = [leg['name'] for leg in self.load_legislators()]
        
        logger.info(f"🗄️ 開始智能更新MongoDB crawler_data...")
        logger.info(f"  - 立委數量: {len(legislators)}")
        
        success_count = 0
        
        for legislator in legislators:
            try:
                logger.info(f"🔄 處理 {legislator} 的數據...")
                
                # 讀取gemini格式數據
                gemini_file = f"{self.base_dir}/crawler/processed/user_data/{legislator}_gemini_format.json"
                if not os.path.exists(gemini_file):
                    logger.warning(f"  ⚠️ {legislator}: 無gemini格式數據文件")
                    continue
                
                with open(gemini_file, 'r', encoding='utf-8') as f:
                    gemini_data = json.load(f)
                
                # 處理每個用戶
                user_count = 0
                for user_name, user_data in gemini_data.get('users', {}).items():
                    try:
                        # 構建用戶數據
                        user_record = self.build_user_record(legislator, user_name, user_data)
                        
                        # 智能更新MongoDB
                        if self.smart_update_user_record(user_record):
                            user_count += 1
                        
                    except Exception as e:
                        logger.error(f"  ❌ 處理用戶 {user_name} 失敗: {e}")
                
                logger.info(f"  ✅ {legislator}: 處理完成，更新 {user_count} 位用戶")
                success_count += 1
                
            except Exception as e:
                logger.error(f"❌ {legislator} 處理失敗: {e}")
        
        logger.info(f"🎉 MongoDB crawler_data智能更新完成！成功處理 {success_count}/{len(legislators)} 位立委")
        return success_count > 0
    
    def build_user_record(self, legislator: str, user_name: str, user_data: Dict) -> Dict:
        """
        構建用戶記錄
        
        Args:
            legislator: 立委姓名
            user_name: 用戶名稱
            user_data: 用戶數據
            
        Returns:
            Dict: 用戶記錄
        """
        # 合併所有平台的留言內容
        all_comments = []
        platforms = []
        
        for comment in user_data.get('comments', []):
            platform = comment.get('platform', 'unknown')
            platforms.append(platform)
            
            comment_text = f"平台：{platform}\n"
            if comment.get('標題'):
                comment_text += f"來源：{comment['標題']}\n"
            if comment.get('留言內容'):
                comment_text += f"留言內容：{comment['留言內容']}\n"
            if comment.get('時間'):
                comment_text += f"時間：{comment['時間']}\n"
            
            all_comments.append(comment_text)
        
        # 構建記錄
        record = {
            "name": legislator,
            "user_name": user_name,
            "留言內容": "\n---\n".join(all_comments),
            "情感標籤": "",
            "情緒": "",
            "標題": "",
            "日期": datetime.now().strftime("%Y-%m-%d"),
            "source": "",
            "platform": ",".join(list(set(platforms))),
            "last_updated": datetime.utcnow(),
            "data_source": "final_data_analysis_optimized",
            "stance": "",
            "need_reRun": True  # 新數據需要重新運行Gemini分析
        }
        
        return record
    
    def smart_update_user_record(self, user_record: Dict) -> bool:
        """
        智能更新用戶記錄
        
        Args:
            user_record: 用戶記錄
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 查找現有記錄
            existing_record = self.crawler_data_collection.find_one({
                "name": user_record["name"],
                "user_name": user_record["user_name"],
                "platform": user_record["platform"]
            })
            
            if existing_record:
                # 更新現有記錄
                update_data = {
                    "$set": {
                        "留言內容": user_record["留言內容"],
                        "日期": user_record["日期"],
                        "last_updated": user_record["last_updated"],
                        "need_reRun": True  # 有新留言，需要重新分析
                    }
                }
                
                result = self.crawler_data_collection.update_one(
                    {"_id": existing_record["_id"]},
                    update_data
                )
                
                if result.modified_count > 0:
                    logger.debug(f"  ✅ 更新現有用戶記錄: {user_record['name']} - {user_record['user_name']}")
                    return True
                else:
                    logger.warning(f"  ⚠️ 更新現有用戶記錄失敗: {user_record['name']} - {user_record['user_name']}")
                    return False
            else:
                # 插入新記錄
                result = self.crawler_data_collection.insert_one(user_record)
                
                if result.inserted_id:
                    logger.debug(f"  ✅ 插入新用戶記錄: {user_record['name']} - {user_record['user_name']}")
                    return True
                else:
                    logger.warning(f"  ⚠️ 插入新用戶記錄失敗: {user_record['name']} - {user_record['user_name']}")
                    return False
                    
        except Exception as e:
            logger.error(f"  ❌ 智能更新用戶記錄失敗: {e}")
            return False
    
    def update_sentiment_labels_from_gemini(self, legislators: List[str] = None) -> bool:
        """
        從Gemini分析結果更新情感標籤
        
        Args:
            legislators: 要更新的立委列表
            
        Returns:
            bool: 更新是否成功
        """
        if not self.mongo_client:
            logger.error("❌ MongoDB未連接，無法更新")
            return False
        
        if legislators is None:
            legislators = [leg['name'] for leg in self.load_legislators()]
        
        logger.info(f"🏷️ 開始從Gemini分析結果更新情感標籤...")
        logger.info(f"  - 立委數量: {len(legislators)}")
        
        success_count = 0
        
        for legislator in legislators:
            try:
                logger.info(f"🔄 更新 {legislator} 的情感標籤...")
                
                # 讀取Gemini分析結果
                final_file = f"{self.base_dir}/crawler/processed/final_data/{legislator}_使用者分析.json"
                if not os.path.exists(final_file):
                    logger.warning(f"  ⚠️ {legislator}: 無最終分析結果文件")
                    continue
                
                with open(final_file, 'r', encoding='utf-8') as f:
                    final_data = json.load(f)
                
                # 更新MongoDB中的情感標籤
                updated_count = self.update_sentiment_labels_for_legislator(legislator, final_data)
                
                logger.info(f"  ✅ {legislator}: 更新完成，更新 {updated_count} 位用戶")
                success_count += 1
                
            except Exception as e:
                logger.error(f"❌ {legislator} 情感標籤更新失敗: {e}")
        
        logger.info(f"🎉 情感標籤更新完成！成功處理 {success_count}/{len(legislators)} 位立委")
        return success_count > 0
    
    def update_sentiment_labels_for_legislator(self, legislator: str, final_data: Dict) -> int:
        """
        更新指定立委的情感標籤
        
        Args:
            legislator: 立委姓名
            final_data: 最終分析數據
            
        Returns:
            int: 更新的用戶數量
        """
        updated_count = 0
        
        try:
            users_data = final_data.get('users', {})
            
            for user_name, user_info in users_data.items():
                emotion_analysis = user_info.get('emotion_analysis', {})
                
                if emotion_analysis:
                    # 提取情感標籤和情緒
                    sentiment_label = emotion_analysis.get('sentiment_label', '')
                    emotion = emotion_analysis.get('emotion', '')
                    
                    # 更新MongoDB
                    update_data = {
                        "$set": {
                            "情感標籤": sentiment_label,
                            "情緒": emotion,
                            "need_reRun": False,  # 分析完成，不需要重新運行
                            "last_updated": datetime.utcnow()
                        }
                    }
                    
                    result = self.crawler_data_collection.update_one(
                        {
                            "name": legislator,
                            "user_name": user_name
                        },
                        update_data
                    )
                    
                    if result.modified_count > 0:
                        updated_count += 1
                        logger.debug(f"  ✅ 更新情感標籤: {user_name} - {sentiment_label} - {emotion}")
            
        except Exception as e:
            logger.error(f"  ❌ 更新立委 {legislator} 情感標籤失敗: {e}")
        
        return updated_count
    
    def generate_legislators_statistics(self, legislators: List[str] = None) -> bool:
        """
        生成立委統計報告（本地和遠端）
        
        Args:
            legislators: 要統計的立委列表
            
        Returns:
            bool: 統計是否成功
        """
        if not self.mongo_client:
            logger.error("❌ MongoDB未連接，無法生成統計")
            return False
        
        if legislators is None:
            legislators = [leg['name'] for leg in self.load_legislators()]
        
        logger.info(f"📊 開始生成立委統計報告...")
        logger.info(f"  - 立委數量: {len(legislators)}")
        
        try:
            # 生成統計數據
            statistics = {}
            
            for legislator in legislators:
                # 統計該立委的用戶數據
                user_stats = self.crawler_data_collection.aggregate([
                    {"$match": {"name": legislator}},
                    {"$group": {
                        "_id": "$name",
                        "total_users": {"$sum": 1},
                        "need_rerun_users": {"$sum": {"$cond": ["$need_reRun", 1, 0]}},
                        "analyzed_users": {"$sum": {"$cond": ["$need_reRun", 0, 1]}},
                        "platforms": {"$addToSet": "$platform"}
                    }}
                ])
                
                for stat in user_stats:
                    statistics[legislator] = {
                        "total_users": stat["total_users"],
                        "need_rerun_users": stat["need_rerun_users"],
                        "analyzed_users": stat["analyzed_users"],
                        "platforms": stat["platforms"]
                    }
            
            # 保存本地統計報告
            stats_file = f"{self.base_dir}/legislators_statistics.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(statistics, f, ensure_ascii=False, indent=2, default=str)
            
            # 更新遠端統計
            self.update_remote_statistics(statistics)
            
            logger.info(f"🎉 統計報告生成完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 統計報告生成失敗: {e}")
            return False
    
    def update_remote_statistics(self, statistics: Dict):
        """
        更新遠端統計數據
        
        Args:
            statistics: 統計數據
        """
        try:
            # 更新或插入統計數據到MongoDB
            for legislator, stats in statistics.items():
                self.db.legislators_statistics.update_one(
                    {"name": legislator},
                    {"$set": {
                        **stats,
                        "last_updated": datetime.utcnow()
                    }},
                    upsert=True
                )
            
            logger.info(f"✅ 遠端統計更新完成")
            
        except Exception as e:
            logger.error(f"❌ 遠端統計更新失敗: {e}")
    
    def load_legislators(self) -> List[Dict]:
        """載入立委配置"""
        return [
            {"name": "丁學忠", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "傅崐萁", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "廖偉翔", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "楊瓊瓔", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "江啟臣", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "游顥", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "呂玉玲", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "廖先翔", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "張智倫", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "邱若華", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "魯明哲", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "萬美玲", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "羅明才", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "林思銘", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "林德福", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "鄭正鈐", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "賴士葆", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "涂權吉", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "徐欣瑩", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "李彥秀", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "林沛祥", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "洪孟楷", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "牛煦庭", "positive_party": "國民黨", "negative_party": "民進黨"},
            {"name": "王鴻薇", "positive_party": "國民黨", "negative_party": "民進黨"}
        ]
    
    def close(self):
        """關閉MongoDB連接"""
        if self.mongo_client:
            self.mongo_client.close()
            logger.info("🔌 MongoDB連接已關閉")

# 導出函數
def update_mongodb_crawler_data_smart(legislators: List[str] = None, base_dir: str = "backend") -> bool:
    """智能更新MongoDB crawler_data"""
    processor = SmartDataProcessor(base_dir)
    try:
        return processor.update_mongodb_crawler_data_smart(legislators)
    finally:
        processor.close()

def update_sentiment_labels_from_gemini(legislators: List[str] = None, base_dir: str = "backend") -> bool:
    """從Gemini分析結果更新情感標籤"""
    processor = SmartDataProcessor(base_dir)
    try:
        return processor.update_sentiment_labels_from_gemini(legislators)
    finally:
        processor.close()

def generate_legislators_statistics(legislators: List[str] = None, base_dir: str = "backend") -> bool:
    """生成立委統計報告"""
    processor = SmartDataProcessor(base_dir)
    try:
        return processor.generate_legislators_statistics(legislators)
    finally:
        processor.close() 