import os
from dotenv import load_dotenv
import logging

load_dotenv()

logger = logging.getLogger(__name__)

class Config:
    """基礎配置類"""
    SECRET_KEY = os.environ.get('SECRET_KEY')
    GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')
    NEWSAPI_KEY = os.environ.get('NEWSAPI_KEY')
    
    # MongoDB Atlas 連線優化參數
    MONGODB_MAX_POOL_SIZE = int(os.environ.get('MONGODB_MAX_POOL_SIZE', 10))
    MONGODB_MAX_IDLE_TIME_MS = int(os.environ.get('MONGODB_MAX_IDLE_TIME_MS', 30000))
    MONGODB_CONNECT_TIMEOUT_MS = int(os.environ.get('MONGODB_CONNECT_TIMEOUT_MS', 30000))
    MONGODB_SOCKET_TIMEOUT_MS = int(os.environ.get('MONGODB_SOCKET_TIMEOUT_MS', 45000))
    MONGODB_SERVER_SELECTION_TIMEOUT_MS = int(os.environ.get('MONGODB_SERVER_SELECTION_TIMEOUT_MS', 30000))
    MONGODB_WAIT_QUEUE_TIMEOUT_MS = int(os.environ.get('MONGODB_WAIT_QUEUE_TIMEOUT_MS', 30000))
    MONGODB_HEARTBEAT_FREQUENCY_MS = int(os.environ.get('MONGODB_HEARTBEAT_FREQUENCY_MS', 10000))
    MONGODB_RETRY_WRITES = os.environ.get('MONGODB_RETRY_WRITES', 'true').lower() == 'true'
    MONGODB_RETRY_READS = os.environ.get('MONGODB_RETRY_READS', 'true').lower() == 'true'
    MONGODB_W = int(os.environ.get('MONGODB_W', 1))
    MONGODB_JOURNAL = os.environ.get('MONGODB_JOURNAL', 'true').lower() == 'true'
    MONGODB_APP_NAME = os.environ.get('MONGODB_APP_NAME', 'legislator_recall_backend')

    # 立委選舉區選舉人總數字典 (第11屆)
    ELECTORAL_COUNT = {
    "丁學忠": {"district": "台北市第三選舉區", "electoral_count": 235845},
    "牛煦庭": {"district": "台北市第六選舉區", "electoral_count": 235790},
    "王鴻薇": {"district": "台北市第一選舉區", "electoral_count": 224816},
    "江啟臣": {"district": "台中市第八選舉區", "electoral_count": 334956},
    "呂玉玲": {"district": "雲林縣第二選舉區", "electoral_count": 330024},
    "李彥秀": {"district": "台北市第四選舉區", "electoral_count": 261330},
    "林沛祥": {"district": "基隆市選舉區", "electoral_count": 308092},
    "林思銘": {"district": "苗栗縣第一選舉區", "electoral_count": 251050},
    "林德福": {"district": "新北市第九選舉區", "electoral_count": 263332},
    "邱若華": {"district": "桃園市第六選舉區", "electoral_count": 254778},
    "邱鎮軍": {"district": "彰化縣第二選舉區", "electoral_count": 333779},
    "洪孟楷": {"district": "新北市第一選舉區", "electoral_count": 357901},
    "徐巧芯": {"district": "台北市第七選舉區", "electoral_count": 238822},
    "徐欣瑩": {"district": "新竹縣第一選舉區", "electoral_count": 367078},
    "馬文君": {"district": "南投縣第一選舉區", "electoral_count": 297894},
    "張其祿": {"district": "台南市第五選舉區", "electoral_count": 315522},
    "張嘉郡": {"district": "雲林縣第一選舉區", "electoral_count": 314455},
    "梁文傑": {"district": "台北市第二選舉區", "electoral_count": 233936},
    "莊瑞雄": {"district": "屏東縣第二選舉區", "electoral_count": 309438},
    "陳玉珍": {"district": "金門縣選舉區", "electoral_count": 134063},
    "陳建仁": {"district": "台北市第九選舉區", "electoral_count": 240533},
    "陳昭姿": {"district": "台北市第十選舉區", "electoral_count": 245423},
    "陳椒華": {"district": "台南市第六選舉區", "electoral_count": 318095},
    "陳雪生": {"district": "苗栗縣第二選舉區", "electoral_count": 324881},
    "陳學聖": {"district": "桃園市第五選舉區", "electoral_count": 302133},
    "曾銘宗": {"district": "新北市第六選舉區", "electoral_count": 290412},
    "游毓蘭": {"district": "台北市第八選舉區", "electoral_count": 281424},
    "黃仁": {"district": "台中市第五選舉區", "electoral_count": 328423},
    "黃國書": {"district": "台中市第七選舉區", "electoral_count": 339084},
    "黃建豪": {"district": "新竹市選舉區", "electoral_count": 356870},
    "楊瓊瓔": {"district": "台中市第四選舉區", "electoral_count": 334423},
    "萬美玲": {"district": "桃園市第四選舉區", "electoral_count": 282373},
    "葉元之": {"district": "新北市第十選舉區", "electoral_count": 279889},
    "廖先翔": {"district": "台中市第六選舉區", "electoral_count": 320956},
    "廖偉翔": {"district": "新北市第七選舉區", "electoral_count": 354567},
    "劉建國": {"district": "雲林縣第二選舉區", "electoral_count": 330024},
    "歐陽立委": {"district": "台北市第十一選舉區", "electoral_count": 245000},
    "蔡培慧": {"district": "南投縣第二選舉區", "electoral_count": 280423},
    "蔡適應": {"district": "基隆市選舉區", "electoral_count": 308092},
    "賴士葆": {"district": "台北市第八選舉區", "electoral_count": 281424},
    "賴惠員": {"district": "嘉義縣第二選舉區", "electoral_count": 265678},
    "鄭正鈐": {"district": "新竹市選舉區", "electoral_count": 356870},
    "魯明哲": {"district": "桃園市第三選舉區", "electoral_count": 326940},
    "顏寬恒": {"district": "台中市第二選舉區", "electoral_count": 354123},
    "羅廷瑋": {"district": "南投縣第一選舉區", "electoral_count": 297894},
    "羅明才": {"district": "台中市第一選舉區", "electoral_count": 345678},
    "羅智強": {"district": "台北市第五選舉區", "electoral_count": 240496},
    "謝衣鳳": {"district": "花蓮縣選舉區", "electoral_count": 267894},
    "高虹安": {"district": "新竹市選舉區", "electoral_count": 356870}
}

class DevelopmentConfig(Config):
    """本地開發環境配置"""
    DEBUG = True
    ENV = 'development'
    # 開發環境使用本地 MongoDB
    MONGODB_URI = 'mongodb://localhost:27017'
    MONGODB_DBNAME = 'legislator_recall'

class AtlasConfig(Config):
    """MongoDB Atlas 雲端生產環境配置"""
    DEBUG = False
    ENV = 'production'
    
    @property
    def MONGODB_URI(self):
        """從環境變數獲取 MongoDB Atlas 連接字串"""
        uri = os.environ.get('MONGODB_ATLAS_URI')
        if not uri:
            # 備用環境變數名稱
            uri = os.environ.get('MONGODB_URI')
            
        if not uri:
            raise ValueError("必須設置 MONGODB_ATLAS_URI 或 MONGODB_URI 環境變數")
            
        # 確保使用 Atlas 格式的連接字串
        if not uri.startswith('mongodb+srv://') and not uri.startswith('mongodb://'):
            raise ValueError("MongoDB Atlas 連接字串格式不正確，應以 mongodb+srv:// 開頭")
        
        # 處理無密碼的情況
        if '<db_password>' in uri:
            # 如果沒有設置密碼，使用本地MongoDB
            logger.warning("MongoDB Atlas 密碼未設置，將使用本地MongoDB")
            return 'mongodb://localhost:27017'
            
        return uri
    
    @property
    def MONGODB_DBNAME(self):
        """從環境變數獲取資料庫名稱"""
        return os.environ.get('MONGODB_ATLAS_DBNAME', 'legislator_recall')

def get_config():
    """根據環境變數自動選擇配置
    
    - development: 本地開發環境
    - production: MongoDB Atlas 生產環境
    """
    # 每次重新讀取環境變數，避免使用緩存
    config = os.environ.get('FLASK_CONFIG', 'development').lower()
    
    # 顯示當前使用的配置模式
    logger.info(f"使用配置模式: {config}")
    
    # 開發環境強制使用本地MongoDB
    if config == 'development':
        logger.info("🔧 開發環境：強制使用本地MongoDB")
        return DevelopmentConfig
    
    # 檢查Atlas URI是否有效（不是模板）
    atlas_uri = os.environ.get('MONGODB_ATLAS_URI') or os.environ.get('MONGODB_URI', '')
    
    # 如果沒有設置Atlas URI，使用本地MongoDB
    if not atlas_uri:
        logger.info("ℹ️ 未設置MongoDB Atlas URI，使用本地MongoDB")
        return DevelopmentConfig
    
    # 檢查是否包含密碼模板
    if '<db_password>' in atlas_uri or '<password>' in atlas_uri or 'password_placeholder' in atlas_uri:
        logger.warning("⚠️ 檢測到無效的Atlas URI（包含密碼模板），自動切換到本地MongoDB")
        logger.info("💡 提示：請設置正確的MONGODB_ATLAS_URI環境變數")
        return DevelopmentConfig
    
    # 檢查Atlas URI格式是否正確
    if not (atlas_uri.startswith('mongodb+srv://') or atlas_uri.startswith('mongodb://')):
        logger.warning("⚠️ Atlas URI格式不正確，自動切換到本地MongoDB")
        return DevelopmentConfig
    
    # 檢查是否為有效的Atlas URI
    if atlas_uri and 'mongodb+srv://' in atlas_uri and '<db_password>' not in atlas_uri:
        logger.info("✅ 檢測到有效Atlas URI，使用 Atlas 配置")
        return AtlasConfig
    
    # 判斷是否使用生產環境
    if config == 'production':
        return AtlasConfig
    else:
        # 預設使用開發環境配置
        return DevelopmentConfig

def get_mongo_config():
    """
    根據當前配置獲取 MongoDB 連接資訊
    自動根據環境選擇本地或 Atlas 雲端資料庫
    並返回優化的連線參數
    """
    config = get_config()()  # 獲取配置實例

    mongodb_uri = config.MONGODB_URI
    mongodb_dbname = config.MONGODB_DBNAME

    # MongoDB Atlas 連線優化參數
    mongo_options = {
        'maxPoolSize': config.MONGODB_MAX_POOL_SIZE,
        'maxIdleTimeMS': config.MONGODB_MAX_IDLE_TIME_MS,
        'connectTimeoutMS': config.MONGODB_CONNECT_TIMEOUT_MS,
        'socketTimeoutMS': config.MONGODB_SOCKET_TIMEOUT_MS,
        'serverSelectionTimeoutMS': config.MONGODB_SERVER_SELECTION_TIMEOUT_MS,
        'waitQueueTimeoutMS': config.MONGODB_WAIT_QUEUE_TIMEOUT_MS,
        'heartbeatFrequencyMS': config.MONGODB_HEARTBEAT_FREQUENCY_MS,
        'retryWrites': config.MONGODB_RETRY_WRITES,
        'retryReads': config.MONGODB_RETRY_READS,
        'w': config.MONGODB_W,
        'journal': config.MONGODB_JOURNAL,
        'appName': config.MONGODB_APP_NAME
    }

    # 如果是 Atlas 連接，添加 Atlas 特有的參數
    if mongodb_uri.startswith('mongodb+srv://'):
        mongo_options.update({
            'tls': True,  # Atlas 需要 TLS
            'tlsAllowInvalidCertificates': False,  # 安全考量
        })

    # 輸出連接信息（僅在開發環境）
    if config.ENV == 'development':
        logger.info(f"MongoDB 連接: {config.ENV} 環境")
        logger.info(f"MongoDB URI: {mongodb_uri}")
        logger.info(f"MongoDB 資料庫: {mongodb_dbname}")
    else:
        logger.info(f"MongoDB 連接: MongoDB Atlas 雲端環境")
        logger.info(f"MongoDB 資料庫: {mongodb_dbname}")
        # 安全考量：不顯示完整 URI
        if mongodb_uri:
            masked_uri = mongodb_uri[:20] + "***" + mongodb_uri[-10:] if len(mongodb_uri) > 30 else "***"
            logger.info(f"MongoDB URI: {masked_uri}")

    return {
        'MONGODB_URI': mongodb_uri,
        'MONGODB_DBNAME': mongodb_dbname,
        'MONGODB_OPTIONS': mongo_options
    }