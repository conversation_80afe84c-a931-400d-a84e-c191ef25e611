{"version": 3, "sources": ["../../../../../../node_modules/angular-tag-cloud-module/fesm2022/angular-tag-cloud-module.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { input, output, computed, inject, ElementRef, Renderer2, effect, HostListener, ChangeDetectionStrategy, Component } from '@angular/core';\nconst DEFAULT_HEIGHT = 400;\nconst DEFAULT_WIDTH = 1;\nconst DEFAULT_STEP = 1;\nclass TagCloudComponent {\n  get calculatedWidth() {\n    let width = this.localConfig().width || this.width() || DEFAULT_WIDTH;\n    if (this.el.nativeElement.parentNode.offsetWidth > 0 && width <= 1 && width > 0) {\n      width = this.el.nativeElement.parentNode.offsetWidth * width;\n    }\n    return width;\n  }\n  get calculatedHeight() {\n    let height = this.localConfig().height || this.height() || DEFAULT_HEIGHT;\n    if (this.el.nativeElement.parentNode.offsetHeight > 0 && height <= 1 && height > 0) {\n      height = this.el.nativeElement.parentNode.offsetHeight * height;\n    }\n    return height;\n  }\n  onResize(event) {\n    this.logMessage('debug', 'rezisze triggered');\n    window.clearTimeout(this.timeoutId);\n    this.timeoutId = window.setTimeout(() => {\n      if (this.options.realignOnResize) {\n        this.reDraw();\n      }\n    }, 200);\n  }\n  constructor() {\n    this.data = input([]);\n    this.width = input();\n    this.height = input();\n    this.step = input();\n    this.overflow = input();\n    this.strict = input();\n    this.zoomOnHover = input();\n    this.realignOnResize = input();\n    this.randomizeAngle = input();\n    this.background = input();\n    this.font = input();\n    this.delay = input();\n    this.config = input({});\n    this.log = input();\n    this.clicked = output();\n    // dataChanges = output<SimpleChanges>();\n    this.afterInit = output();\n    this.afterChecked = output();\n    this.localConfig = computed(() => {\n      const config = this.config();\n      const localConfig = {\n        ...config,\n        // override default width params in config object\n        width: this.width() || config.width || 500,\n        height: this.height() || config.height || 300,\n        overflow: this.overflow() ?? (config.overflow || true),\n        strict: this.strict() ?? (config.strict || false),\n        zoomOnHover: this.zoomOnHover() || config.zoomOnHover || {\n          transitionTime: 0,\n          scale: 1,\n          delay: 0\n        },\n        realignOnResize: this.realignOnResize() ?? (config.realignOnResize || false),\n        randomizeAngle: this.randomizeAngle() ?? (config.randomizeAngle || false),\n        step: this.step() || config.step || 2.0,\n        log: this.log() || config.log || false,\n        delay: this.delay() || config.delay,\n        background: this.background() || config.background,\n        font: this.font() || config.font\n      };\n      return localConfig;\n    });\n    this.cloudDataHtmlElements = [];\n    this.dataArr = [];\n    this.el = inject(ElementRef);\n    this.r2 = inject(Renderer2);\n    const el = this.el.nativeElement;\n    effect(() => {\n      // this.logMessage('debug', 'ngOnChanges fired', changes);\n      // set default values\n      const config = this.localConfig();\n      this.logMessage('warn', 'cloud configuration', config);\n      // set the basic font style if property is provided\n      if (config.font) {\n        this.r2.setStyle(el, 'font', config.font);\n      }\n      // set a background image if property is provided\n      if (config.background) {\n        this.r2.setStyle(el, 'background', config.background);\n      }\n      this.reDraw();\n    });\n  }\n  // ngAfterContentInit() {\n  //   this.afterInit?.emit();\n  //   this.logMessage('debug', 'afterInit emitted');\n  // }\n  // ngAfterContentChecked() {\n  //   this.afterChecked?.emit();\n  //   this.logMessage('debug', 'afterChecked emitted');\n  // }\n  /**\n   * re-draw the word cloud\n   * @param changes the change set\n   */\n  reDraw() {\n    // this.dataChanges?.emit(changes);\n    this.afterChecked?.emit();\n    this.logMessage('debug', 'dataChanges emitted');\n    this.cloudDataHtmlElements = [];\n    // check if data is not null or empty\n    if (!this.data()) {\n      console.error('angular-tag-cloud: No data passed. Please pass an Array of CloudData');\n      return;\n    }\n    // values changed, reset cloud\n    this.el.nativeElement.innerHTML = '';\n    // set value changes\n    if (this.data()) {\n      this.dataArr = this.data();\n    }\n    // set options\n    this.options = {\n      ...this.localConfig(),\n      aspectRatio: this.calculatedWidth / this.calculatedHeight,\n      width: this.calculatedWidth,\n      height: this.calculatedHeight,\n      center: {\n        x: this.calculatedWidth / 2.0,\n        y: this.calculatedHeight / 2.0\n      }\n    };\n    // set the dimensions\n    this.r2.setStyle(this.el.nativeElement, 'width', this.options.width + 'px');\n    this.r2.setStyle(this.el.nativeElement, 'height', this.options.height + 'px');\n    // draw the cloud\n    this.drawWordCloud();\n    this.logMessage('debug', 'reDraw finished');\n  }\n  /**\n   * helper to generate a descriptive string for an entry to use when sorting alphabetically\n   * @param entry the cloud entry to be used\n   */\n  descriptiveEntry(entry) {\n    let description = entry.text;\n    description += entry.color ? `-${entry.color}` : '';\n    description += entry.external ? `-${entry.external}` : '';\n    description += entry.link ? `-${entry.link}` : '';\n    description += entry.rotate ? `-${entry.rotate}` : '';\n    return description;\n  }\n  /**\n   * proceed draw the cloud\n   */\n  drawWordCloud() {\n    // Sort alphabetically to ensure that, all things being equal, words are placed uniformly\n    this.dataArr.sort((a, b) => this.descriptiveEntry(a).localeCompare(this.descriptiveEntry(b)));\n    // Sort this._dataArr from the word with the highest weight to the one with the lowest\n    this.dataArr.sort((a, b) => b.weight - a.weight);\n    // place fixed elements first\n    const elementsWithFixedPositions = this.dataArr.filter(item => item.position);\n    const elementsWithRandomPositions = this.dataArr.filter(item => !item.position);\n    elementsWithFixedPositions.forEach((elem, index) => {\n      this.drawWord(index, elem);\n    });\n    elementsWithRandomPositions.forEach((elem, index) => {\n      this.drawWord(index, elem);\n    });\n  }\n  /**\n   * Helper function to test if an element overlaps others\n   * @param rect the DOM rectangle that represents the element's bounds\n   */\n  hitTest(rect) {\n    // Check elements for overlap one by one, stop and return false as soon as an overlap is found\n    for (const item of this.cloudDataHtmlElements) {\n      if (this.overlapping(rect, item)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /**\n   * Pairwise overlap detection\n   * @param rect the DOM rectangle that represents the element's bounds\n   * @param e2 the second element for overlap detection\n   */\n  overlapping(rect, e2) {\n    const {\n      offsetLeft,\n      offsetTop,\n      offsetWidth,\n      offsetHeight\n    } = e2;\n    const offsetRight = offsetLeft + offsetWidth;\n    const offsetBottom = offsetTop + offsetHeight;\n    const overlap = !(rect.right < offsetLeft || rect.left > offsetRight || rect.bottom < offsetTop || rect.top > offsetBottom);\n    return overlap;\n  }\n  /**\n   * Check if min(weight) > max(weight) otherwise use default\n   * @param word the particular word configuration\n   */\n  getWeightForWord(word) {\n    let weight = 5;\n    if (this.dataArr[0].weight > this.dataArr[this.dataArr.length - 1].weight) {\n      // check if strict mode is active\n      if (!this.options.strict) {\n        // Linearly map the original weight to a discrete scale from 1 to 10\n        weight = Math.round((word.weight - this.dataArr[this.dataArr.length - 1].weight) / (this.dataArr[0].weight - this.dataArr[this.dataArr.length - 1].weight) * 9.0) + 1;\n      } else {\n        // use given value for weigth directly\n        // fallback to 10\n        if (word.weight > 10) {\n          weight = 10;\n          this.logMessage('warn', `[TagCloud strict] Weight property ${word.weight} > 10. Fallback to 10 as you are using strict mode`, word);\n        } else if (word.weight < 1) {\n          // fallback to 1\n          weight = 1;\n          this.logMessage('warn', `[TagCloud strict] Given weight property ${word.weight} < 1. Fallback to 1 as you are using strict mode`, word);\n        } else if (word.weight % 1 !== 0) {\n          // round if given value is not an integer\n          weight = Math.round(word.weight);\n          this.logMessage('warn', `[TagCloud strict] Given weight property ${word.weight} is not an integer. Rounded value to ${weight}`, word);\n        } else {\n          weight = word.weight;\n        }\n      }\n    }\n    return weight;\n  }\n  /**\n   * change the HTMLElements color style\n   * @param el the HTML element\n   * @param color the CSS color value\n   */\n  setWordColor(el, color) {\n    this.r2.setStyle(el, 'color', color);\n  }\n  /**\n   * Add a tooltip to the element\n   * @param el the HTML element\n   * @param tooltip the tooltip text\n   */\n  setTooltip(el, tooltip) {\n    this.r2.addClass(el, 'tooltip');\n    const tooltipSpan = this.r2.createElement('span');\n    tooltipSpan.className = 'tooltiptext';\n    const text = this.r2.createText(tooltip);\n    tooltipSpan.appendChild(text);\n    el.appendChild(tooltipSpan);\n  }\n  /**\n   * change the HTMLElements rotation style\n   * @param el the HTML element\n   * @param deg the rotation value (degrees)\n   */\n  setWordRotation(el, deg) {\n    const transformString = deg ? `rotate(${deg}deg)` : '';\n    this.r2.setStyle(el, 'transform', transformString);\n    return transformString;\n  }\n  /**\n   * wrap the given node into an HTML anchor element\n   * @param node the HTML node that should be wrapped\n   * @param word the particular word configuration\n   */\n  wrapNodeIntoAnchorElement(node, word) {\n    const wordLink = this.r2.createElement('a');\n    wordLink.href = word.link || '';\n    if (word.external !== undefined && word.external) {\n      wordLink.target = '_blank';\n    }\n    wordLink.appendChild(node);\n    return wordLink;\n  }\n  /**\n   * wrap the given node into an HTML anchor element\n   * @param node the HTML node that should be wrapped\n   * @param word the particular word configuration\n   */\n  applyZoomStyle(node, el, link, transformString) {\n    if (this.options.zoomOnHover && this.options.zoomOnHover.scale !== 1) {\n      if (!this.options.zoomOnHover.transitionTime) {\n        this.options.zoomOnHover.transitionTime = 0;\n      }\n      if (!this.options.zoomOnHover.scale) {\n        this.options.zoomOnHover.scale = 1;\n      }\n      el.onmouseover = () => {\n        if (this.options.zoomOnHover?.transitionTime) {\n          this.r2.setStyle(el, 'transition', `transform ${this.options.zoomOnHover.transitionTime}s`);\n        }\n        if (this.options.zoomOnHover?.scale) {\n          this.r2.setStyle(el, 'transform', `scale(${this.options.zoomOnHover.scale}) ${transformString}`);\n        }\n        if (this.options.zoomOnHover?.delay) {\n          this.r2.setStyle(el, 'transition-delay', `${this.options.zoomOnHover.delay}s`);\n        }\n        if (this.options.zoomOnHover?.color) {\n          link ? this.r2.setStyle(node, 'color', this.options.zoomOnHover.color) : this.r2.setStyle(el, 'color', this.options.zoomOnHover.color);\n        }\n      };\n      el.onmouseout = () => {\n        this.r2.setStyle(el, 'transform', `none ${transformString}`);\n        if (this.options.zoomOnHover?.color) {\n          link ? this.r2.removeStyle(node, 'color') : this.r2.removeStyle(el, 'color');\n        }\n      };\n    }\n  }\n  /**\n   * Place the word at a calculated position\n   * @param wordSpan The HTML Span element to be placed\n   * @param word The word to be placed\n   * @param index The index of the element\n   */\n  setPosition(wordSpan, word, index) {\n    let angle = this.options.randomizeAngle ? 6.28 * Math.random() : 0;\n    let radius = 0;\n    // Save a reference to the style property, for better performance\n    const wordStyle = wordSpan.style;\n    wordStyle.position = 'absolute';\n    const useFixedPosition = Boolean(word.position && word.position.left && word.position.top);\n    const width = wordSpan.offsetWidth;\n    const height = wordSpan.offsetHeight;\n    let left = useFixedPosition && word.position?.left ? word.position.left : this.options.center.x - width / 2;\n    let top = useFixedPosition && word.position?.top ? word.position.top : this.options.center.y - height / 2;\n    // place the first word\n    wordStyle.left = left + 'px';\n    wordStyle.top = top + 'px';\n    // delayed appearance\n    if (this.options.delay) {\n      wordSpan.classList.add('tag-animation-delay');\n      // add custom css properties\n      wordStyle.setProperty('--tag-animation-delay', `${this.options.delay * index}ms`);\n    }\n    // default case: place randomly\n    if (!useFixedPosition) {\n      // do not place the first word always right in the middle\n      if (index === 0) {\n        wordStyle.left = left + (Math.random() - 0.5) * 2 * (this.calculatedWidth / 5) + 'px';\n        wordStyle.top = top + (Math.random() - 0.5) * 2 * (this.calculatedHeight / 5) + '30px';\n      } else {\n        while (this.options.width && this.options.height && wordSpan.offsetHeight && wordSpan.offsetWidth && this.hitTest(new DOMRect(left, top, wordSpan.offsetWidth, wordSpan.offsetHeight))) {\n          radius += this.options.step || DEFAULT_STEP;\n          angle += (index % 2 === 0 ? 1 : -1) * (this.options.step || DEFAULT_STEP);\n          left = this.options.center.x - width / 2.0 + radius * Math.cos(angle) * this.options.aspectRatio;\n          top = this.options.center.y + radius * Math.sin(angle) - height / 2.0;\n        }\n        wordStyle.left = left + 'px';\n        wordStyle.top = top + 'px';\n      }\n    }\n    // Don't render word if part of it would be outside the container\n    if (!this.options.overflow && (left < 0 || top < 0 || left + width > this.calculatedWidth || top + height > this.calculatedHeight)) {\n      this.logMessage('warn', \"Word did not fit into the cloud and overflow is set to 'false'. The element will be removed\", wordSpan);\n      wordSpan.remove();\n      return;\n    }\n  }\n  /**\n   * Methods to draw a word, by moving it in spiral until it finds a suitable empty place.\n   * This will be iterated on each word.\n   * @param index the index number for the word\n   * @param word the particular word configuration\n   */\n  drawWord(index, word) {\n    let wordSpan;\n    // get calculated word weight\n    const weight = this.getWeightForWord(word);\n    // Create a new span and insert node.\n    wordSpan = this.r2.createElement('span');\n    wordSpan.className = `w${weight}`;\n    // emit onclick event\n    wordSpan.onclick = () => {\n      this.clicked?.emit(word);\n    };\n    // Put the word (and its tooltip) in foreground when cursor is above\n    wordSpan.onmouseenter = () => {\n      wordSpan.style.zIndex = '2';\n    };\n    // Otherwise, restore standard priority\n    wordSpan.onmouseleave = () => {\n      wordSpan.style.zIndex = '1';\n    };\n    // append word text\n    let node = this.r2.createText(word.text);\n    // set color\n    if (word.color) this.setWordColor(wordSpan, word.color);\n    // rotate word possibly\n    const transformString = this.setWordRotation(wordSpan, word.rotate);\n    // Append href if there's a link along with the tag\n    if (word.link) node = this.wrapNodeIntoAnchorElement(node, word);\n    // set zoomOption\n    if (this.options.zoomOnHover && this.options.zoomOnHover.scale !== 1) {\n      this.applyZoomStyle(node, wordSpan, word.link, transformString);\n    }\n    wordSpan.appendChild(node);\n    this.r2.appendChild(this.el.nativeElement, wordSpan);\n    // add tooltip if provided\n    if (word.tooltip) this.setTooltip(wordSpan, word.tooltip);\n    // set a unique id\n    wordSpan.id = `angular-tag-cloud-item-${index}`;\n    // define the elements position\n    this.setPosition(wordSpan, word, index);\n    this.logMessage('debug', 'Adds new word <span>', wordSpan);\n    this.cloudDataHtmlElements.push(wordSpan);\n    this.logMessage('debug', 'Placed words', this.cloudDataHtmlElements);\n  }\n  /**\n   * Log messages to console\n   * @param level the log level\n   * @param args extra args to be logged\n   */\n  logMessage(level, ...args) {\n    if (!this.localConfig()) {\n      return;\n    }\n    if (this.localConfig().log === 'debug') {\n      console.log(`[AngularTagCloudModule ${level}]`, ...args);\n    } else if (this.localConfig().log === 'warn' && level === 'warn') {\n      console.warn(`[AngularTagCloudModule ${level}]`, ...args);\n    }\n  }\n  static {\n    this.ɵfac = function TagCloudComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TagCloudComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TagCloudComponent,\n      selectors: [[\"angular-tag-cloud\"], [\"ng-tag-cloud\"], [\"ngtc\"]],\n      hostBindings: function TagCloudComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function TagCloudComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        data: [1, \"data\"],\n        width: [1, \"width\"],\n        height: [1, \"height\"],\n        step: [1, \"step\"],\n        overflow: [1, \"overflow\"],\n        strict: [1, \"strict\"],\n        zoomOnHover: [1, \"zoomOnHover\"],\n        realignOnResize: [1, \"realignOnResize\"],\n        randomizeAngle: [1, \"randomizeAngle\"],\n        background: [1, \"background\"],\n        font: [1, \"font\"],\n        delay: [1, \"delay\"],\n        config: [1, \"config\"],\n        log: [1, \"log\"]\n      },\n      outputs: {\n        clicked: \"clicked\",\n        afterInit: \"afterInit\",\n        afterChecked: \"afterChecked\"\n      },\n      decls: 0,\n      vars: 0,\n      template: function TagCloudComponent_Template(rf, ctx) {},\n      styles: [\"[_nghost-%COMP%]{font-family:Helvetica,Arial,sans-serif;font-size:10px;line-height:normal;color:#09f;overflow:hidden;position:relative;display:block}.tag-animation-delay[_ngcontent-%COMP%]{--tag-animation-delay: .5s;animation:_ngcontent-%COMP%_fadeIn .5s;opacity:0;animation-fill-mode:forwards;animation-delay:var(--tag-animation-delay)}span[_ngcontent-%COMP%]{padding:0}span.w10[_ngcontent-%COMP%]{font-size:550%}span.w9[_ngcontent-%COMP%]{font-size:500%}span.w8[_ngcontent-%COMP%]{font-size:450%}span.w7[_ngcontent-%COMP%]{font-size:400%}span.w6[_ngcontent-%COMP%]{font-size:350%}span.w5[_ngcontent-%COMP%]{font-size:300%}span.w4[_ngcontent-%COMP%]{font-size:250%}span.w3[_ngcontent-%COMP%]{font-size:200%}span.w2[_ngcontent-%COMP%]{font-size:150%}span.w1[_ngcontent-%COMP%]{font-size:100%}a[_ngcontent-%COMP%]:hover{color:#0df}a[_ngcontent-%COMP%]:hover, span.w10[_ngcontent-%COMP%], span.w9[_ngcontent-%COMP%], span.w8[_ngcontent-%COMP%]{color:#0cf}span.w7[_ngcontent-%COMP%]{color:#39d}span.w6[_ngcontent-%COMP%]{color:#90c5f0}span.w5[_ngcontent-%COMP%]{color:#90a0dd}span.w4[_ngcontent-%COMP%]{color:#90c5f0}span.w3[_ngcontent-%COMP%]{color:#a0ddff}span.w2[_ngcontent-%COMP%]{color:#9ce}span.w1[_ngcontent-%COMP%]{color:#aab5f0}.tooltip[_ngcontent-%COMP%]   .tooltiptext[_ngcontent-%COMP%]{visibility:hidden;width:inherit;background-color:#555;color:#fff;text-align:center;border-radius:6px;padding:5px 10px;position:absolute;bottom:100%;left:0;opacity:0;transition:opacity .3s}.tooltip[_ngcontent-%COMP%]   .tooltiptext[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:100%;left:50%;margin-left:-5px;border-width:5px;border-style:solid;border-color:#555 transparent transparent transparent}.tooltip[_ngcontent-%COMP%]:hover   .tooltiptext[_ngcontent-%COMP%]{visibility:visible;opacity:1}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TagCloudComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'angular-tag-cloud, ng-tag-cloud, ngtc',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: '',\n      styles: [\":host{font-family:Helvetica,Arial,sans-serif;font-size:10px;line-height:normal;color:#09f;overflow:hidden;position:relative;display:block}.tag-animation-delay{--tag-animation-delay: .5s;animation:fadeIn .5s;opacity:0;animation-fill-mode:forwards;animation-delay:var(--tag-animation-delay)}span{padding:0}span.w10{font-size:550%}span.w9{font-size:500%}span.w8{font-size:450%}span.w7{font-size:400%}span.w6{font-size:350%}span.w5{font-size:300%}span.w4{font-size:250%}span.w3{font-size:200%}span.w2{font-size:150%}span.w1{font-size:100%}a:hover{color:#0df}a:hover,span.w10,span.w9,span.w8{color:#0cf}span.w7{color:#39d}span.w6{color:#90c5f0}span.w5{color:#90a0dd}span.w4{color:#90c5f0}span.w3{color:#a0ddff}span.w2{color:#9ce}span.w1{color:#aab5f0}.tooltip .tooltiptext{visibility:hidden;width:inherit;background-color:#555;color:#fff;text-align:center;border-radius:6px;padding:5px 10px;position:absolute;bottom:100%;left:0;opacity:0;transition:opacity .3s}.tooltip .tooltiptext:after{content:\\\"\\\";position:absolute;top:100%;left:50%;margin-left:-5px;border-width:5px;border-style:solid;border-color:#555 transparent transparent transparent}.tooltip:hover .tooltiptext{visibility:visible;opacity:1}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}\\n\"]\n    }]\n  }], () => [], {\n    onResize: [{\n      type: HostListener,\n      args: ['window:resize', ['$event']]\n    }]\n  });\n})();\n\n/*\n * Public API Surface of angular-tag-cloud-module\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TagCloudComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,eAAe;AACrB,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,IAAI,kBAAkB;AACpB,QAAI,QAAQ,KAAK,YAAY,EAAE,SAAS,KAAK,MAAM,KAAK;AACxD,QAAI,KAAK,GAAG,cAAc,WAAW,cAAc,KAAK,SAAS,KAAK,QAAQ,GAAG;AAC/E,cAAQ,KAAK,GAAG,cAAc,WAAW,cAAc;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,mBAAmB;AACrB,QAAI,SAAS,KAAK,YAAY,EAAE,UAAU,KAAK,OAAO,KAAK;AAC3D,QAAI,KAAK,GAAG,cAAc,WAAW,eAAe,KAAK,UAAU,KAAK,SAAS,GAAG;AAClF,eAAS,KAAK,GAAG,cAAc,WAAW,eAAe;AAAA,IAC3D;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,OAAO;AACd,SAAK,WAAW,SAAS,mBAAmB;AAC5C,WAAO,aAAa,KAAK,SAAS;AAClC,SAAK,YAAY,OAAO,WAAW,MAAM;AACvC,UAAI,KAAK,QAAQ,iBAAiB;AAChC,aAAK,OAAO;AAAA,MACd;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,MAAM,CAAC,CAAC;AACpB,SAAK,QAAQ,MAAM;AACnB,SAAK,SAAS,MAAM;AACpB,SAAK,OAAO,MAAM;AAClB,SAAK,WAAW,MAAM;AACtB,SAAK,SAAS,MAAM;AACpB,SAAK,cAAc,MAAM;AACzB,SAAK,kBAAkB,MAAM;AAC7B,SAAK,iBAAiB,MAAM;AAC5B,SAAK,aAAa,MAAM;AACxB,SAAK,OAAO,MAAM;AAClB,SAAK,QAAQ,MAAM;AACnB,SAAK,SAAS,MAAM,CAAC,CAAC;AACtB,SAAK,MAAM,MAAM;AACjB,SAAK,UAAU,OAAO;AAEtB,SAAK,YAAY,OAAO;AACxB,SAAK,eAAe,OAAO;AAC3B,SAAK,cAAc,SAAS,MAAM;AAChC,YAAM,SAAS,KAAK,OAAO;AAC3B,YAAM,cAAc,iCACf,SADe;AAAA;AAAA,QAGlB,OAAO,KAAK,MAAM,KAAK,OAAO,SAAS;AAAA,QACvC,QAAQ,KAAK,OAAO,KAAK,OAAO,UAAU;AAAA,QAC1C,UAAU,KAAK,SAAS,MAAM,OAAO,YAAY;AAAA,QACjD,QAAQ,KAAK,OAAO,MAAM,OAAO,UAAU;AAAA,QAC3C,aAAa,KAAK,YAAY,KAAK,OAAO,eAAe;AAAA,UACvD,gBAAgB;AAAA,UAChB,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,QACA,iBAAiB,KAAK,gBAAgB,MAAM,OAAO,mBAAmB;AAAA,QACtE,gBAAgB,KAAK,eAAe,MAAM,OAAO,kBAAkB;AAAA,QACnE,MAAM,KAAK,KAAK,KAAK,OAAO,QAAQ;AAAA,QACpC,KAAK,KAAK,IAAI,KAAK,OAAO,OAAO;AAAA,QACjC,OAAO,KAAK,MAAM,KAAK,OAAO;AAAA,QAC9B,YAAY,KAAK,WAAW,KAAK,OAAO;AAAA,QACxC,MAAM,KAAK,KAAK,KAAK,OAAO;AAAA,MAC9B;AACA,aAAO;AAAA,IACT,CAAC;AACD,SAAK,wBAAwB,CAAC;AAC9B,SAAK,UAAU,CAAC;AAChB,SAAK,KAAK,OAAO,UAAU;AAC3B,SAAK,KAAK,OAAO,SAAS;AAC1B,UAAM,KAAK,KAAK,GAAG;AACnB,WAAO,MAAM;AAGX,YAAM,SAAS,KAAK,YAAY;AAChC,WAAK,WAAW,QAAQ,uBAAuB,MAAM;AAErD,UAAI,OAAO,MAAM;AACf,aAAK,GAAG,SAAS,IAAI,QAAQ,OAAO,IAAI;AAAA,MAC1C;AAEA,UAAI,OAAO,YAAY;AACrB,aAAK,GAAG,SAAS,IAAI,cAAc,OAAO,UAAU;AAAA,MACtD;AACA,WAAK,OAAO;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,SAAS;AAEP,SAAK,cAAc,KAAK;AACxB,SAAK,WAAW,SAAS,qBAAqB;AAC9C,SAAK,wBAAwB,CAAC;AAE9B,QAAI,CAAC,KAAK,KAAK,GAAG;AAChB,cAAQ,MAAM,sEAAsE;AACpF;AAAA,IACF;AAEA,SAAK,GAAG,cAAc,YAAY;AAElC,QAAI,KAAK,KAAK,GAAG;AACf,WAAK,UAAU,KAAK,KAAK;AAAA,IAC3B;AAEA,SAAK,UAAU,iCACV,KAAK,YAAY,IADP;AAAA,MAEb,aAAa,KAAK,kBAAkB,KAAK;AAAA,MACzC,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,QAAQ;AAAA,QACN,GAAG,KAAK,kBAAkB;AAAA,QAC1B,GAAG,KAAK,mBAAmB;AAAA,MAC7B;AAAA,IACF;AAEA,SAAK,GAAG,SAAS,KAAK,GAAG,eAAe,SAAS,KAAK,QAAQ,QAAQ,IAAI;AAC1E,SAAK,GAAG,SAAS,KAAK,GAAG,eAAe,UAAU,KAAK,QAAQ,SAAS,IAAI;AAE5E,SAAK,cAAc;AACnB,SAAK,WAAW,SAAS,iBAAiB;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,OAAO;AACtB,QAAI,cAAc,MAAM;AACxB,mBAAe,MAAM,QAAQ,IAAI,MAAM,KAAK,KAAK;AACjD,mBAAe,MAAM,WAAW,IAAI,MAAM,QAAQ,KAAK;AACvD,mBAAe,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK;AAC/C,mBAAe,MAAM,SAAS,IAAI,MAAM,MAAM,KAAK;AACnD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AAEd,SAAK,QAAQ,KAAK,CAAC,GAAG,MAAM,KAAK,iBAAiB,CAAC,EAAE,cAAc,KAAK,iBAAiB,CAAC,CAAC,CAAC;AAE5F,SAAK,QAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM;AAE/C,UAAM,6BAA6B,KAAK,QAAQ,OAAO,UAAQ,KAAK,QAAQ;AAC5E,UAAM,8BAA8B,KAAK,QAAQ,OAAO,UAAQ,CAAC,KAAK,QAAQ;AAC9E,+BAA2B,QAAQ,CAAC,MAAM,UAAU;AAClD,WAAK,SAAS,OAAO,IAAI;AAAA,IAC3B,CAAC;AACD,gCAA4B,QAAQ,CAAC,MAAM,UAAU;AACnD,WAAK,SAAS,OAAO,IAAI;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,MAAM;AAEZ,eAAW,QAAQ,KAAK,uBAAuB;AAC7C,UAAI,KAAK,YAAY,MAAM,IAAI,GAAG;AAChC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,MAAM,IAAI;AACpB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,cAAc,aAAa;AACjC,UAAM,eAAe,YAAY;AACjC,UAAM,UAAU,EAAE,KAAK,QAAQ,cAAc,KAAK,OAAO,eAAe,KAAK,SAAS,aAAa,KAAK,MAAM;AAC9G,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,MAAM;AACrB,QAAI,SAAS;AACb,QAAI,KAAK,QAAQ,CAAC,EAAE,SAAS,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,EAAE,QAAQ;AAEzE,UAAI,CAAC,KAAK,QAAQ,QAAQ;AAExB,iBAAS,KAAK,OAAO,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,EAAE,WAAW,KAAK,QAAQ,CAAC,EAAE,SAAS,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,EAAE,UAAU,CAAG,IAAI;AAAA,MACtK,OAAO;AAGL,YAAI,KAAK,SAAS,IAAI;AACpB,mBAAS;AACT,eAAK,WAAW,QAAQ,qCAAqC,KAAK,MAAM,sDAAsD,IAAI;AAAA,QACpI,WAAW,KAAK,SAAS,GAAG;AAE1B,mBAAS;AACT,eAAK,WAAW,QAAQ,2CAA2C,KAAK,MAAM,oDAAoD,IAAI;AAAA,QACxI,WAAW,KAAK,SAAS,MAAM,GAAG;AAEhC,mBAAS,KAAK,MAAM,KAAK,MAAM;AAC/B,eAAK,WAAW,QAAQ,2CAA2C,KAAK,MAAM,wCAAwC,MAAM,IAAI,IAAI;AAAA,QACtI,OAAO;AACL,mBAAS,KAAK;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,IAAI,OAAO;AACtB,SAAK,GAAG,SAAS,IAAI,SAAS,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,IAAI,SAAS;AACtB,SAAK,GAAG,SAAS,IAAI,SAAS;AAC9B,UAAM,cAAc,KAAK,GAAG,cAAc,MAAM;AAChD,gBAAY,YAAY;AACxB,UAAM,OAAO,KAAK,GAAG,WAAW,OAAO;AACvC,gBAAY,YAAY,IAAI;AAC5B,OAAG,YAAY,WAAW;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,IAAI,KAAK;AACvB,UAAM,kBAAkB,MAAM,UAAU,GAAG,SAAS;AACpD,SAAK,GAAG,SAAS,IAAI,aAAa,eAAe;AACjD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B,MAAM,MAAM;AACpC,UAAM,WAAW,KAAK,GAAG,cAAc,GAAG;AAC1C,aAAS,OAAO,KAAK,QAAQ;AAC7B,QAAI,KAAK,aAAa,UAAa,KAAK,UAAU;AAChD,eAAS,SAAS;AAAA,IACpB;AACA,aAAS,YAAY,IAAI;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,MAAM,IAAI,MAAM,iBAAiB;AAC9C,QAAI,KAAK,QAAQ,eAAe,KAAK,QAAQ,YAAY,UAAU,GAAG;AACpE,UAAI,CAAC,KAAK,QAAQ,YAAY,gBAAgB;AAC5C,aAAK,QAAQ,YAAY,iBAAiB;AAAA,MAC5C;AACA,UAAI,CAAC,KAAK,QAAQ,YAAY,OAAO;AACnC,aAAK,QAAQ,YAAY,QAAQ;AAAA,MACnC;AACA,SAAG,cAAc,MAAM;AACrB,YAAI,KAAK,QAAQ,aAAa,gBAAgB;AAC5C,eAAK,GAAG,SAAS,IAAI,cAAc,aAAa,KAAK,QAAQ,YAAY,cAAc,GAAG;AAAA,QAC5F;AACA,YAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,eAAK,GAAG,SAAS,IAAI,aAAa,SAAS,KAAK,QAAQ,YAAY,KAAK,KAAK,eAAe,EAAE;AAAA,QACjG;AACA,YAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,eAAK,GAAG,SAAS,IAAI,oBAAoB,GAAG,KAAK,QAAQ,YAAY,KAAK,GAAG;AAAA,QAC/E;AACA,YAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAO,KAAK,GAAG,SAAS,MAAM,SAAS,KAAK,QAAQ,YAAY,KAAK,IAAI,KAAK,GAAG,SAAS,IAAI,SAAS,KAAK,QAAQ,YAAY,KAAK;AAAA,QACvI;AAAA,MACF;AACA,SAAG,aAAa,MAAM;AACpB,aAAK,GAAG,SAAS,IAAI,aAAa,QAAQ,eAAe,EAAE;AAC3D,YAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAO,KAAK,GAAG,YAAY,MAAM,OAAO,IAAI,KAAK,GAAG,YAAY,IAAI,OAAO;AAAA,QAC7E;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,UAAU,MAAM,OAAO;AACjC,QAAI,QAAQ,KAAK,QAAQ,iBAAiB,OAAO,KAAK,OAAO,IAAI;AACjE,QAAI,SAAS;AAEb,UAAM,YAAY,SAAS;AAC3B,cAAU,WAAW;AACrB,UAAM,mBAAmB,QAAQ,KAAK,YAAY,KAAK,SAAS,QAAQ,KAAK,SAAS,GAAG;AACzF,UAAM,QAAQ,SAAS;AACvB,UAAM,SAAS,SAAS;AACxB,QAAI,OAAO,oBAAoB,KAAK,UAAU,OAAO,KAAK,SAAS,OAAO,KAAK,QAAQ,OAAO,IAAI,QAAQ;AAC1G,QAAI,MAAM,oBAAoB,KAAK,UAAU,MAAM,KAAK,SAAS,MAAM,KAAK,QAAQ,OAAO,IAAI,SAAS;AAExG,cAAU,OAAO,OAAO;AACxB,cAAU,MAAM,MAAM;AAEtB,QAAI,KAAK,QAAQ,OAAO;AACtB,eAAS,UAAU,IAAI,qBAAqB;AAE5C,gBAAU,YAAY,yBAAyB,GAAG,KAAK,QAAQ,QAAQ,KAAK,IAAI;AAAA,IAClF;AAEA,QAAI,CAAC,kBAAkB;AAErB,UAAI,UAAU,GAAG;AACf,kBAAU,OAAO,QAAQ,KAAK,OAAO,IAAI,OAAO,KAAK,KAAK,kBAAkB,KAAK;AACjF,kBAAU,MAAM,OAAO,KAAK,OAAO,IAAI,OAAO,KAAK,KAAK,mBAAmB,KAAK;AAAA,MAClF,OAAO;AACL,eAAO,KAAK,QAAQ,SAAS,KAAK,QAAQ,UAAU,SAAS,gBAAgB,SAAS,eAAe,KAAK,QAAQ,IAAI,QAAQ,MAAM,KAAK,SAAS,aAAa,SAAS,YAAY,CAAC,GAAG;AACtL,oBAAU,KAAK,QAAQ,QAAQ;AAC/B,oBAAU,QAAQ,MAAM,IAAI,IAAI,OAAO,KAAK,QAAQ,QAAQ;AAC5D,iBAAO,KAAK,QAAQ,OAAO,IAAI,QAAQ,IAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ;AACrF,gBAAM,KAAK,QAAQ,OAAO,IAAI,SAAS,KAAK,IAAI,KAAK,IAAI,SAAS;AAAA,QACpE;AACA,kBAAU,OAAO,OAAO;AACxB,kBAAU,MAAM,MAAM;AAAA,MACxB;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,QAAQ,aAAa,OAAO,KAAK,MAAM,KAAK,OAAO,QAAQ,KAAK,mBAAmB,MAAM,SAAS,KAAK,mBAAmB;AAClI,WAAK,WAAW,QAAQ,+FAA+F,QAAQ;AAC/H,eAAS,OAAO;AAChB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,OAAO,MAAM;AACpB,QAAI;AAEJ,UAAM,SAAS,KAAK,iBAAiB,IAAI;AAEzC,eAAW,KAAK,GAAG,cAAc,MAAM;AACvC,aAAS,YAAY,IAAI,MAAM;AAE/B,aAAS,UAAU,MAAM;AACvB,WAAK,SAAS,KAAK,IAAI;AAAA,IACzB;AAEA,aAAS,eAAe,MAAM;AAC5B,eAAS,MAAM,SAAS;AAAA,IAC1B;AAEA,aAAS,eAAe,MAAM;AAC5B,eAAS,MAAM,SAAS;AAAA,IAC1B;AAEA,QAAI,OAAO,KAAK,GAAG,WAAW,KAAK,IAAI;AAEvC,QAAI,KAAK,MAAO,MAAK,aAAa,UAAU,KAAK,KAAK;AAEtD,UAAM,kBAAkB,KAAK,gBAAgB,UAAU,KAAK,MAAM;AAElE,QAAI,KAAK,KAAM,QAAO,KAAK,0BAA0B,MAAM,IAAI;AAE/D,QAAI,KAAK,QAAQ,eAAe,KAAK,QAAQ,YAAY,UAAU,GAAG;AACpE,WAAK,eAAe,MAAM,UAAU,KAAK,MAAM,eAAe;AAAA,IAChE;AACA,aAAS,YAAY,IAAI;AACzB,SAAK,GAAG,YAAY,KAAK,GAAG,eAAe,QAAQ;AAEnD,QAAI,KAAK,QAAS,MAAK,WAAW,UAAU,KAAK,OAAO;AAExD,aAAS,KAAK,0BAA0B,KAAK;AAE7C,SAAK,YAAY,UAAU,MAAM,KAAK;AACtC,SAAK,WAAW,SAAS,wBAAwB,QAAQ;AACzD,SAAK,sBAAsB,KAAK,QAAQ;AACxC,SAAK,WAAW,SAAS,gBAAgB,KAAK,qBAAqB;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,UAAU,MAAM;AACzB,QAAI,CAAC,KAAK,YAAY,GAAG;AACvB;AAAA,IACF;AACA,QAAI,KAAK,YAAY,EAAE,QAAQ,SAAS;AACtC,cAAQ,IAAI,0BAA0B,KAAK,KAAK,GAAG,IAAI;AAAA,IACzD,WAAW,KAAK,YAAY,EAAE,QAAQ,UAAU,UAAU,QAAQ;AAChE,cAAQ,KAAK,0BAA0B,KAAK,KAAK,GAAG,IAAI;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAmB;AAAA,IACtD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,GAAG,CAAC,cAAc,GAAG,CAAC,MAAM,CAAC;AAAA,MAC7D,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,UAAU,SAAS,4CAA4C,QAAQ;AACnF,mBAAO,IAAI,SAAS,MAAM;AAAA,UAC5B,GAAG,OAAU,eAAe;AAAA,QAC9B;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,MAAM,CAAC,GAAG,MAAM;AAAA,QAChB,OAAO,CAAC,GAAG,OAAO;AAAA,QAClB,QAAQ,CAAC,GAAG,QAAQ;AAAA,QACpB,MAAM,CAAC,GAAG,MAAM;AAAA,QAChB,UAAU,CAAC,GAAG,UAAU;AAAA,QACxB,QAAQ,CAAC,GAAG,QAAQ;AAAA,QACpB,aAAa,CAAC,GAAG,aAAa;AAAA,QAC9B,iBAAiB,CAAC,GAAG,iBAAiB;AAAA,QACtC,gBAAgB,CAAC,GAAG,gBAAgB;AAAA,QACpC,YAAY,CAAC,GAAG,YAAY;AAAA,QAC5B,MAAM,CAAC,GAAG,MAAM;AAAA,QAChB,OAAO,CAAC,GAAG,OAAO;AAAA,QAClB,QAAQ,CAAC,GAAG,QAAQ;AAAA,QACpB,KAAK,CAAC,GAAG,KAAK;AAAA,MAChB;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,QACT,WAAW;AAAA,QACX,cAAc;AAAA,MAChB;AAAA,MACA,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AAAA,MAAC;AAAA,MACxD,QAAQ,CAAC,40DAA80D;AAAA,MACv1D,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,QAAQ,CAAC,4tCAA8tC;AAAA,IACzuC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;AAAA,IACpC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;", "names": []}