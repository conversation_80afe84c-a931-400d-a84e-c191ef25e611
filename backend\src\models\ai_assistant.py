"""
AI 助手數據庫模型
包含用戶管理、聊天記錄、使用限制、付費系統等功能
"""

from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import json
import hashlib
import uuid

class User:
    """用戶模型"""
    
    def __init__(self, user_id: str = None):
        self.user_id = user_id or str(uuid.uuid4())
        self.created_at = datetime.now()
        self.last_active = datetime.now()
        self.subscription_level = 'free'  # free, premium, pro
        self.subscription_expires = None
        self.daily_chat_count = 0
        self.monthly_chat_count = 0
        self.total_chat_count = 0
        self.last_reset_date = datetime.now().date()
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat(),
            'last_active': self.last_active.isoformat(),
            'subscription_level': self.subscription_level,
            'subscription_expires': self.subscription_expires.isoformat() if self.subscription_expires else None,
            'daily_chat_count': self.daily_chat_count,
            'monthly_chat_count': self.monthly_chat_count,
            'total_chat_count': self.total_chat_count,
            'last_reset_date': self.last_reset_date.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        user = cls(data.get('user_id'))
        user.created_at = datetime.fromisoformat(data['created_at'])
        user.last_active = datetime.fromisoformat(data['last_active'])
        user.subscription_level = data['subscription_level']
        user.subscription_expires = datetime.fromisoformat(data['subscription_expires']) if data.get('subscription_expires') else None
        user.daily_chat_count = data['daily_chat_count']
        user.monthly_chat_count = data['monthly_chat_count']
        user.total_chat_count = data['total_chat_count']
        user.last_reset_date = datetime.fromisoformat(data['last_reset_date']).date()
        return user

class ChatMessage:
    """聊天訊息模型"""
    
    def __init__(self, message_id: str = None):
        self.message_id = message_id or str(uuid.uuid4())
        self.user_id = None
        self.message_type = 'user'  # user, assistant
        self.content = ''
        self.category = 'general'  # general, guide, analysis, search, trend
        self.timestamp = datetime.now()
        self.tokens_used = 0
        self.api_cost = 0.0
        self.context_data = {}  # 存儲相關的上下文數據
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            'message_id': self.message_id,
            'user_id': self.user_id,
            'message_type': self.message_type,
            'content': self.content,
            'category': self.category,
            'timestamp': self.timestamp.isoformat(),
            'tokens_used': self.tokens_used,
            'api_cost': self.api_cost,
            'context_data': self.context_data
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatMessage':
        message = cls(data.get('message_id'))
        message.user_id = data['user_id']
        message.message_type = data['message_type']
        message.content = data['content']
        message.category = data['category']
        message.timestamp = datetime.fromisoformat(data['timestamp'])
        message.tokens_used = data['tokens_used']
        message.api_cost = data['api_cost']
        message.context_data = data.get('context_data', {})
        return message

class ChatSession:
    """聊天會話模型"""
    
    def __init__(self, session_id: str = None):
        self.session_id = session_id or str(uuid.uuid4())
        self.user_id = None
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.messages: List[ChatMessage] = []
        self.total_tokens = 0
        self.total_cost = 0.0
        
    def add_message(self, message: ChatMessage):
        self.messages.append(message)
        self.last_activity = datetime.now()
        self.total_tokens += message.tokens_used
        self.total_cost += message.api_cost
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat(),
            'last_activity': self.last_activity.isoformat(),
            'messages': [msg.to_dict() for msg in self.messages],
            'total_tokens': self.total_tokens,
            'total_cost': self.total_cost
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatSession':
        session = cls(data.get('session_id'))
        session.user_id = data['user_id']
        session.created_at = datetime.fromisoformat(data['created_at'])
        session.last_activity = datetime.fromisoformat(data['last_activity'])
        session.messages = [ChatMessage.from_dict(msg) for msg in data['messages']]
        session.total_tokens = data['total_tokens']
        session.total_cost = data['total_cost']
        return session

class UsageLimit:
    """使用限制配置"""
    
    FREE_LIMITS = {
        'daily_chats': 10,
        'monthly_chats': 100,
        'max_tokens_per_message': 1000,
        'max_context_length': 5
    }
    
    PREMIUM_LIMITS = {
        'daily_chats': 50,
        'monthly_chats': 500,
        'max_tokens_per_message': 2000,
        'max_context_length': 10
    }
    
    PRO_LIMITS = {
        'daily_chats': 200,
        'monthly_chats': 2000,
        'max_tokens_per_message': 4000,
        'max_context_length': 20
    }
    
    @classmethod
    def get_limits(cls, subscription_level: str) -> Dict[str, int]:
        if subscription_level == 'premium':
            return cls.PREMIUM_LIMITS
        elif subscription_level == 'pro':
            return cls.PRO_LIMITS
        else:
            return cls.FREE_LIMITS

class PaymentRecord:
    """付費記錄模型"""
    
    def __init__(self, payment_id: str = None):
        self.payment_id = payment_id or str(uuid.uuid4())
        self.user_id = None
        self.amount = 0.0
        self.currency = 'TWD'
        self.subscription_level = ''
        self.duration_days = 0
        self.payment_method = ''
        self.status = 'pending'  # pending, completed, failed, refunded
        self.created_at = datetime.now()
        self.completed_at = None
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            'payment_id': self.payment_id,
            'user_id': self.user_id,
            'amount': self.amount,
            'currency': self.currency,
            'subscription_level': self.subscription_level,
            'duration_days': self.duration_days,
            'payment_method': self.payment_method,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PaymentRecord':
        record = cls(data.get('payment_id'))
        record.user_id = data['user_id']
        record.amount = data['amount']
        record.currency = data['currency']
        record.subscription_level = data['subscription_level']
        record.duration_days = data['duration_days']
        record.payment_method = data['payment_method']
        record.status = data['status']
        record.created_at = datetime.fromisoformat(data['created_at'])
        record.completed_at = datetime.fromisoformat(data['completed_at']) if data.get('completed_at') else None
        return record 