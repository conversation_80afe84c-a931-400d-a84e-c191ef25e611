#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
數據整合模組
整合所有平台的爬取資料並整理成gemini格式
"""

import os
import json
import logging
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Any

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class DataIntegrator:
    """數據整合器，整合所有平台的爬取資料"""
    
    def __init__(self, base_dir="backend"):
        self.base_dir = base_dir
        self.crawler_dir = os.path.join(base_dir, "crawler")
        self.data_dir = os.path.join(self.crawler_dir, "data")
        self.processed_dir = os.path.join(self.crawler_dir, "processed")
        self.user_data_dir = os.path.join(self.processed_dir, "user_data")
        
        # 確保目錄存在
        os.makedirs(self.user_data_dir, exist_ok=True)
        
        logger.info(f"🔧 數據整合器初始化完成")
        logger.info(f"  - 基礎目錄: {self.base_dir}")
        logger.info(f"  - 爬蟲目錄: {self.crawler_dir}")
        logger.info(f"  - 數據目錄: {self.data_dir}")
        logger.info(f"  - 處理目錄: {self.processed_dir}")
    
    def integrate_all_platform_data(self, legislator_name: str) -> Dict[str, Any]:
        """
        整合所有平台的數據
        
        Args:
            legislator_name: 立委姓名
            
        Returns:
            Dict: 整合後的數據
        """
        logger.info(f"🔄 開始整合 {legislator_name} 的所有平台數據...")
        
        integrated_data = {}
        
        # 整合各平台數據
        platforms = ['youtube', 'ptt', 'threads', 'facebook']
        
        for platform in platforms:
            platform_data = self.load_platform_data(legislator_name, platform)
            if platform_data:
                integrated_data[platform] = platform_data
                logger.info(f"  ✅ {platform}: {len(platform_data)} 筆數據")
            else:
                logger.info(f"  ⚠️ {platform}: 無數據")
        
        # 生成整合報告
        total_records = sum(len(data) for data in integrated_data.values())
        logger.info(f"📊 整合完成: {legislator_name} 共 {total_records} 筆數據")
        
        return integrated_data
    
    def load_platform_data(self, legislator_name: str, platform: str) -> List[Dict]:
        """載入特定平台的數據"""
        platform_dir = os.path.join(self.data_dir, platform)
        if not os.path.exists(platform_dir):
            return []
        
        data_file = os.path.join(platform_dir, f"{legislator_name}.json")
        if not os.path.exists(data_file):
            return []
        
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data if isinstance(data, list) else [data]
        except Exception as e:
            logger.error(f"❌ 載入 {platform} 數據失敗: {e}")
            return []
    
    def process_to_gemini_format(self, integrated_data: Dict[str, Any], legislator_name: str) -> Dict[str, Any]:
        """
        將整合數據處理成Gemini分析格式
        
        Args:
            integrated_data: 整合後的數據
            legislator_name: 立委姓名
            
        Returns:
            Dict: Gemini格式的數據
        """
        logger.info(f"🧠 開始處理 {legislator_name} 的數據為Gemini格式...")
        
        gemini_data = {}
        
        for platform, platform_data in integrated_data.items():
            logger.info(f"  🔄 處理 {platform} 平台數據...")
            
            if platform == 'youtube':
                gemini_data.update(self.process_youtube_data(platform_data, platform))
            elif platform == 'ptt':
                gemini_data.update(self.process_ptt_data(platform_data, platform))
            elif platform == 'threads':
                gemini_data.update(self.process_threads_data(platform_data, platform))
            elif platform == 'facebook':
                gemini_data.update(self.process_facebook_data(platform_data, platform))
        
        # 保存Gemini格式數據
        gemini_file = os.path.join(self.user_data_dir, f"{legislator_name}_gemini_format.json")
        with open(gemini_file, 'w', encoding='utf-8') as f:
            json.dump(gemini_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ Gemini格式數據已保存: {gemini_file}")
        logger.info(f"📊 共處理 {len(gemini_data)} 位用戶")
        
        return gemini_data
    
    def process_youtube_data(self, data: List[Dict], platform: str) -> Dict[str, Any]:
        """處理YouTube數據為Gemini格式"""
        processed_users = {}
        
        for item in data:
            try:
                # 提取用戶信息
                user_id = item.get('user_id') or item.get('author') or item.get('username', 'unknown')
                if not user_id or user_id == 'unknown':
                    continue
                
                # 提取留言內容
                comment_text = item.get('comment_text') or item.get('text') or item.get('content', '')
                if not comment_text.strip():
                    continue
                
                # 提取標題（如果有）
                title = item.get('video_title') or item.get('title', '')
                
                # 提取時間
                comment_time = item.get('comment_time') or item.get('time') or item.get('published_time', '')
                
                # 構建留言數據
                comment_data = {
                    '標題': title,
                    '留言內容': comment_text,
                    '日期': comment_time,
                    'source': platform
                }
                
                # 添加到用戶數據
                if user_id not in processed_users:
                    processed_users[user_id] = {
                        'comments': [],
                        'latest_date': comment_time,
                        'comment_count': 0
                    }
                
                processed_users[user_id]['comments'].append(comment_data)
                processed_users[user_id]['comment_count'] += 1
                
                # 更新最新日期
                if comment_time and (not processed_users[user_id]['latest_date'] or comment_time > processed_users[user_id]['latest_date']):
                    processed_users[user_id]['latest_date'] = comment_time
                    
            except Exception as e:
                logger.warning(f"⚠️ 處理YouTube數據項目失敗: {e}")
                continue
        
        return processed_users
    
    def process_ptt_data(self, data: List[Dict], platform: str) -> Dict[str, Any]:
        """處理PTT數據為Gemini格式"""
        processed_users = {}
        
        for item in data:
            try:
                # 提取用戶信息
                user_id = item.get('user_id') or item.get('author') or item.get('username', 'unknown')
                if not user_id or user_id == 'unknown':
                    continue
                
                # 提取留言內容
                comment_text = item.get('comment_text') or item.get('text') or item.get('content', '')
                if not comment_text.strip():
                    continue
                
                # 提取標題
                title = item.get('post_title') or item.get('title', '')
                
                # 提取時間
                comment_time = item.get('comment_time') or item.get('time') or item.get('published_time', '')
                
                # 構建留言數據
                comment_data = {
                    '標題': title,
                    '留言內容': comment_text,
                    '日期': comment_time,
                    'source': platform
                }
                
                # 添加到用戶數據
                if user_id not in processed_users:
                    processed_users[user_id] = {
                        'comments': [],
                        'latest_date': comment_time,
                        'comment_count': 0
                    }
                
                processed_users[user_id]['comments'].append(comment_data)
                processed_users[user_id]['comment_count'] += 1
                
                # 更新最新日期
                if comment_time and (not processed_users[user_id]['latest_date'] or comment_time > processed_users[user_id]['latest_date']):
                    processed_users[user_id]['latest_date'] = comment_time
                    
            except Exception as e:
                logger.warning(f"⚠️ 處理PTT數據項目失敗: {e}")
                continue
        
        return processed_users
    
    def process_threads_data(self, data: List[Dict], platform: str) -> Dict[str, Any]:
        """處理Threads數據為Gemini格式"""
        processed_users = {}
        
        for item in data:
            try:
                # 提取用戶信息
                user_id = item.get('user_id') or item.get('author') or item.get('username', 'unknown')
                if not user_id or user_id == 'unknown':
                    continue
                
                # 提取留言內容
                comment_text = item.get('comment_text') or item.get('text') or item.get('content', '')
                if not comment_text.strip():
                    continue
                
                # 提取標題
                title = item.get('post_title') or item.get('title', '')
                
                # 提取時間
                comment_time = item.get('comment_time') or item.get('time') or item.get('published_time', '')
                
                # 構建留言數據
                comment_data = {
                    '標題': title,
                    '留言內容': comment_text,
                    '日期': comment_time,
                    'source': platform
                }
                
                # 添加到用戶數據
                if user_id not in processed_users:
                    processed_users[user_id] = {
                        'comments': [],
                        'latest_date': comment_time,
                        'comment_count': 0
                    }
                
                processed_users[user_id]['comments'].append(comment_data)
                processed_users[user_id]['comment_count'] += 1
                
                # 更新最新日期
                if comment_time and (not processed_users[user_id]['latest_date'] or comment_time > processed_users[user_id]['latest_date']):
                    processed_users[user_id]['latest_date'] = comment_time
                    
            except Exception as e:
                logger.warning(f"⚠️ 處理Threads數據項目失敗: {e}")
                continue
        
        return processed_users
    
    def process_facebook_data(self, data: List[Dict], platform: str) -> Dict[str, Any]:
        """處理Facebook數據為Gemini格式"""
        processed_users = {}
        
        for item in data:
            try:
                # 提取用戶信息
                user_id = item.get('username') or item.get('user_id') or item.get('author', 'unknown')
                if not user_id or user_id == 'unknown':
                    continue
                
                # 提取留言內容
                comment_text = item.get('content') or item.get('text') or item.get('comment_text', '')
                if not comment_text.strip():
                    continue
                
                # 提取標題（Facebook貼文通常沒有標題）
                title = item.get('title', '')
                
                # 提取時間
                comment_time = item.get('time') or item.get('comment_time') or item.get('published_time', '')
                
                # 構建留言數據
                comment_data = {
                    '標題': title,
                    '留言內容': comment_text,
                    '日期': comment_time,
                    'source': platform
                }
                
                # 添加到用戶數據
                if user_id not in processed_users:
                    processed_users[user_id] = {
                        'comments': [],
                        'latest_date': comment_time,
                        'comment_count': 0
                    }
                
                processed_users[user_id]['comments'].append(comment_data)
                processed_users[user_id]['comment_count'] += 1
                
                # 更新最新日期
                if comment_time and (not processed_users[user_id]['latest_date'] or comment_time > processed_users[user_id]['latest_date']):
                    processed_users[user_id]['latest_date'] = comment_time
                    
            except Exception as e:
                logger.warning(f"⚠️ 處理Facebook數據項目失敗: {e}")
                continue
        
        return processed_users
    
    def merge_user_data(self, user_data_dicts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合併多個用戶數據字典"""
        merged_users = {}
        
        for user_dict in user_data_dicts:
            for user_id, user_info in user_dict.items():
                if user_id not in merged_users:
                    merged_users[user_id] = user_info
                else:
                    # 合併留言
                    merged_users[user_id]['comments'].extend(user_info['comments'])
                    merged_users[user_id]['comment_count'] += user_info['comment_count']
                    
                    # 更新最新日期
                    if user_info['latest_date'] and (not merged_users[user_id]['latest_date'] or user_info['latest_date'] > merged_users[user_id]['latest_date']):
                        merged_users[user_id]['latest_date'] = user_info['latest_date']
        
        return merged_users
    
    def generate_statistics(self, gemini_data: Dict[str, Any], legislator_name: str) -> Dict[str, Any]:
        """生成數據統計報告"""
        total_users = len(gemini_data)
        total_comments = sum(user_info['comment_count'] for user_info in gemini_data.values())
        
        # 平台統計
        platform_stats = defaultdict(int)
        for user_info in gemini_data.values():
            for comment in user_info['comments']:
                platform = comment.get('source', 'unknown')
                platform_stats[platform] += 1
        
        # 日期統計
        date_stats = defaultdict(int)
        for user_info in gemini_data.values():
            latest_date = user_info.get('latest_date', '')
            if latest_date:
                try:
                    # 簡化日期格式
                    date_part = latest_date.split(' ')[0] if ' ' in latest_date else latest_date
                    date_stats[date_part] += 1
                except:
                    pass
        
        statistics = {
            'legislator_name': legislator_name,
            'total_users': total_users,
            'total_comments': total_comments,
            'platform_distribution': dict(platform_stats),
            'date_distribution': dict(date_stats),
            'generated_at': datetime.now().isoformat()
        }
        
        # 保存統計報告
        stats_file = os.path.join(self.user_data_dir, f"{legislator_name}_statistics.json")
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(statistics, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📊 統計報告已生成: {stats_file}")
        return statistics

def integrate_legislator_data(legislator_name: str, base_dir: str = "backend") -> Dict[str, Any]:
    """整合特定立委的所有數據"""
    integrator = DataIntegrator(base_dir)
    
    try:
        # 整合所有平台數據
        integrated_data = integrator.integrate_all_platform_data(legislator_name)
        
        if not integrated_data:
            logger.warning(f"⚠️ {legislator_name} 沒有找到任何平台數據")
            return {}
        
        # 處理為Gemini格式
        gemini_data = integrator.process_to_gemini_format(integrated_data, legislator_name)
        
        # 生成統計報告
        statistics = integrator.generate_statistics(gemini_data, legislator_name)
        
        logger.info(f"✅ {legislator_name} 數據整合完成")
        return gemini_data
        
    except Exception as e:
        logger.error(f"❌ {legislator_name} 數據整合失敗: {e}")
        return {}

if __name__ == "__main__":
    # 測試數據整合
    test_legislator = "測試立委"
    print(f"🧪 測試數據整合: {test_legislator}")
    
    try:
        result = integrate_legislator_data(test_legislator)
        if result:
            print(f"✅ 測試成功，整合了 {len(result)} 位用戶的數據")
        else:
            print("⚠️ 測試完成，但沒有找到數據")
    except Exception as e:
        print(f"❌ 測試失敗: {e}") 