.header {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 70px;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.header-brand {
  .brand-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #333;
    font-weight: bold;
    font-size: 1.5rem;
    
    i {
      margin-right: 0.5rem;
      color: #007bff;
    }
  }
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  
  @media (max-width: 1024px) {
    gap: 0.3rem;
  }
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.5rem 0.75rem;
  text-decoration: none;
  color: #333;
  border-radius: 5px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  white-space: nowrap;
  
  &:hover {
    background: #f8f9fa;
    color: #007bff;
  }
  
  &.active {
    background: #007bff;
    color: white;
  }
  
  i {
    font-size: 0.9rem;
  }
  
  @media (max-width: 1024px) {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
    
    i {
      font-size: 0.8rem;
    }
  }
}

.header-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
  
  @media (max-width: 768px) {
    display: none; // 在移動端隱藏統計
  }
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  
  i {
    color: #007bff;
  }
}

// 響應式設計
@media (max-width: 768px) {
  .header-container {
    padding: 0 15px;
  }
  
  .header-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    justify-content: space-around;
    z-index: 1000;
    
    .nav-link {
      flex-direction: column;
      gap: 0.2rem;
      font-size: 0.7rem;
      padding: 0.3rem 0.5rem;
      
      i {
        font-size: 1rem;
      }
    }
  }
  
  .header {
    height: 60px;
  }
}
