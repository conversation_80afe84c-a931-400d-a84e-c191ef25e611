#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手動數據處理腳本
用於重新處理所有立委的數據，包括：
1. 數據整合 (alldata)
2. 用戶數據處理 (user_data)
3. Gemini分析 (final_data)
4. MongoDB更新
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import List, Dict, Any

# 添加當前目錄到Python路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('manual_processing.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 立委列表
ALL_LEGISLATORS = [
    '劉建國', '歐陽立委', '蔡適應', '賴士葆',
    '黃仁', '黃國書', '黃建豪', '楊瓊瓔', '萬美玲', '葉元之', '廖先翔',
    '賴惠員', '鄭正鈐', '魯明哲', '顏寬恒', '羅廷瑋', '羅明才', '羅智強', '謝衣鳳', '高虹安',
    '丁學忠', '牛煦庭', '王鴻薇', '江啟臣', '呂玉玲', '李彥秀', '林沛祥', '林思銘',
    '林德福', '邱若華', '邱鎮軍', '洪孟楷', '徐巧芯', '徐欣瑩', '馬文君', '張其祿',
    '張嘉郡', '梁文傑', '莊瑞雄', '陳玉珍', '陳建仁', '陳昭姿', '陳椒華', '陳雪生',
    '陳學聖', '曾銘宗', '游毓蘭', '蔡培慧', '廖偉翔'
]

class ManualDataProcessor:
    """手動數據處理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def get_legislators_with_data(self) -> List[str]:
        """獲取有數據的立委列表"""
        legislators_with_data = []
        
        # 檢查YouTube數據
        youtube_data_dir = "crawler/data/youtube"
        if os.path.exists(youtube_data_dir):
            for file in os.listdir(youtube_data_dir):
                if file.endswith('.json'):
                    name = file.replace('.json', '')
                    if name not in legislators_with_data:
                        legislators_with_data.append(name)
        
        # 檢查PTT數據
        ptt_data_dir = "crawler/data/ptt"
        if os.path.exists(ptt_data_dir):
            for file in os.listdir(ptt_data_dir):
                if file.endswith('.json'):
                    name = file.replace('.json', '')
                    if name not in legislators_with_data:
                        legislators_with_data.append(name)
        
        return legislators_with_data
    
    def process_all_data(self, legislators: List[str] = None) -> Dict[str, Any]:
        """處理所有數據"""
        if not legislators:
            legislators = self.get_legislators_with_data()
        
        if not legislators:
            self.logger.error("❌ 沒有找到任何立委數據")
            return {'success': False, 'error': 'No data found'}
        
        self.logger.info(f"🚀 開始處理 {len(legislators)} 位立委的數據")
        self.logger.info(f"📋 立委列表: {legislators}")
        
        results = {
            'success': True,
            'total_legislators': len(legislators),
            'processed_legislators': 0,
            'failed_legislators': [],
            'total_records': 0
        }
        
        # 階段1: 數據整合
        self.logger.info("📊 階段1: 數據整合...")
        #integration_results = self.integrate_all_data(legislators)
        #if not integration_results['success']:
        #    self.logger.error("❌ 數據整合失敗")
        #    return integration_results
        
        #results['total_records'] = integration_results.get('total_records', 0)
        
        # 階段2: 用戶數據處理
        self.logger.info("👥 階段2: 用戶數據處理...")
        #user_results = self.process_user_data(legislators)
        #if not user_results['success']:
        #    self.logger.warning("⚠️ 用戶數據處理部分失敗")
        
        # 階段3: Gemini分析
        self.logger.info("🤖 階段3: Gemini分析...")
        #gemini_results = self.process_gemini_analysis(legislators)
        #if not gemini_results['success']:
        #    self.logger.warning("⚠️ Gemini分析部分失敗")
        
        # 階段4: MongoDB更新
        self.logger.info("💾 階段4: MongoDB更新...")
        mongo_results = self.update_mongodb(legislators)
        if not mongo_results['success']:
            self.logger.warning("⚠️ MongoDB更新部分失敗")
        
        results['processed_legislators'] = len(legislators) - len(results['failed_legislators'])
        
        self.logger.info(f"🎉 數據處理完成!")
        self.logger.info(f"   ✅ 成功處理: {results['processed_legislators']} 位立委")
        self.logger.info(f"   ❌ 失敗: {len(results['failed_legislators'])} 位立委")
        self.logger.info(f"   📊 總記錄數: {results['total_records']}")
        
        return results
    
    def integrate_all_data(self, legislators: List[str]) -> Dict[str, Any]:
        """整合所有數據"""
        try:
            from crawler.data_integrator import DataIntegrator
            integrator = DataIntegrator()
            
            platforms = ['ptt', 'youtube']
            result = integrator.integrate_all_legislators(legislators, platforms)
            
            if result.get('success'):
                self.logger.info(f"✅ 數據整合完成: {result.get('total_records', 0)} 條記錄")
            else:
                self.logger.error(f"❌ 數據整合失敗: {result.get('error', 'Unknown error')}")
            
            return result
            
        except ImportError as e:
            self.logger.error(f"❌ 無法導入DataIntegrator: {e}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            self.logger.error(f"❌ 數據整合異常: {e}")
            return {'success': False, 'error': str(e)}
    
    def process_user_data(self, legislators: List[str]) -> Dict[str, Any]:
        """處理用戶數據"""
        try:
            from crawler.user_data_processor import process_legislators_data
            
            success = process_legislators_data(
                specific_legislators=legislators,
                force_reprocess=True,
                quiet=False
            )
            
            if success:
                self.logger.info("✅ 用戶數據處理完成")
            else:
                self.logger.error("❌ 用戶數據處理失敗")
            
            return {'success': success}
            
        except ImportError as e:
            self.logger.error(f"❌ 無法導入user_data_processor: {e}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            self.logger.error(f"❌ 用戶數據處理異常: {e}")
            return {'success': False, 'error': str(e)}
    
    def process_gemini_analysis(self, legislators: List[str]) -> Dict[str, Any]:
        """處理Gemini分析"""
        try:
            from crawler.gemini_emo_user import analyze_legislators_emotions
            
            result = analyze_legislators_emotions(legislators)
            
            if result.get('success'):
                self.logger.info("✅ Gemini分析完成")
            else:
                self.logger.error(f"❌ Gemini分析失敗: {result.get('error', 'Unknown error')}")
            
            return result
            
        except ImportError as e:
            self.logger.error(f"❌ 無法導入gemini_emo_user: {e}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            self.logger.error(f"❌ Gemini分析異常: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_mongodb(self, legislators: List[str]) -> Dict[str, Any]:
        """更新MongoDB"""
        try:
            from crawler.data_to_mongo_v2 import DataToMongo
            
            mongo_updater = DataToMongo()
            result = mongo_updater.process_legislators(legislators)
            
            if result.get('success'):
                self.logger.info("✅ MongoDB更新完成")
            else:
                self.logger.error(f"❌ MongoDB更新失敗: {result.get('error', 'Unknown error')}")
            
            return result
            
        except ImportError as e:
            self.logger.error(f"❌ 無法導入data_to_mongo_v2: {e}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            self.logger.error(f"❌ MongoDB更新異常: {e}")
            return {'success': False, 'error': str(e)}

def main():
    """主函數"""
    logger.info("=== 手動數據處理腳本啟動 ===")
    
    processor = ManualDataProcessor()
    
    # 獲取有數據的立委
    legislators = processor.get_legislators_with_data()
    
    if not legislators:
        logger.error("❌ 沒有找到任何立委數據")
        return
    
    logger.info(f"📊 找到 {len(legislators)} 位立委有數據")
    logger.info(f"👥 立委列表: {legislators}")
    
    # 處理所有數據
    result = processor.process_all_data(legislators)
    
    if result.get('success'):
        logger.info("🎉 所有數據處理完成!")
    else:
        logger.error(f"❌ 數據處理失敗: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main() 