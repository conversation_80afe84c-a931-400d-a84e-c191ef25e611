.civic-issues-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 4rem 2rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.development-notice {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  text-align: center;
  width: 100%;
}

.notice-content {
  .notice-icon {
    margin-bottom: 2rem;
    
    i {
      font-size: 4rem;
      color: #007bff;
    }
  }
  
  h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 0.5rem;
  }
  
  h2 {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
  }
  
  .notice-text {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
  }
}

.feature-description {
  text-align: left;
  margin: 2rem 0;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 10px;
  
  h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
  }
  
  ul {
    list-style: none;
    padding: 0;
    
    li {
      padding: 0.5rem 0;
      color: #555;
      position: relative;
      padding-left: 1.5rem;
      
      &:before {
        content: "•";
        color: #007bff;
        font-weight: bold;
        position: absolute;
        left: 0;
      }
    }
  }
}

.back-button {
  margin-top: 2rem;
  
  .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      background: #0056b3;
      transform: translateY(-2px);
    }
  }
}
