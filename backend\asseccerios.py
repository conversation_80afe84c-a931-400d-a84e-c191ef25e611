import os
from pymongo import MongoClient
from config import get_mongo_config
from datetime import datetime

# 初始化 MongoDB 連接
client = None
db = None

def init_mongodb():
    """初始化 MongoDB 連接 - 支援 Atlas 雲端和本地環境"""
    global client, db
    try:
        # 獲取配置
        mongo_conf = get_mongo_config()
        # 驗證配置
        if not mongo_conf['MONGODB_URI']:
            raise ValueError("MONGODB_URI is not configured")
        if not mongo_conf['MONGODB_DBNAME']:
            raise ValueError("MONGODB_DBNAME is not configured")
        
        # 建立連接 (使用優化參數)
        client = MongoClient(
            mongo_conf['MONGODB_URI'], 
            **mongo_conf.get('MONGODB_OPTIONS', {})
        )
        db = client[mongo_conf['MONGODB_DBNAME']]
        
        # 測試連接
        client.admin.command('ping')
        
        # 檢查是否為 Atlas 連接
        uri = mongo_conf['MONGODB_URI']
        if uri.startswith('mongodb+srv://'):
            print(f"✅ 成功連接到 MongoDB Atlas")
        else:
            print(f"✅ 成功連接到 MongoDB: {mongo_conf['MONGODB_URI']}")
        return True
        
    except Exception as e:
        print(f"❌ MongoDB 連接錯誤: {e}")
        # 只有在開發環境才使用 fallback
        env = os.getenv('FLASK_ENV', 'development')
        if env == 'development':
            try:
                fallback_uri = 'mongodb://localhost:27017'
                fallback_db = 'legislator_recall'
                # 使用基礎連線參數
                fallback_options = {
                    'connectTimeoutMS': 30000,
                    'socketTimeoutMS': 45000,
                    'serverSelectionTimeoutMS': 30000,
                    'retryWrites': True
                }
                client = MongoClient(fallback_uri, **fallback_options)
                db = client[fallback_db]
                client.admin.command('ping')
                print(f"✅ 已切換到本地備用連接: {fallback_uri}")
                return True
            except Exception as fallback_error:
                print(f"❌ 本地備用連接也失敗: {fallback_error}")
                print("💡 請確保本地 MongoDB 服務正在運行")
                client = None
                db = None
                return False
        else:
            # 在生產環境不使用 fallback，直接返回失敗
            print("❌ 生產環境：必須設置正確的 MONGODB_ATLAS_URI 或 MONGODB_URI")
            client = None
            db = None
            return False

# 初始化連接
init_mongodb()

# 1. 查詢某立委所有留言/新聞
def get_crawler_data(legislator_name):
    if db is None:
        return []
    try:
        crawler_col = db['crawler_data']
        return list(crawler_col.find({'name': legislator_name}))
    except Exception as e:
        print(f"Error getting legislator data for {legislator_name}: {e}")
        return []

# 2. 寫入分析結果到 legislators
def update_legislator_analysis(legislator_name, analysis_results):
    if db is None:
        return False
    try:
        legislators_col = db['legislators']
        result = legislators_col.update_one(
            {"name": legislator_name},
            {"$set": analysis_results},
            upsert=True
        )
        return result.acknowledged
    except Exception as e:
        print(f"Error updating legislator analysis for {legislator_name}: {e}")
        return False

# 3. 根據姓名查詢立委
def get_legislator_by_name(name):
    if db is None:
        return None
    try:
        legislators_col = db['legislators']
        return legislators_col.find_one({"name": name})
    except Exception as e:
        print(f"Error getting legislator by name {name}: {e}")
        return None

# 4. 獲取所有立委資料
def get_all_legislators():
    if db is None:
        return []
    try:
        legislators_col = db['legislators']
        return list(legislators_col.find())
    except Exception as e:
        print(f"Error getting all legislators: {e}")
        return []

# 5. 獲取立委的留言情緒分析結果
def get_legislator_sentiment(legislator_name):
    if db is None:
        return None
    try:
        legislator = get_legislator_by_name(legislator_name)
        if legislator and 'sentiment_analysis' in legislator:
            return legislator['sentiment_analysis']
        return None
    except Exception as e:
        print(f"Error getting sentiment for {legislator_name}: {e}")
        return None

# 6. 記錄資料分析任務
def log_analysis_task(legislator_name, task_type, status="started", details=None):
    if db is None:
        return False
    try:
        tasks_col = db['analysis_tasks']
        task = {
            "legislator_name": legislator_name,
            "task_type": task_type,
            "status": status,
            "timestamp": datetime.now(),
            "details": details or {}
        }
        result = tasks_col.insert_one(task)
        return result.acknowledged
    except Exception as e:
        print(f"Error logging analysis task: {e}")
        return False

# 7. 更新資料分析任務狀態
def update_analysis_task_status(task_id, status, details=None):
    if db is None:
        return False
    try:
        tasks_col = db['analysis_tasks']
        update_data = {
            "status": status,
            "updated_at": datetime.now()
        }
        if details:
            update_data["details"] = details
            
        result = tasks_col.update_one(
            {"_id": task_id},
            {"$set": update_data}
        )
        return result.acknowledged
    except Exception as e:
        print(f"Error updating analysis task: {e}")
        return False

# 8. 查詢MongoDB連接狀態
def get_mongodb_status():
    if client is None or db is None:
        return {
            "status": "disconnected",
            "message": "尚未連接到MongoDB"
        }
    
    try:
        # 測試連接
        client.admin.command('ping')
        config = get_mongo_config()
        return {
            "status": "connected",
            "message": "MongoDB連接正常",
            "uri": config['MONGODB_URI'],
            "database": config['MONGODB_DBNAME']
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"MongoDB連接出錯: {str(e)}"
        }
