import {
  Chart,
  merge_default,
  registerables
} from "./chunk-WTQCERWO.js";
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  NgModule,
  <PERSON><PERSON><PERSON>,
  Renderer2,
  afterRenderEffect,
  booleanAttribute,
  computed,
  inject,
  input,
  linkedSignal,
  numberAttribute,
  output,
  setClassMetadata,
  untracked,
  viewChild,
  ɵɵNgOnChangesFeature,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵqueryAdvance,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵstyleProp,
  ɵɵviewQuerySignal
} from "./chunk-LHK6QOT4.js";
import "./chunk-WPM5VTLQ.js";
import "./chunk-PEBH6BBU.js";
import "./chunk-4S3KYZTJ.js";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-XWLXMCJQ.js";

// node_modules/@coreui/chartjs/dist/js/coreui-chartjs.esm.js
var ClassName = {
  TOOLTIP: "chartjs-tooltip",
  TOOLTIP_BODY: "chartjs-tooltip-body",
  TOOLTIP_BODY_ITEM: "chartjs-tooltip-body-item",
  TOOLTIP_HEADER: "chartjs-tooltip-header",
  TOOLTIP_HEADER_ITEM: "chartjs-tooltip-header-item"
};
var getOrCreateTooltip = (chart) => {
  let tooltipEl = chart.canvas.parentNode.querySelector("div");
  if (!tooltipEl) {
    tooltipEl = document.createElement("div");
    tooltipEl.classList.add(ClassName.TOOLTIP);
    const table = document.createElement("table");
    table.style.margin = "0px";
    tooltipEl.append(table);
    chart.canvas.parentNode.append(tooltipEl);
  }
  return tooltipEl;
};
var customTooltips = (context) => {
  const {
    chart,
    tooltip
  } = context;
  const tooltipEl = getOrCreateTooltip(chart);
  if (tooltip.opacity === 0) {
    tooltipEl.style.opacity = 0;
    return;
  }
  if (tooltip.body) {
    const titleLines = tooltip.title || [];
    const bodyLines = tooltip.body.map((b) => b.lines);
    const tableHead = document.createElement("thead");
    tableHead.classList.add(ClassName.TOOLTIP_HEADER);
    for (const title of titleLines) {
      const tr = document.createElement("tr");
      tr.style.borderWidth = 0;
      tr.classList.add(ClassName.TOOLTIP_HEADER_ITEM);
      const th = document.createElement("th");
      th.style.borderWidth = 0;
      const text = document.createTextNode(title);
      th.append(text);
      tr.append(th);
      tableHead.append(tr);
    }
    const tableBody = document.createElement("tbody");
    tableBody.classList.add(ClassName.TOOLTIP_BODY);
    for (const [i, body] of bodyLines.entries()) {
      const colors = tooltip.labelColors[i];
      const span = document.createElement("span");
      span.style.background = colors.backgroundColor;
      span.style.borderColor = colors.borderColor;
      span.style.borderWidth = "2px";
      span.style.marginRight = "10px";
      span.style.height = "10px";
      span.style.width = "10px";
      span.style.display = "inline-block";
      const tr = document.createElement("tr");
      tr.classList.add(ClassName.TOOLTIP_BODY_ITEM);
      const td = document.createElement("td");
      td.style.borderWidth = 0;
      const text = document.createTextNode(body);
      td.append(span);
      td.append(text);
      tr.append(td);
      tableBody.append(tr);
    }
    const tableRoot = tooltipEl.querySelector("table");
    while (tableRoot.firstChild) {
      tableRoot.firstChild.remove();
    }
    tableRoot.append(tableHead);
    tableRoot.append(tableBody);
  }
  const {
    offsetLeft: positionX,
    offsetTop: positionY
  } = chart.canvas;
  tooltipEl.style.opacity = 1;
  tooltipEl.style.left = `${positionX + tooltip.caretX}px`;
  tooltipEl.style.top = `${positionY + tooltip.caretY}px`;
  tooltipEl.style.font = tooltip.options.bodyFont.string;
  tooltipEl.style.padding = `${tooltip.padding}px ${tooltip.padding}px`;
};

// node_modules/@coreui/angular-chartjs/fesm2022/coreui-angular-chartjs.mjs
var _c0 = ["canvasElement"];
var _c1 = ["*"];
Chart.register(...registerables);
var nextId = 0;
var ChartjsComponent = class _ChartjsComponent {
  get id() {
    return this.idInput();
  }
  constructor() {
    this.ngZone = inject(NgZone);
    this.renderer = inject(Renderer2);
    this.changeDetectorRef = inject(ChangeDetectorRef);
    this.customTooltips = input(true, {
      transform: booleanAttribute
    });
    this.data = input();
    this.height = input(null, {
      transform: (value) => numberAttribute(value, void 0)
    });
    this.idInput = input(`c-chartjs-${nextId++}`, {
      alias: "id"
    });
    this.optionsInput = input({}, {
      alias: "options"
    });
    this.options = linkedSignal(this.optionsInput);
    this.plugins = input([]);
    this.redraw = input(false, {
      transform: booleanAttribute
    });
    this.type = input("bar");
    this.width = input(null, {
      transform: (value) => numberAttribute(value, void 0)
    });
    this.wrapper = input(true, {
      transform: booleanAttribute
    });
    this.getDatasetAtEvent = output();
    this.getElementAtEvent = output();
    this.getElementsAtEvent = output();
    this.chartRef = output();
    this.canvasElement = viewChild.required("canvasElement");
    this.hostClasses = computed(() => {
      return {
        "chart-wrapper": this.wrapper()
      };
    });
    this.chartDataConfig = computed(() => {
      const {
        labels,
        datasets
      } = __spreadValues({}, this.data());
      return {
        labels: labels ?? [],
        datasets: datasets ?? []
      };
    });
    this.chartOptions = computed(() => this.options() ?? {});
    this.chartConfig = computed(() => {
      this.chartCustomTooltips();
      return {
        data: this.chartDataConfig(),
        options: this.chartOptions(),
        plugins: this.plugins(),
        type: this.type()
      };
    });
    afterRenderEffect({
      read: () => {
        const canvasElement = this.canvasElement();
        this.ctx = canvasElement?.nativeElement?.getContext("2d");
        this.chartRender();
      }
    });
  }
  ngOnChanges(changes) {
    if (changes["data"] && !changes["data"].firstChange) {
      this.chartUpdate();
    }
  }
  ngOnDestroy() {
    this.chartDestroy();
  }
  handleClick($event) {
    if (!this.chart) {
      return;
    }
    const datasetAtEvent = this.chart.getElementsAtEventForMode($event, "dataset", {
      intersect: true
    }, false);
    this.getDatasetAtEvent.emit(datasetAtEvent);
    const elementAtEvent = this.chart.getElementsAtEventForMode($event, "nearest", {
      intersect: true
    }, false);
    this.getElementAtEvent.emit(elementAtEvent);
    const elementsAtEvent = this.chart.getElementsAtEventForMode($event, "index", {
      intersect: true
    }, false);
    this.getElementsAtEvent.emit(elementsAtEvent);
  }
  chartDestroy() {
    this.chart?.destroy();
    this.chartRef.emit(void 0);
  }
  chartRender() {
    const canvasElement = this.canvasElement();
    if (!canvasElement?.nativeElement || !this.ctx || this.chart) {
      return;
    }
    this.ngZone.runOutsideAngular(() => {
      const config = this.chartConfig();
      if (config) {
        this.chart = new Chart(this.ctx, config);
        this.ngZone.run(() => {
          this.renderer.setStyle(canvasElement.nativeElement, "display", "block");
          this.changeDetectorRef.markForCheck();
          this.chartRef.emit(this.chart);
        });
      }
    });
  }
  chartUpdate() {
    if (!this.chart) {
      return;
    }
    if (this.redraw()) {
      this.chartDestroy();
      this.chartRender();
      return;
    }
    const config = this.chartConfig();
    if (this.options()) {
      Object.assign(this.chart.options ?? {}, config.options ?? {});
    }
    if (!this.chart.config.data) {
      this.chart.config.data = __spreadValues({}, config.data);
      this.chartUpdateOutsideAngular();
    }
    if (this.chart) {
      Object.assign(this.chart.config.options ?? {}, config.options ?? {});
      Object.assign(this.chart.config.plugins ?? [], config.plugins ?? []);
      Object.assign(this.chart.config.data, config.data);
    }
    this.chartUpdateOutsideAngular();
  }
  chartUpdateOutsideAngular() {
    setTimeout(() => {
      this.ngZone.runOutsideAngular(() => {
        this.chart?.update();
        this.ngZone.run(() => {
          this.changeDetectorRef.markForCheck();
        });
      });
    });
  }
  chartToBase64Image() {
    return this.chart?.toBase64Image();
  }
  chartCustomTooltips() {
    if (this.customTooltips()) {
      const options = this.options();
      const plugins = options?.plugins;
      const tooltip = options?.plugins?.tooltip;
      untracked(() => {
        this.options.set(merge_default(__spreadProps(__spreadValues({}, options), {
          plugins: __spreadProps(__spreadValues({}, plugins), {
            tooltip: __spreadProps(__spreadValues({}, tooltip), {
              enabled: false,
              mode: "index",
              position: "nearest",
              external: customTooltips
            })
          })
        })));
      });
    }
  }
  static {
    this.ɵfac = function ChartjsComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ChartjsComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _ChartjsComponent,
      selectors: [["c-chart"]],
      viewQuery: function ChartjsComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuerySignal(ctx.canvasElement, _c0, 5);
        }
        if (rf & 2) {
          ɵɵqueryAdvance();
        }
      },
      hostVars: 6,
      hostBindings: function ChartjsComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassMap(ctx.hostClasses());
          ɵɵstyleProp("height", ctx.height(), "px")("width", ctx.width(), "px");
        }
      },
      inputs: {
        customTooltips: [1, "customTooltips"],
        data: [1, "data"],
        height: [1, "height"],
        idInput: [1, "id", "idInput"],
        optionsInput: [1, "options", "optionsInput"],
        plugins: [1, "plugins"],
        redraw: [1, "redraw"],
        type: [1, "type"],
        width: [1, "width"],
        wrapper: [1, "wrapper"]
      },
      outputs: {
        getDatasetAtEvent: "getDatasetAtEvent",
        getElementAtEvent: "getElementAtEvent",
        getElementsAtEvent: "getElementsAtEvent",
        chartRef: "chartRef"
      },
      exportAs: ["cChart"],
      features: [ɵɵNgOnChangesFeature],
      ngContentSelectors: _c1,
      decls: 3,
      vars: 3,
      consts: [["canvasElement", ""], ["role", "img", 2, "display", "none", 3, "click", "height", "id", "width"]],
      template: function ChartjsComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r1 = ɵɵgetCurrentView();
          ɵɵprojectionDef();
          ɵɵelementStart(0, "canvas", 1, 0);
          ɵɵlistener("click", function ChartjsComponent_Template_canvas_click_0_listener($event) {
            ɵɵrestoreView(_r1);
            return ɵɵresetView(ctx.handleClick($event));
          });
          ɵɵprojection(2);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵproperty("height", ctx.height() || null)("id", ctx.id)("width", ctx.width() || null);
        }
      },
      styles: [".chart-wrapper[_nghost-%COMP%]{display:block}"],
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ChartjsComponent, [{
    type: Component,
    args: [{
      selector: "c-chart",
      exportAs: "cChart",
      changeDetection: ChangeDetectionStrategy.OnPush,
      host: {
        "[class]": "hostClasses()",
        "[style.height.px]": "height()",
        "[style.width.px]": "width()"
      },
      template: '<canvas\n  #canvasElement\n  (click)="handleClick($event)"\n  [height]="height() || null"\n  [id]="id"\n  [width]="width() || null"\n  role="img"\n  style="display: none;"\n>\n  <ng-content />\n  <!--  <ng-container *ngTemplateOutlet="fallbackContent"/>-->\n</canvas>\n',
      styles: [":host.chart-wrapper{display:block}\n"]
    }]
  }], () => [], null);
})();
var ChartjsModule = class _ChartjsModule {
  static {
    this.ɵfac = function ChartjsModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ChartjsModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _ChartjsModule,
      imports: [ChartjsComponent],
      exports: [ChartjsComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ChartjsModule, [{
    type: NgModule,
    args: [{
      imports: [ChartjsComponent],
      exports: [ChartjsComponent]
    }]
  }], null, null);
})();
export {
  ChartjsComponent,
  ChartjsModule
};
/*! Bundled license information:

@coreui/chartjs/dist/js/coreui-chartjs.esm.js:
  (*!
    * CoreUI v4.0.0 (https://coreui.io)
    * Copyright 2024 [object Object]
    * Licensed under MIT (https://github.com/coreui/coreui-chartjs/blob/main/LICENSE)
    *)
*/
//# sourceMappingURL=@coreui_angular-chartjs.js.map
