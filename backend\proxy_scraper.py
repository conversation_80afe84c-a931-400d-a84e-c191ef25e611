#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Scrapy的代理爬取器
参考: https://ithelp.ithome.com.tw/articles/10201288
"""

import requests
import json
import time
from bs4 import BeautifulSoup
from typing import List, Dict

class ProxyScraper:
    """代理爬取器 - 基于免费代理网站"""
    
    def __init__(self):
        self.proxy_sources = [
            {
                'name': 'US Proxy List',
                'url': 'https://www.us-proxy.org/',
                'parser': self.parse_us_proxy
            },
            {
                'name': 'Free Proxy List',
                'url': 'https://free-proxy-list.net/',
                'parser': self.parse_free_proxy
            },
            {
                'name': 'Proxy List',
                'url': 'https://www.proxy-list.download/api/v1/get?type=https',
                'parser': self.parse_proxy_list
            }
        ]
    
    def parse_us_proxy(self, response_text: str) -> List[Dict]:
        """解析US Proxy List网站"""
        proxies = []
        try:
            soup = BeautifulSoup(response_text, 'html.parser')
            trs = soup.select("#proxylisttable tr")
            
            for tr in trs:
                tds = tr.select("td")
                if len(tds) > 6:
                    ip = tds[0].text.strip()
                    port = tds[1].text.strip()
                    anonymity = tds[4].text.strip()
                    https_support = tds[6].text.strip()
                    
                    if https_support == 'yes':
                        scheme = 'https'
                    else:
                        scheme = 'http'
                    
                    proxy = f"{scheme}://{ip}:{port}"
                    
                    proxies.append({
                        'ip': ip,
                        'port': port,
                        'scheme': scheme,
                        'proxy': proxy,
                        'anonymity': anonymity,
                        'source': 'US Proxy List'
                    })
                    
        except Exception as e:
            print(f"❌ 解析US Proxy List失败: {e}")
        
        return proxies
    
    def parse_free_proxy(self, response_text: str) -> List[Dict]:
        """解析Free Proxy List网站"""
        proxies = []
        try:
            soup = BeautifulSoup(response_text, 'html.parser')
            trs = soup.select("table.table tbody tr")
            
            for tr in trs:
                tds = tr.select("td")
                if len(tds) >= 8:
                    ip = tds[0].text.strip()
                    port = tds[1].text.strip()
                    https_support = tds[6].text.strip()
                    anonymity = tds[4].text.strip()
                    
                    if https_support == 'yes':
                        scheme = 'https'
                    else:
                        scheme = 'http'
                    
                    proxy = f"{scheme}://{ip}:{port}"
                    
                    proxies.append({
                        'ip': ip,
                        'port': port,
                        'scheme': scheme,
                        'proxy': proxy,
                        'anonymity': anonymity,
                        'source': 'Free Proxy List'
                    })
                    
        except Exception as e:
            print(f"❌ 解析Free Proxy List失败: {e}")
        
        return proxies
    
    def parse_proxy_list(self, response_text: str) -> List[Dict]:
        """解析Proxy List API"""
        proxies = []
        try:
            lines = response_text.strip().split('\n')
            for line in lines:
                if ':' in line:
                    ip, port = line.split(':')[:2]
                    proxy = f"https://{ip}:{port}"
                    
                    proxies.append({
                        'ip': ip,
                        'port': port,
                        'scheme': 'https',
                        'proxy': proxy,
                        'anonymity': 'unknown',
                        'source': 'Proxy List API'
                    })
                    
        except Exception as e:
            print(f"❌ 解析Proxy List API失败: {e}")
        
        return proxies
    
    def test_proxy(self, proxy_dict: Dict) -> bool:
        """测试代理是否可用 - 优化版"""
        try:
            proxy_url = proxy_dict['proxy']
            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            
            # 减少超时时间：5秒 -> 3秒
            response = requests.get(
                'https://httpbin.org/ip',
                proxies=proxies,
                timeout=3  # 快速失败
            )
            
            if response.status_code == 200:
                data = response.json()
                origin_ip = data.get('origin', '')
                
                # 检查是否是匿名代理
                if origin_ip != proxy_dict['ip']:
                    print(f"✅ 匿名代理可用: {proxy_url} -> {origin_ip}")
                    return True
                else:
                    print(f"⚠️ 非匿名代理: {proxy_url}")
                    return False
            else:
                return False
                
        except Exception as e:
            # 简化错误输出，减少日志噪音
            error_type = type(e).__name__
            print(f"❌ 代理测试失败 {proxy_dict['proxy']}: {error_type}")
            return False
    
    def scrape_proxies(self) -> List[Dict]:
        """爬取所有代理源 - 优化版"""
        all_proxies = []
        
        for source in self.proxy_sources:
            try:
                print(f"🔄 从 {source['name']} 获取代理...")
                response = requests.get(source['url'], timeout=10)
                
                if response.status_code == 200:
                    proxies = source['parser'](response.text)
                    print(f"✅ 从 {source['name']} 获取到 {len(proxies)} 个代理")
                    all_proxies.extend(proxies)
                else:
                    print(f"❌ 获取 {source['name']} 失败: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 获取 {source['name']} 失败: {e}")
        
        return all_proxies
    
    def get_working_proxies(self, max_proxies: int = 20) -> List[Dict]:
        """获取可用的代理 - 优化版"""
        print("🚀 开始爬取和测试代理...")
        
        # 爬取代理
        all_proxies = self.scrape_proxies()
        print(f"📊 总共获取到 {len(all_proxies)} 个代理")
        
        # 🚨 优化：限制测试数量，避免等待太久
        max_test_count = min(50, len(all_proxies))  # 最多测试50个代理
        test_proxies = all_proxies[:max_test_count]
        
        print(f"🎯 限制测试数量为 {max_test_count} 个代理（原总数: {len(all_proxies)}）")
        
        # 测试代理
        working_proxies = []
        consecutive_failures = 0  # 连续失败计数
        
        for i, proxy in enumerate(test_proxies):
            # 🚨 早期终止：如果找到足够的代理或连续失败太多次
            if len(working_proxies) >= max_proxies:
                print(f"✅ 已找到足够的代理 ({max_proxies} 个)，停止测试")
                break
                
            if consecutive_failures >= 20:  # 连续失败20次就停止
                print(f"⚠️ 连续失败 {consecutive_failures} 次，停止测试")
                break
                
            print(f"🧪 测试代理 {i+1}/{max_test_count}: {proxy['proxy']}")
            if self.test_proxy(proxy):
                working_proxies.append(proxy)
                print(f"✅ 代理可用: {proxy['proxy']}")
                consecutive_failures = 0  # 重置失败计数
            else:
                print(f"❌ 代理不可用: {proxy['proxy']}")
                consecutive_failures += 1
            
            # 减少等待时间：0.5秒 -> 0.2秒
            time.sleep(0.2)  # 避免请求过快
        
        print(f"🎉 测试完成！找到 {len(working_proxies)} 个可用代理")
        
        # 🚨 如果找不到代理，给出明确提示
        if len(working_proxies) == 0:
            print("⚠️ 未找到可用代理，系统将自动使用直连模式")
            print("💡 建议:")
            print("   1. 检查网络连接")
            print("   2. 使用 --use-proxy=false 禁用代理")
            print("   3. 或者不使用 --use-proxy 参数，让系统自动处理")
        
        return working_proxies

def main():
    """主函数"""
    scraper = ProxyScraper()
    working_proxies = scraper.get_working_proxies(max_proxies=10)
    
    if working_proxies:
        print("\n📋 可用代理列表:")
        for i, proxy in enumerate(working_proxies, 1):
            print(f"{i}. {proxy['proxy']} ({proxy['source']})")
    else:
        print("❌ 没有找到可用的代理")

if __name__ == "__main__":
    main() 