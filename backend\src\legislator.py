from flask import Blueprint, jsonify, request, abort
import os
from collections import Counter
import jieba
from asseccerios import get_legislator_by_name, get_crawler_data, db
from datetime import datetime
from bson.objectid import ObjectId
from urllib.parse import urlparse, parse_qs
import re
from datetime import datetime, timedelta
import time
from functools import lru_cache
import multiprocessing
from multiprocessing import Pool
import math
import sys
import os

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)


# 創建一個 Flask 藍圖，命名為 'legislator_app'
legislator_app = Blueprint('legislator_app', __name__)

# 全局變量，用於緩存停用詞和jieba設置
_stopwords_cache = None
_jieba_initialized = False

def load_stopwords():
    """載入停用詞文件，使用緩存提升效率"""
    global _stopwords_cache
    if _stopwords_cache is not None:
        return _stopwords_cache

    stopwords_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'stopwords.txt')
    stopwords = set()

    try:
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):  # 忽略空行和註釋
                    stopwords.add(line)
    except FileNotFoundError:
        print(f"⚠️ 停用詞文件未找到: {stopwords_file}")
        # 使用基本停用詞作為後備
        stopwords = {'的', '了', '是', '我', '也', '和', '就', '都', '不', '在', '會', '要', '很'}
    except Exception as e:
        print(f"❌ 載入停用詞失敗: {e}")
        stopwords = set()

    _stopwords_cache = stopwords
    return stopwords

def initialize_jieba():
    """初始化jieba，只執行一次"""
    global _jieba_initialized
    if _jieba_initialized:
        return

    try:
        # 載入自定義詞典
        user_dict_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'user_dict.txt')
        if os.path.exists(user_dict_file):
            jieba.load_userdict(user_dict_file)
        else:
            print(f"⚠️ 自定義詞典未找到: {user_dict_file}")

        # 設置jieba為精確模式
        try:
            import platform
            if platform.system() != 'Windows':
                jieba.enable_parallel(4)  # 只在非Windows系統啟用並行
        except Exception as e:
            print(f"⚠️ Jieba並行模式啟用失敗: {e}")
    except Exception as e:
        print(f"❌ Jieba初始化失敗: {e}")

    _jieba_initialized = True

def extract_meaningful_keywords(text_content, min_count=2):
    """
    極速版關鍵字提取函數，使用基本jieba分詞以提高速度
    省略詞性標註以加快處理速度
    """
    if not text_content:
        return []

    # 全局常量和配置，避免重複創建
    global _stopwords_cache
    if _stopwords_cache is None:
        _stopwords_cache = load_stopwords()
    stopwords = _stopwords_cache

    # 快速分詞模式：使用基本jieba分詞而非詞性標註
    # 初始化jieba（如果尚未初始化）
    initialize_jieba()
    
    # 清理文本：僅保留必要字符並縮短文本
    # 將文本截斷為前1000個字符，這對詞雲足夠了
    if len(text_content) > 1000:
        text_content = text_content[:1000]
    
    # 簡化的正則表達式清理
    cleaned_text = re.sub(r'[^\u4e00-\u9fff\w\s]', ' ', text_content)
    
    # 使用標準jieba而非詞性標註
    words = jieba.cut(cleaned_text, cut_all=False)
    
    # 預先編譯常用正則表達式以加速處理
    english_re = re.compile(r'^[a-zA-Z]+$')
    
    # 常見無用詞，包含常見人名姓氏
    common_excluded = {'不是', '不要', '不到', '不出', '案不', '王', '李', '張', '劉', '陳', '楊', '黃', '趙', '周', '吳', '林', '郭', '何', '高', '羅', '鄭', '梁', '謝', '宋', '唐', '許', '韓', '馮', '鄧', '曹', '彭', '曾', '蕭', '田', '董', '袁', '潘', '蔣', '蔡', '余', '杜', '葉', '程', '魏', '蘇', '呂', '丁', '任', '沈', '姚', '盧', '姜', '崔', '鐘', '譚', '陸', '汪', '范', '金', '石', '廖', '賈', '夏', '韋', '方', '白', '鄒', '孟', '熊', '秦', '邱', '江', '尹', '薛', '閻', '段', '雷', '侯', '龍', '史', '陶', '黎', '賀', '顧', '毛', '郝', '龔', '邵', '萬', '錢', '嚴', '覃', '武', '戴', '莫', '孔', '向', '湯'}
    
    # 簡化的過濾邏輯：減少條件檢查
    word_counter = Counter()
    for word in words:
        word = word.strip()
        
        # 合併多條件檢查為一條
        if (len(word) < 2 or word in stopwords or word in common_excluded or 
            word.isdigit() or (english_re.match(word) and len(word) < 3)):
            continue
            
        word_counter[word] += 1
    
    # 直接返回最高頻的詞，使用更高的閾值以減少結果集
    return word_counter.most_common(100)

# 全局變量，用於追踪索引是否已經初始化
_indexes_initialized = False

def ensure_database_indexes(is_server_startup=False):
    global _indexes_initialized
    
    # 如果索引已經初始化過且不是伺服器啟動時調用，則跳過
    # 重要：不要在每個請求中都執行索引創建
    if _indexes_initialized:
        if not is_server_startup:
            print("⏩ 索引已初始化，跳過重複初始化")
        return
    
    try:
        if db is None:
            print("❌ 數據庫連接不可用，無法創建索引")
            return

        # 首先初始化訪問統計集合（如果存在）
        try:
            # 檢查是否處於 Railway 或 Render 環境
            is_cloud = os.environ.get('RAILWAY_ENVIRONMENT') or os.environ.get('RAILWAY_URI') or os.environ.get('RENDER')
            if is_cloud:
                # 嘗試導入訪問統計初始化函數
                from src.visitor_counter import ensure_visitor_counter_initialized
                print("🔄 正在初始化訪問統計集合...")
                result = ensure_visitor_counter_initialized()
                if result:
                    print("✅ 訪問統計集合已成功初始化")
                else:
                    print("ℹ️ 訪問統計集合已存在，無需初始化")
        except Exception as e:
            print(f"⚠️ 初始化訪問統計時出錯: {e}")
            # 繼續執行後續索引創建，不因訪問統計初始化失敗而中斷

        _indexes_initialized = True
        

        legislators_col = db['legislators']

        # 檢查當前索引

        legislators_indexes = legislators_col.index_information()

        print(f"目前 legislators 集合有 {len(legislators_indexes)} 個索引")
        
      
                
        if '_id_' not in legislators_indexes:
            print("警告: legislators 集合缺少基本的 _id 索引，這非常不尋常")
            try:
                print("嘗試創建 _id 索引...")
                legislators_col.create_index([("_id", 1)], background=True)
            except Exception as id_err:
                print(f"創建 _id 索引失敗: {id_err}")        # 無論在什麼環境下，都創建所有必要的索引
        # 這確保在本地開發環境和生產環境中都有相同的索引
        print("正在創建所有必要的索引...")
        
        # 為 legislators 集合創建索引 (先處理小集合)
        try:
            # 1. 名稱索引
            print("創建 legislators 名稱索引...")
            legislators_col.create_index([("name", 1)], background=True, name="name_1")
            
            # 短暫暫停，避免資源緊張
            time.sleep(1)
            
            # 2. 黨派索引（如果有此欄位）
            if legislators_col.find_one({"黨派": {"$exists": True}}):
                print("創建黨派索引...")
                legislators_col.create_index([("黨派", 1)], background=True, name="party_1")
                time.sleep(1)
            
            # 3. 地區索引（如果有此欄位）
            if legislators_col.find_one({"地區": {"$exists": True}}):
                print("創建地區索引...")
                legislators_col.create_index([("地區", 1)], background=True, name="region_1")
                time.sleep(1)
        except Exception as e:
            print(f"創建 legislators 索引失敗: {e}")
        

        

        # 索引初始化已在函數開始時設置
        print("✅ 索引初始化標記已設置，不會重複創建索引")
        
    except Exception as e:
        print(f"❌ 數據庫索引創建失敗: {e}")

@lru_cache(maxsize=128)
def get_cached_legislator_data(identifier):
    """緩存立委基本數據查詢"""
    try:
        legislators_col = db['legislators']
        return legislators_col.find_one({'name': identifier})
    except Exception as e:
        print(f"❌ 查詢立委數據失敗: {e}")
        return None

def get_crawler_data_optimized(identifier, start_date=None, end_date=None, limit=None):
    """
    優化的爬蟲數據查詢
    
    策略:
    1. 使用索引加速查詢 (name + 日期)
    2. 只選擇需要的字段
    3. 分批處理大量數據
    4. 使用索引排序
    
    參數:
        identifier: 立委識別符 (通常是姓名)
        start_date: 開始日期 (YYYY-MM-DD 格式)
        end_date: 結束日期 (YYYY-MM-DD 格式)
        limit: 記錄數量限制 (None表示不限制)
        
    返回:
        list: 符合條件的記錄列表
    """
    try:
        if db is None:
            print("❌ 數據庫連接不可用")
            return []

        # 使用現有的 get_crawler_data 函數
        all_data = get_crawler_data(identifier)
        
        if not all_data:
            return []

        # 過濾日期範圍
        filtered_data = []
        for record in all_data:
            record_date = record.get('日期', '')
            if record_date:
                try:
                    if start_date and end_date:
                        if start_date <= record_date <= end_date:
                            filtered_data.append(record)
                    else:
                        filtered_data.append(record)
                except:
                    continue
            else:
                filtered_data.append(record)

        # 應用限制
        if limit:
            filtered_data = filtered_data[:limit]

        return filtered_data

    except Exception as e:
        print(f"❌ 查詢爬蟲數據失敗: {e}")
        return []

def serialize_legislator_data(legislator_data):
    # 將 MongoDB 的 _id (ObjectId) 轉換為字串，以便 JSON 序列化
    if '_id' in legislator_data:
        legislator_data['_id'] = str(legislator_data['_id'])
    # 處理 datetime 物件，將其轉換為 ISO 格式的字串
    if 'last_updated' in legislator_data and isinstance(legislator_data['last_updated'], datetime):
        legislator_data['last_updated'] = legislator_data['last_updated'].isoformat()

    return legislator_data

# --- API 路由 ---

@legislator_app.route('/', methods=['GET'])
def get_legislators():
    try:
        county = request.args.get('county') # 從查詢參數獲取縣市
        party = request.args.get('party') # 從查詢參數獲取黨派
        # 判斷是否需要包含分析數據，預設為 false
        include_analysis = request.args.get('include_analysis', 'false').lower() == 'true'
        if db is None:
            return jsonify({"error": "Database connection not available"}), 500
        legislators_col = db['legislators'] # 獲取 legislators 集合的引用
        query = {} # 初始化查詢條件字典
        # 根據縣市篩選：使用正則表達式進行不區分大小寫的部分匹配
        if county:
            query['constituency'] = {'$regex': county, '$options': 'i'}
        # 根據黨派篩選：使用正則表達式進行不區分大小寫的部分匹配
        if party:
            query['party'] = {'$regex': party, '$options': 'i'}
        # 根據 include_analysis 決定要返回哪些欄位 (投影)
        if include_analysis:
            # 如果需要分析數據，則包含所有相關分析欄位
            projection = {
                '_id': 1, 'id': 1, 'name': 1, 'party': 1, 'constituency': 1, 'image_url': 1,
                '情感分析': 1, '情緒分析': 1, '留言數': 1, '用戶數': 1, # 之前 Python 分析腳本新增的字段
                'positive_count': 1, 'negative_count': 1, 'emotion': 1, 
                'recall_data': 1 # 罷免數據
            }
        else:
            # 否則只返回基本資訊 (姓名、照片、選區資訊、調查數據)
            projection = {'_id': 1, 'id': 1, 'name': 1, 'image_url': 1, 'constituency': 1}

        # 執行 MongoDB 查詢，返回符合條件的立委列表
        legislators = list(legislators_col.find(query, projection))
        # 處理每個立委數據，使其可 JSON 序列化，並為列表視圖添加簡化選區資訊
        for legislator in legislators:
            serialize_legislator_data(legislator)
            # 如果不包含分析數據（即用於列表視圖），則提取簡化選區並移除原始選區陣列
            if not include_analysis and 'constituency' in legislator:
                constituency_list = legislator['constituency']
                district_found = "選區資訊不詳" # 預設值

                if isinstance(constituency_list, list):
                    for item in constituency_list:
                        if isinstance(item, str) and '選區：' in item:
                            district_found = item.replace('選區：', '').strip()
                            break # 找到第一個就跳出
                    else: # 如果循環結束都沒找到 '選區：'
                        if constituency_list:
                            district_found = constituency_list[0] # 使用列表的第一個元素

                legislator['district'] = district_found
                del legislator['constituency'] # 移除完整的 constituency 陣列
        return jsonify(legislators) # 返回 JSON 格式的立委列表

    except Exception as e:
        print(f"Error in get_legislators: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@legislator_app.route('/<identifier>', methods=['GET'])
def get_legislator_detail(identifier):
        legislators_col = db['legislators']
        print(f"🔍 查詢立委: {identifier}")
        print(f"📊 數據庫名稱: {db.name}")
        print(f"📊 集合名稱: {legislators_col.name}")
        
        # 獲取時間範圍參數
        time_range = request.args.get('time_range', '7')  # 預設7天
        print(f"⏰ 請求時間範圍: {time_range}天")
        
        legislator_data = None
        # 首先嘗試以姓名查詢 (最常見的查詢方式)
        legislator_data = legislators_col.find_one({'name': identifier})
        if legislator_data:
            print(f"✅ 找到立委數據，ID: {legislator_data.get('_id')}")
            
            # 確保返回所有模擬的數據字段
            # 檢查並添加模擬的統計數據字段
            if 'wordcloud_data' in legislator_data:
                # 保持原有的wordcloud_data字段
                pass
            elif 'word_cloud' in legislator_data:
                # 如果只有word_cloud字段，複製為wordcloud_data
                legislator_data['wordcloud_data'] = legislator_data['word_cloud']
            
            # 根據時間範圍選擇對應的time_series_stats
            if 'time_series_stats' in legislator_data:
                time_series_stats = legislator_data['time_series_stats']
                
                # 根據時間範圍選擇對應的數據
                selected_stats = None
                if time_range == '7' and 'recent_7_days' in time_series_stats:
                    selected_stats = time_series_stats['recent_7_days']
                    print(f"📊 使用7天時間序列數據")
                elif time_range == '14' and 'recent_14_days' in time_series_stats:
                    selected_stats = time_series_stats['recent_14_days']
                    print(f"📊 使用14天時間序列數據")
                elif time_range == '30' and 'recent_30_days' in time_series_stats:
                    selected_stats = time_series_stats['recent_30_days']
                    print(f"📊 使用30天時間序列數據")
                elif time_range == '90' and 'recent_90_days' in time_series_stats:
                    selected_stats = time_series_stats['recent_90_days']
                    print(f"📊 使用90天時間序列數據")
                elif time_range == '180' and 'recent_180_days' in time_series_stats:
                    selected_stats = time_series_stats['recent_180_days']
                    print(f"📊 使用180天時間序列數據")
                elif time_range == '365' and 'recent_365_days' in time_series_stats:
                    selected_stats = time_series_stats['recent_365_days']
                    print(f"📊 使用365天時間序列數據")
                elif time_range == 'all':
                    # 對於 'all'，使用最長的時間範圍，如果沒有則使用365天
                    if 'recent_365_days' in time_series_stats:
                        selected_stats = time_series_stats['recent_365_days']
                        print(f"📊 使用全部時間範圍數據 (365天)")
                    elif 'recent_180_days' in time_series_stats:
                        selected_stats = time_series_stats['recent_180_days']
                        print(f"📊 使用全部時間範圍數據 (180天)")
                    elif 'recent_90_days' in time_series_stats:
                        selected_stats = time_series_stats['recent_90_days']
                        print(f"📊 使用全部時間範圍數據 (90天)")
                    else:
                        selected_stats = time_series_stats.get('recent_7_days', {})
                        print(f"📊 使用全部時間範圍數據 (預設7天)")
                else:
                    # 如果沒有找到對應的時間範圍，使用預設的7天數據
                    selected_stats = time_series_stats.get('recent_7_days', {})
                    print(f"⚠️ 未找到{time_range}天的數據，使用預設7天數據")
                
                # 將選中的時間序列數據添加到返回結果中
                legislator_data['selected_time_series'] = selected_stats
                
                # 根據選中的時間範圍數據更新圓餅圖數據
                if selected_stats and 'stats_points' in selected_stats:
                    # 計算該時間範圍內的總情感分析數據
                    total_support = 0
                    total_oppose = 0
                    
                    for point in selected_stats['stats_points']:
                        if 'emotion_counts' in point:
                            emotion_counts = point['emotion_counts']
                            # 負面情緒：sadness, anger, fear, disgust = 支持罷免
                            support_count = (emotion_counts.get('sadness', 0) + 
                                          emotion_counts.get('anger', 0) + 
                                          emotion_counts.get('fear', 0) + 
                                          emotion_counts.get('disgust', 0))
                            # 正面情緒：joy, trust, anticipation = 反對罷免
                            oppose_count = (emotion_counts.get('joy', 0) + 
                                          emotion_counts.get('trust', 0) + 
                                          emotion_counts.get('anticipation', 0))
                            
                            total_support += support_count
                            total_oppose += oppose_count
                    
                    # 更新圓餅圖數據
                    legislator_data['recall_support'] = total_support
                    legislator_data['recall_oppose'] = total_oppose
                    print(f"📈 更新圓餅圖數據: 支持={total_support}, 反對={total_oppose}")
                
                # 轉換為前端需要的格式
                if selected_stats and 'stats_points' in selected_stats:
                    stats_points = selected_stats['stats_points']
                    labels = []
                    support_data = []
                    oppose_data = []
                    
                    for point in stats_points:
                        if 'date' in point:
                            # 格式化日期為 MM/DD 格式
                            try:
                                date_obj = datetime.strptime(point['date'], '%Y-%m-%d')
                                # 修改為年/月/日格式
                                labels.append(date_obj.strftime('%Y/%m/%d'))
                            except:
                                labels.append(point['date'])
                        
                        # 從emotion_counts中提取支持/反對數據
                        if 'emotion_counts' in point:
                            emotion_counts = point['emotion_counts']
                            # 負面情緒：sadness, anger, fear, disgust = 支持罷免
                            support_count = (emotion_counts.get('sadness', 0) + 
                                          emotion_counts.get('anger', 0) + 
                                          emotion_counts.get('fear', 0) + 
                                          emotion_counts.get('disgust', 0))
                            # 正面情緒：joy, trust, anticipation = 反對罷免
                            oppose_count = (emotion_counts.get('joy', 0) + 
                                          emotion_counts.get('trust', 0) + 
                                          emotion_counts.get('anticipation', 0))
                        else:
                            support_count = 0
                            oppose_count = 0
                        
                        support_data.append(support_count)
                        oppose_data.append(oppose_count)
                    
                    # 創建Chart.js格式的時間序列數據
                    legislator_data['time_series_chart'] = {
                        "labels": labels,
                        "datasets": [
                            {
                                "label": "支持罷免",
                                "data": support_data,
                                "borderColor": "#f87171",
                                "backgroundColor": "rgba(248, 113, 113, 0.1)",
                                "tension": 0.3,
                                "fill": True
                            },
                            {
                                "label": "反對罷免",
                                "data": oppose_data,
                                "borderColor": "#4f8cff",
                                "backgroundColor": "rgba(79, 140, 255, 0.1)",
                                "tension": 0.3,
                                "fill": True
                            }
                        ]
                    }
            
            # 確保返回所有模擬的統計數據
            # 這些字段應該已經由模擬腳本添加
            expected_fields = [
                'wordcloud_data', 'time_series_stats', 'emotion_analysis', 
                'total_stats', 'recall_support', 'recall_oppose',
                'last_updated', 'analysis_date'
            ]
            
            # 檢查哪些字段存在
            existing_fields = [field for field in expected_fields if field in legislator_data]
            print(f"立委 {identifier} 的數據字段: {existing_fields}")
            print(f"立委 {identifier} 的所有字段: {list(legislator_data.keys())}")
            print(f"wordcloud_data 存在: {'wordcloud_data' in legislator_data}")
            print(f"time_series_stats 存在: {'time_series_stats' in legislator_data}")
            print(f"emotion_analysis 存在: {'emotion_analysis' in legislator_data}")
                
            # 調用 serialize_legislator_data 進行序列化
            serialized_data = serialize_legislator_data(legislator_data)
            return jsonify(serialized_data)
        else:
            print(f"❌ 未找到立委: {identifier}")
            return jsonify({"error": f"Legislator not found with identifier: {identifier}"}), 404

def generate_time_series_chart(time_series_data, start_date, end_date):
    """
    生成時間序列圖表數據 - 根據時間範圍動態調整數據點數量
    """
    from datetime import datetime, timedelta

    # 解析日期
    if isinstance(start_date, str):
        start = datetime.strptime(start_date, '%Y-%m-%d')
    else:
        start = start_date

    if isinstance(end_date, str):
        end = datetime.strptime(end_date, '%Y-%m-%d')
    else:
        end = end_date

    # 計算時間間隔
    total_days = (end - start).days + 1

    # 動態決定數據點數量和間隔，不使用硬編碼的天數判斷
    def calculate_optimal_points(days):
        """根據天數動態計算最佳數據點數量"""
        if days <= 7:
            return days, 1  # 7天以內：每天一個點
        elif days <= 30:
            return min(15, days), max(1, days // 15)  # 30天以內：最多15個點
        elif days <= 90:
            return min(20, days // 3), max(1, days // 20)  # 90天以內：最多20個點
        elif days <= 365:
            return min(25, days // 7), max(1, days // 25)  # 一年以內：最多25個點
        else:
            return min(30, days // 14), max(1, days // 30)  # 超過一年：最多30個點

    target_points, interval_days = calculate_optimal_points(total_days)

    labels = []
    support_data = []
    oppose_data = []

    current_date = start
    while current_date <= end:
        # 計算這個時間點的數據
        period_support = 0
        period_oppose = 0

        # 累計這個間隔內的數據
        for i in range(interval_days):
            check_date = current_date + timedelta(days=i)
            if check_date > end:
                break
            date_key = check_date.strftime('%Y-%m-%d')
            if date_key in time_series_data:
                period_support += time_series_data[date_key]['support']
                period_oppose += time_series_data[date_key]['oppose']

        labels.append(current_date.strftime('%m/%d'))
        support_data.append(period_support)
        oppose_data.append(period_oppose)

        current_date += timedelta(days=interval_days)

    return {
        "labels": labels,
        "datasets": [
            {
                "label": "支持罷免",
                "data": support_data,
                "borderColor": "#f87171",
                "backgroundColor": "rgba(248, 113, 113, 0.1)",
                "tension": 0.3,
                "fill": True
            },
            {
                "label": "反對罷免",
                "data": oppose_data,
                "borderColor": "#4f8cff",
                "backgroundColor": "rgba(79, 140, 255, 0.1)",
                "tension": 0.3,
                "fill": True
            }
        ]
    }

@legislator_app.route('/sentiment/<name>', methods=['GET'])
def get_legislator_sentiment(name):
    """
    獲取特定立委的情感分析數據。
    只返回與情感分析相關的欄位。
    路由: /legislators/sentiment/<name>
    """
    legislators_col = db['legislators']

    projection = {
        'name': 1,
        '情感分析': 1,
        '情緒分析': 1,
        '留言數': 1,
        '用戶數': 1,
        'positive_count': 1, # 可能為舊版或冗餘字段
        'negative_count': 1, # 可能為舊版或冗餘字段
        'emotion': 1, # 可能為舊版或冗餘字段
        'top_words': 1 # 關鍵詞數據
    }

    legislator_data = legislators_col.find_one({'name': name}, projection)

    if legislator_data:
        serialized_data = serialize_legislator_data(legislator_data)
        return jsonify(serialized_data)
    else:
        return jsonify({"error": f"Sentiment data not found for legislator: {name}"}), 404

@legislator_app.route('/recall', methods=['GET'])
def get_recall_list():
    """
    獲取所有有罷免數據的立委列表。
    返回包含罷免相關數據的立委資訊。
    路由: /legislators/recall
    """
    legislators_col = db['legislators']

    # 查詢具有 'recall_data' 欄位且不為空值的立委
    query = {'recall_data': {'$exists': True, '$ne': None}}
    projection = {
        'name': 1,
        'recall_data': 1,
        'image_url': 1,
        'party': 1,
        'constituency': 1
    }
    legislators_with_recall = list(legislators_col.find(query, projection))
    # 將數據轉換為預期格式，把立委的基本資訊合併到罷免數據中
    recall_list = []
    for legislator in legislators_with_recall:
        if 'recall_data' in legislator and legislator['recall_data']:
            recall_item = legislator['recall_data'].copy() # 複製罷免數據
            # 添加額外的立委資訊到罷免數據項目中            recall_item['image_url'] = legislator.get('image_url', '')
            recall_item['party'] = legislator.get('party', '')
            recall_item['constituency'] = legislator.get('constituency', '')
            # 添加 status 字段（從 recall_data.狀態 提取）
            recall_item['status'] = recall_item.get('狀態', '網路聲量調查')
            # 添加罷免狀態與說明字段
            recall_item['recall_status'] = recall_item.get('罷免狀態', '狀態未知')
            recall_item['recall_description'] = recall_item.get('罷免狀態描述', '')
            recall_list.append(recall_item)

    # 序列化每個項目以便 JSON 響應
    for item in recall_list:
        serialize_legislator_data(item)
    return jsonify(recall_list) # 返回 JSON 格式的罷免列表

@legislator_app.route('/<identifier>/time-series', methods=['GET'])
def get_legislator_time_series(identifier):
    """API1：初始載入 - 返回全時間範圍數據"""
    try:
        if db is None:
            return jsonify({"error": "Database connection not available"}), 500

        # 獲取日期範圍
        date_range = get_legislator_date_range_data(identifier)
        if not date_range:
            return jsonify({
                "word_cloud": [],
                "time_series": {"labels": [], "datasets": []},
                "emotion_analysis_detailed": {"positive": {}, "negative": {}},
                "total_records": 0,
                "date_range": {"start_date": None, "end_date": None}
            })

        # 使用現有的 get_crawler_data 函數
        recent_data = get_crawler_data(identifier)

        # 生成詞雲 - 使用改進的中文分詞
        word_cloud = []
        if recent_data:
            from collections import Counter
            word_counter = Counter()

            # 載入停用詞
            stopwords = load_stopwords()

            for record in recent_data[:500]:  # 只處理前500筆
                content = record.get('留言內容', '') or record.get('標題', '')
                if content and len(content) > 2:
                    # 使用改進的中文分詞
                    try:
                        import jieba
                        import jieba.posseg as pseg

                        # 使用詞性標註，只保留名詞、動詞、形容詞
                        words = pseg.cut(content)
                        for word, flag in words:
                            if (len(word) >= 2 and
                                word not in stopwords and
                                flag in ['n', 'v', 'a', 'nr', 'ns', 'nt', 'nz'] and  # 名詞、動詞、形容詞
                                not word.isdigit() and
                                not all(c in '，。！？；：""''（）【】' for c in word)):
                                word_counter[word] += 1
                    except ImportError:
                        # 如果沒有 jieba，使用簡化版本
                        words = [w.strip() for w in content.split() if len(w.strip()) >= 2]
                        for word in words[:5]:
                            if word not in stopwords and len(word) <= 6:  # 限制長度
                                word_counter[word] += 1

            word_cloud = [
                {"text": word, "weight": count}
                for word, count in word_counter.most_common(50)
                if len(word) >= 2 and len(word) <= 6  # 確保是短詞或單字
            ]

        # 生成真實的全時間範圍時間序列
        from datetime import datetime, timedelta

        # 使用實際的數據日期範圍
        start_date_str = date_range['start_date']  # 2020-06-04
        end_date_str = date_range['end_date']      # 2025-09-30

        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')

        # 計算總天數，動態決定間隔
        total_days = (end_date - start_date).days + 1

        # 動態決定數據點數量，讓圖表可以左右橫移
        def calculate_optimal_interval(days):
            """根據天數動態計算最佳間隔和數據點數量"""
            if days <= 7:
                return 1, days  # 7天：每天一個點，7個點
            elif days <= 30:
                return 2, 15    # 30天：每2天一個點，15個點
            elif days <= 60:
                return 2, 30    # 60天：每2天一個點，30個點
            elif days <= 90:
                return 3, 30    # 90天：每3天一個點，30個點
            elif days <= 180:
                return 6, 30    # 半年：每6天一個點，30個點
            elif days <= 365:
                return 12, 30   # 一年：每12天一個點，30個點
            else:
                return max(15, days // 40), min(50, days // 15)  # 超過一年：更多點，支持橫移

        interval_days, target_points = calculate_optimal_interval(total_days)

        # 統計實際數據中的情感分布 - 按日期分組
        time_series_data = {}
        actual_dates = set()

        for record in recent_data:
            record_date = record.get('日期', '')
            if record_date:
                actual_dates.add(record_date)
                if record_date not in time_series_data:
                    time_series_data[record_date] = {"support": 0, "oppose": 0}

                sentiment = record.get('情感標籤', '')
                if sentiment in ['NEGATIVE', 'negative']:
                    time_series_data[record_date]['support'] += 1
                elif sentiment in ['POSITIVE', 'positive']:
                    time_series_data[record_date]['oppose'] += 1

        # 修復：根據時間範圍長短決定統計間隔，避免重複標籤
        if actual_dates:
            sorted_dates = sorted(list(actual_dates))

            # 根據時間範圍長短決定統計方式
            def generate_time_intervals(total_days):
                """根據時間範圍生成不重複的時間間隔"""
                from datetime import datetime, timedelta

                start_dt = datetime.strptime(start_date_str, '%Y-%m-%d')
                end_dt = datetime.strptime(end_date_str, '%Y-%m-%d')

                intervals = []
                labels = []

                if total_days <= 7:
                    # 7天以內：每天統計
                    current = start_dt
                    while current <= end_dt:
                        intervals.append(current.strftime('%Y-%m-%d'))
                        labels.append(current.strftime('%m/%d'))
                        current += timedelta(days=1)

                elif total_days <= 30:
                    # 30天以內：每2-3天統計
                    step = max(1, total_days // 15)
                    current = start_dt
                    while current <= end_dt:
                        intervals.append(current.strftime('%Y-%m-%d'))
                        labels.append(current.strftime('%m/%d'))
                        current += timedelta(days=step)

                elif total_days <= 90:
                    # 90天以內：每週統計，顯示實際日期
                    current = start_dt
                    while current <= end_dt:
                        intervals.append(current.strftime('%Y-%m-%d'))
                        labels.append(current.strftime('%m/%d'))  # 顯示月/日
                        current += timedelta(days=7)

                elif total_days <= 365:
                    # 一年以內：每月統計
                    current = start_dt.replace(day=1)  # 月初
                    while current <= end_dt:
                        intervals.append(current.strftime('%Y-%m-%d'))
                        labels.append(current.strftime('%Y/%m'))
                        # 下個月
                        if current.month == 12:
                            current = current.replace(year=current.year + 1, month=1)
                        else:
                            current = current.replace(month=current.month + 1)
                else:
                    # 超過一年：每月統計，提供更多數據點
                    current = start_dt.replace(day=1)  # 月初
                    while current <= end_dt:
                        intervals.append(current.strftime('%Y-%m-%d'))
                        labels.append(current.strftime('%Y/%m'))  # 年/月格式
                        # 下個月
                        if current.month == 12:
                            current = current.replace(year=current.year + 1, month=1)
                        else:
                            current = current.replace(month=current.month + 1)

                return intervals, labels

            # 生成時間間隔
            intervals, labels = generate_time_intervals(total_days)

            # 統計每個間隔的數據
            support_data = []
            oppose_data = []

            for i, interval_start in enumerate(intervals):
                # 計算間隔結束日期
                if i < len(intervals) - 1:
                    interval_end = intervals[i + 1]
                else:
                    interval_end = end_date_str

                # 統計該間隔內的數據
                period_support = 0
                period_oppose = 0

                for date_str in sorted_dates:
                    if interval_start <= date_str < interval_end:
                        day_data = time_series_data.get(date_str, {"support": 0, "oppose": 0})
                        period_support += day_data['support']
                        period_oppose += day_data['oppose']

                support_data.append(period_support)
                oppose_data.append(period_oppose)
        else:
            labels = []
            support_data = []
            oppose_data = []

        time_series = {
            "labels": labels,
            "datasets": [
                {
                    "label": "支持罷免",
                    "data": support_data,
                    "borderColor": "#f87171",
                    "backgroundColor": "rgba(248, 113, 113, 0.1)",
                    "tension": 0.3,
                    "fill": True
                },
                {
                    "label": "反對罷免",
                    "data": oppose_data,
                    "borderColor": "#4f8cff",
                    "backgroundColor": "rgba(79, 140, 255, 0.1)",
                    "tension": 0.3,
                    "fill": True
                }
            ]
        }

        # 統計情感和情緒數據
        sentiment_stats = {"support_count": 0, "oppose_count": 0}
        emotion_stats = {"positive": {}, "negative": {}}

        # 初始化情緒統計
        emotions = ['joy', 'anger', 'sadness', 'fear', 'surprise', 'disgust', 'trust', 'anticipation']
        for emotion in emotions:
            emotion_stats["positive"][emotion] = 0
            emotion_stats["negative"][emotion] = 0

        # 統計實際數據
        for record in recent_data:
            sentiment = record.get('情感標籤', '')
            emotion = record.get('情緒', '')

            # 統計情感
            if sentiment in ['NEGATIVE', 'negative']:
                sentiment_stats["support_count"] += 1
            elif sentiment in ['POSITIVE', 'positive']:
                sentiment_stats["oppose_count"] += 1

            # 統計情緒
            if emotion in emotions:
                if sentiment in ['NEGATIVE', 'negative']:
                    emotion_stats["negative"][emotion] += 1
                elif sentiment in ['POSITIVE', 'positive']:
                    emotion_stats["positive"][emotion] += 1

        # 按照 before.py 的正確格式返回數據
        result = {
            "word_cloud": word_cloud,
            "time_series": time_series,
            "sentiment_analysis": sentiment_stats,  # 改為 sentiment_analysis
            "emotion_analysis_detailed": {
                "positive": emotion_stats["positive"],  # 反對罷免的情緒
                "negative": emotion_stats["negative"]   # 支持罷免的情緒
            },
            "total_records": len(recent_data),
            "date_range": {
                "start_date": date_range['start_date'],
                "end_date": date_range['end_date']
            }
        }
        return jsonify(result)

    except Exception as e:
        print(f"Error in get_legislator_time_series: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

def analyze_full_time_series(all_data, identifier):
    # 詞雲分析 - 統計所有文本中的關鍵詞
    word_counter = Counter()
    time_series_data = {}

    # 情緒分析統計
    emotion_counts = Counter()
    raw_positive_emotion_counts = Counter()  # 反對罷免的情緒（原始）
    raw_negative_emotion_counts = Counter()  # 支持罷免的情緒（原始）

    # 標準的8大情緒
    allowed_emotions = ['joy', 'anger', 'sadness', 'fear', 'surprise', 'disgust', 'trust', 'anticipation']

    # 找出數據的時間範圍
    dates = []

    for data in all_data:
        # 提取文本內容進行詞雲分析
        text_content = ""
        #if '標題' in data:
        #    text_content += data['標題'] + " "
        if '留言內容' in data:
            text_content += data['留言內容'] + " "
        # 提取關鍵詞 - 使用統一的關鍵字提取函數
        if text_content:
            meaningful_words = extract_meaningful_keywords(text_content, min_count=1)
            word_counter.update(dict(meaningful_words))
        # 收集日期信息
        date_str = None
        if '日期' in data:
            date_str = data['日期']
        if date_str:
            try:
                # 嘗試解析日期
                if isinstance(date_str, str):
                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                dates.append(date_obj)
                # 統計情感數據用於時間序列
                date_key = date_obj.strftime('%Y-%m-%d')
                if date_key not in time_series_data:
                    time_series_data[date_key] = {"support": 0, "oppose": 0}

                # 分析情感
                sentiment_value = None
                if '情感標籤' in data:
                    sentiment_value = data['情感標籤']
                if sentiment_value:
                    if sentiment_value in ['POSITIVE', 'positive']:
                        time_series_data[date_key]['oppose'] += 1
                    elif sentiment_value in ['NEGATIVE', 'negative']:
                        time_series_data[date_key]['support'] += 1
                    #可以加入中立，但我還沒想好
            except Exception as e:
                print(f"日期解析錯誤: {date_str}, {e}")
                continue
        # 分析情緒（根據情感標籤分類）
        emotion_value = data['情緒']
        sentiment_value = data['情感標籤']
        if emotion_value:
            emotion_counts[emotion_value] += 1

            # 根據情感標籤分類情緒
            if sentiment_value in ['POSITIVE', 'positive']:
                # POSITIVE = 反對罷免，所以這個情緒屬於反對罷免
                raw_positive_emotion_counts[emotion_value] += 1
            elif sentiment_value in ['NEGATIVE', 'negative']:
                # NEGATIVE = 支持罷免，所以這個情緒屬於支持罷免
                raw_negative_emotion_counts[emotion_value] += 1

    # 過濾和標準化情緒數據
    final_positive_emotion_counts = Counter()
    final_negative_emotion_counts = Counter()

    # 標準化 neutral 相關的鍵
    for emotion, count in raw_positive_emotion_counts.items():
        if emotion in allowed_emotions:
            final_positive_emotion_counts[emotion] += count
        else: # 任何不在允許情緒中的都歸為 neutral
            final_positive_emotion_counts['neutral'] += count

    # 過濾負面情緒
    for emotion, count in raw_negative_emotion_counts.items():
        if emotion in allowed_emotions:
            final_negative_emotion_counts[emotion] += count
        else: # 任何不在允許情緒中的都歸為 neutral
            final_negative_emotion_counts['neutral'] += count

    # 生成詞雲數據（取前50個最常見的詞）- 使用統一函數
    # 直接使用word_counter的結果，因為extract_meaningful_keywords已經過濾過了
    word_cloud = [
        {"text": word, "weight": count}
        for word, count in word_counter.most_common(50)
    ]
    # 生成動態時間序列圖表
    time_series_chart = generate_time_series_chart(time_series_data, min(dates) if dates else None, max(dates) if dates else None)

    return {
        "word_cloud": word_cloud,
        "time_series": time_series_chart,
        "emotion_analysis": dict(emotion_counts),  # 總情緒分析數據
        "emotion_analysis_detailed": {
            "positive": dict(final_positive_emotion_counts),  # POSITIVE情感標籤的情緒 = 反對罷免情緒
            "negative": dict(final_negative_emotion_counts)   # NEGATIVE情感標籤的情緒 = 支持罷免情緒
        },
        "total_records": len(all_data),
        "date_range": {
            "start": min(dates).strftime('%Y-%m-%d') if dates else None,
            "end": max(dates).strftime('%Y-%m-%d') if dates else None
        }
    }

# 刪除重複的 generate_quarterly_time_series 函數，使用 generate_time_series_chart 替代

# 函數1：獲取立委的日期範圍和基本統計
def get_legislator_date_range_data(identifier):
    """
    獲取指定立委的crawler_data日期範圍和基本統計
    返回：最早日期、最晚日期、總記錄數
    """
    try:
        if db is None:
            return None

        # 使用現有的 get_crawler_data 函數
        all_data = get_crawler_data(identifier)
        
        if not all_data:
            return None

        # 收集所有有效日期
        valid_dates = []
        for record in all_data:
            date_str = record.get('日期', '')
            if date_str and isinstance(date_str, str):
                try:
                    # 驗證日期格式
                    datetime.strptime(date_str, '%Y-%m-%d')
                    valid_dates.append(date_str)
                except:
                    continue

        if not valid_dates:
            return None

        # 計算日期範圍
        start_date = min(valid_dates)
        end_date = max(valid_dates)
        total_records = len(all_data)

        return {
            "start_date": start_date,
            "end_date": end_date,
            "total_records": total_records
        }

    except Exception as e:
        print(f"❌ Error in get_legislator_date_range_data: {e}")
        return None

@legislator_app.route('/<identifier>/date-range', methods=['GET'])
def get_legislator_date_range(identifier):
    """API：返回立委的日期範圍"""
    try:
        date_range_data = get_legislator_date_range_data(identifier)

        if not date_range_data:
            return jsonify({
                "start_date": None,
                "end_date": None,
                "total_records": 0,
                "message": "No data found for this legislator"
            })

        return jsonify(date_range_data)

    except Exception as e:
        print(f"❌ Error in get_legislator_date_range: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

# 使用 LRU 緩存來存儲時間範圍查詢結果，提高重複查詢的性能
@legislator_app.route('/check-indexes', methods=['GET'])
def api_check_indexes():
    """API端點：檢查數據庫索引狀態"""
    try:
        # 檢查 legislators 集合的索引
        legislators_col = db['legislators']
        index_info = legislators_col.index_information()
        
        # 格式化索引信息用於API返回
        formatted_indexes = []
        for name, info in index_info.items():
            # 提取索引字段信息
            keys = [f"{field}: {direction}" for field, direction in info['key']]
            formatted_indexes.append({
                "name": name,
                "keys": keys,
                "is_system": name == '_id_'
            })
            
        return jsonify({
            "total_indexes": len(index_info),
            "indexes": formatted_indexes,
            "atlas_free_limit": 3,
            "is_within_limit": len(index_info) - 1 <= 3  # 減去系統的_id索引
        })
    except Exception as e:
        return jsonify({"error": f"檢查索引失敗: {str(e)}"}), 500

# 檢查是否處於雲端環境
is_cloud = os.environ.get('RENDER') or os.environ.get('MONGODB_ATLAS_URI') or 'mongodb+srv://' in os.environ.get('MONGODB_URI', '')

def is_cloud_environment():
    """
    檢查是否在雲端環境中運行
    
    返回值:
        bool: 是否為雲端環境
    """
    # 1. 檢查明確的環境設定
    if os.environ.get('FLASK_ENV', '').lower() == 'development' or os.environ.get('FLASK_CONFIG', '').lower() == 'development':
        return False
        
    # 2. 檢查是否為生產環境設定
    if os.environ.get('FLASK_ENV', '').lower() == 'production' or os.environ.get('FLASK_CONFIG', '').lower() == 'production':
        return True
        
    # 3. 檢查 Atlas URI 是否存在
    if 'MONGODB_ATLAS_URI' in os.environ:
        return True
        
    # 4. 檢查 MongoDB URI 是否包含 Atlas 關鍵字
    mongodb_uri = os.environ.get('MONGODB_URI', '')
    if 'mongodb+srv://' in mongodb_uri:
        return True
        
    # 5. 檢查是否在 Render 環境
    if os.environ.get('RENDER') or os.environ.get('IS_RENDER'):
        return True
        
    return False

@legislator_app.route('/<identifier>/data', methods=['GET'])
def get_legislator_unified_data(identifier):
    """統一的立委數據API端點
    支持查詢參數：
    - days: 天數 (預設365天) - 直接對應time_series_stats中的預定義對象
    - include_pie_chart: 是否包含圓餅圖數據 (預設true)
    - include_time_series: 是否包含時間序列數據 (預設true)
    - include_word_cloud: 是否包含詞雲數據 (預設true)
    - include_emotion: 是否包含情緒分析數據 (預設true)
    """
    try:
        # 計時開始
        api_start_time = time.time()
        
        # 獲取查詢參數
        days = request.args.get('days', '365')  # 預設365天
        include_pie_chart = request.args.get('include_pie_chart', 'true').lower() == 'true'
        include_time_series = request.args.get('include_time_series', 'true').lower() == 'true'
        include_word_cloud = request.args.get('include_word_cloud', 'true').lower() == 'true'
        include_emotion = request.args.get('include_emotion', 'true').lower() == 'true'
        
        # 驗證數據庫連接
        if db is None:
            return jsonify({"error": "數據庫連接不可用", "code": "DATABASE_UNAVAILABLE"}), 500
        
        # 獲取立委基本數據
        legislator_data = get_legislator_by_name(identifier)
        if not legislator_data:
            return jsonify({"error": "立委不存在", "code": "LEGISLATOR_NOT_FOUND"}), 404
        
        # 初始化結果
        result = {
            "legislator": {
                "name": legislator_data.get("name", identifier),
                "photo": legislator_data.get("photo", ""),
                "party": legislator_data.get("party", ""),
                "constituency": legislator_data.get("constituency", "")
            },
            "total_records": 0,
            "api_response_time": f"{time.time() - api_start_time:.2f}秒"
        }
        
        # 映射天數到time_series_stats的鍵名 - 支持新的daily結構
        days_mapping = {
            '7': 'recent_7_days_daily',
            '14': 'recent_14_days_daily', 
            '30': 'recent_30_days_daily',
            '90': 'recent_90_days_daily',
            '180': 'recent_180_days_daily',
            '365': 'recent_365_days_daily'
        }
        
        # 獲取對應的時間範圍數據
        time_series_key = days_mapping.get(days, 'recent_365_days_daily')
        
        # 從time_series_stats中獲取對應的數據
        if 'time_series_stats' in legislator_data:
            time_series_stats = legislator_data['time_series_stats']
            
            # 優先使用新的daily結構，如果沒有則回退到舊結構
            selected_stats = time_series_stats.get(time_series_key)
            if not selected_stats:
                # 回退到舊的結構
                fallback_mapping = {
                    'recent_7_days_daily': 'recent_7_days',
                    'recent_14_days_daily': 'recent_14_days',
                    'recent_30_days_daily': 'recent_30_days',
                    'recent_90_days_daily': 'recent_90_days',
                    'recent_180_days_daily': 'recent_180_days',
                    'recent_365_days_daily': 'recent_365_days'
                }
                fallback_key = fallback_mapping.get(time_series_key, 'recent_365_days')
                selected_stats = time_series_stats.get(fallback_key, {})
                print(f"⚠️ 使用回退結構: {fallback_key}")
            
            if selected_stats:
                print(f"📊 使用{time_series_key}時間序列數據")
                
                # 處理時間序列數據
                if include_time_series and 'stats_points' in selected_stats:
                    stats_points = selected_stats['stats_points']
                    
                    # 計算日期範圍
                    days_int = int(days)
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=days_int)
                    
                    # 過濾在日期範圍內的數據點
                    filtered_points = []
                    for point in stats_points:
                        if 'date' in point:
                            try:
                                point_date = datetime.strptime(point['date'], '%Y-%m-%d')
                                if start_date <= point_date <= end_date:
                                    filtered_points.append(point)
                            except:
                                continue
                    
                    # 按日期排序
                    filtered_points.sort(key=lambda x: x.get('date', ''))
                    
                    labels = []
                    support_data = []
                    oppose_data = []
                    
                    for point in filtered_points:
                        if 'date' in point:
                            try:
                                date_obj = datetime.strptime(point['date'], '%Y-%m-%d')
                                # 修改為年/月/日格式
                                labels.append(date_obj.strftime('%Y/%m/%d'))
                            except:
                                labels.append(point['date'])
                        
                        # 直接使用sentiment_counts數據
                        if 'sentiment_counts' in point:
                            sentiment_counts = point['sentiment_counts']
                            support_count = sentiment_counts.get('support', 0)
                            oppose_count = sentiment_counts.get('oppose', 0)
                            
                            support_data.append(support_count)
                            oppose_data.append(oppose_count)
                    
                    result["time_series"] = {
                        "labels": labels,
                        "datasets": [
                            {
                                "label": "支持罷免",
                                "data": support_data,
                                "borderColor": "#f87171",
                                "backgroundColor": "rgba(248, 113, 113, 0.1)"
                            },
                            {
                                "label": "反對罷免", 
                                "data": oppose_data,
                                "borderColor": "#4f8cff",
                                "backgroundColor": "rgba(79, 140, 255, 0.1)"
                            }
                        ]
                    }
                
                # 處理圓餅圖數據
                if include_pie_chart:
                    # 只取最後一個時間點的累計數據，而不是累加所有時間點
                    if 'stats_points' in selected_stats and selected_stats['stats_points']:
                        # 取最後一個時間點的數據
                        last_point = selected_stats['stats_points'][-1]
                        if 'sentiment_counts' in last_point:
                            sentiment_counts = last_point['sentiment_counts']
                            total_support = sentiment_counts.get('support', 0)
                            total_oppose = sentiment_counts.get('oppose', 0)
                        else:
                            total_support = 0
                            total_oppose = 0
                    else:
                        total_support = 0
                        total_oppose = 0
                    
                    result["sentiment_analysis"] = {
                        "support_count": total_support,
                        "oppose_count": total_oppose
                    }
                
                # 處理詞雲數據
                if include_word_cloud:
                    # 優先使用MongoDB中預存的詞雲數據
                    if 'wordcloud_data' in legislator_data and legislator_data['wordcloud_data']:
                        result["word_cloud"] = legislator_data['wordcloud_data']
                        print(f"✅ 使用預存詞雲數據: {len(legislator_data['wordcloud_data'])} 個關鍵詞")
                    elif 'word_cloud' in legislator_data and legislator_data['word_cloud']:
                        result["word_cloud"] = legislator_data['word_cloud']
                        print(f"✅ 使用預存詞雲數據: {len(legislator_data['word_cloud'])} 個關鍵詞")
                    else:
                        # 如果沒有預存數據，嘗試重新生成
                        try:
                            # 獲取該立委的crawler數據
                            crawler_data = get_crawler_data_optimized(identifier, limit=1000)
                            
                            if crawler_data:
                                # 收集所有文本內容
                                all_text = ""
                                for data in crawler_data:
                                    content = data.get('留言內容', '')
                                    if content and len(content.strip()) > 5:
                                        all_text += content + " "
                                
                                if all_text.strip():
                                    # 使用改進的關鍵詞提取函數
                                    keywords = extract_meaningful_keywords(all_text, min_count=2)
                                    
                                    # 轉換為文字雲格式
                                    wordcloud_data = [
                                        {
                                            "text": word,
                                            "weight": count
                                        }
                                        for word, count in keywords
                                        if count >= 2  # 至少出現2次
                                    ]
                                    
                                    result["word_cloud"] = wordcloud_data
                                    print(f"✅ 重新生成文字雲數據: {len(wordcloud_data)} 個關鍵詞")
                                else:
                                    print("⚠️ 沒有有效的文本內容用於生成文字雲")
                                    result["word_cloud"] = []
                            else:
                                print("⚠️ 沒有crawler數據用於生成文字雲")
                                result["word_cloud"] = []
                                
                        except Exception as e:
                            print(f"❌ 生成文字雲數據時出錯: {e}")
                            result["word_cloud"] = []
                
                # 處理情緒分析數據
                if include_emotion and 'emotion_analysis' in legislator_data:
                    result["emotion_analysis"] = legislator_data['emotion_analysis']
                    if 'emotion_analysis_detailed' in legislator_data:
                        result["emotion_analysis_detailed"] = legislator_data['emotion_analysis_detailed']
                
                # 更新總記錄數
                if 'stats_points' in selected_stats:
                    result["total_records"] = len(selected_stats['stats_points'])
            else:
                print(f"⚠️ 未找到{time_series_key}的數據")
        
        # 添加日期範圍信息
        result["date_range"] = {
            "days": int(days),
            "time_series_key": time_series_key
        }
        
        return jsonify(result)
        
    except Exception as e:
        print(f"❌ Error in get_legislator_unified_data: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Internal server error: {str(e)}", "code": "INTERNAL_ERROR"}), 500

