"""
AI 聊天助手
使用 Gemini 2.5 API 提供聊天對話和文字雲詞彙解釋功能
"""

import google.generativeai as genai
import json
import os
import logging
from flask import Blueprint, request, jsonify
from typing import Dict, Any, Optional
import re

# 創建藍圖
ai_chat_bp = Blueprint('ai-chat', __name__, url_prefix='/api/ai')

# 設置日誌
logger = logging.getLogger(__name__)

class AiChatAssistant:
    """AI 聊天助手服務"""
    
    def __init__(self):
        # 載入 API 配置
        self.api_key = self._load_api_key()
        self.model = None
        self._setup_gemini()
        
        # 載入知識庫
        self.website_knowledge = self._load_website_knowledge()
        self.legislator_data = self._load_legislator_data()
        
    def _load_api_key(self) -> str:
        """載入 Gemini API Key"""
        api_path = os.path.join(os.path.dirname(__file__), '../api.json')
        try:
            with open(api_path, 'r', encoding='utf-8') as f:
                api_data = json.load(f)
                if api_data.get('api_keys'):
                    return api_data['api_keys'][0]
        except Exception as e:
            logger.error(f"載入 API Key 失敗: {e}")
        return ""
    
    def _setup_gemini(self):
        """設置 Gemini 模型"""
        if self.api_key:
            try:
                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Gemini 模型初始化成功")
            except Exception as e:
                logger.error(f"Gemini 模型初始化失敗: {e}")
        else:
            logger.warning("⚠️ 未配置 API Key，將使用模擬回應")
    
    def _load_website_knowledge(self) -> Dict[str, Any]:
        """載入網站知識庫"""
        return {
            'website_name': 'Legislative Recall 立委罷免數據分析平台',
            'description': '這是一個專門分析台灣立委在各大社群平台的討論數據、情緒分析、聲量趨勢的網站',
            'features': [
                {
                    'name': '文字雲分析',
                    'description': '顯示立委討論中的熱門關鍵詞，幫助了解公眾關注焦點',
                    'usage': '點擊立委頁面可查看其文字雲，了解討論熱點'
                },
                {
                    'name': '情緒分析',
                    'description': '分析網友對立委的情感傾向，包括憤怒、悲傷、高興等情緒',
                    'usage': '查看情緒分布圖表，了解公眾情感變化'
                },
                {
                    'name': '聲量趨勢',
                    'description': '追蹤立委在各平台的討論熱度變化',
                    'usage': '觀察聲量曲線，了解討論熱度趨勢'
                },
                {
                    'name': '多平台數據',
                    'description': '整合PTT、YouTube、Threads等平台的討論數據',
                    'usage': '比較不同平台的討論特點和熱度'
                }
            ],
            'data_sources': [
                'PTT 政治版討論',
                'YouTube 政治相關影片評論',
                'Threads 政治討論',
                '新聞媒體報導'
            ]
        }
    
    def _load_legislator_data(self) -> Dict[str, Any]:
        """載入立委數據"""
        try:
            # 這裡可以載入實際的立委數據
            # 暫時使用模擬數據
            return {
                'legislators': [
                    '丁學忠', '傅崐萁', '廖偉翔', '楊瓊瓔', '江啟臣',
                    '游顥', '呂玉玲', '廖先翔', '張智倫', '邱若華',
                    '魯明哲', '萬美玲', '羅明才', '林思銘', '林德福',
                    '鄭正鈐', '賴士葆', '涂權吉', '徐欣瑩', '李彥秀',
                    '林沛祥', '洪孟楷', '牛煦庭', '王鴻薇'
                ]
            }
        except Exception as e:
            logger.error(f"載入立委數據失敗: {e}")
            return {'legislators': []}
    
    def chat(self, message: str, user_id: str = None) -> Dict[str, Any]:
        """處理聊天對話"""
        try:
            if not self.model:
                return self._generate_mock_response(message)
            
            # 構建提示詞
            prompt = self._build_chat_prompt(message)
            
            # 調用 Gemini API
            response = self.model.generate_content(prompt)
            
            return {
                'success': True,
                'message': response.text.strip(),
                'user_id': user_id
            }
            
        except Exception as e:
            logger.error(f"聊天處理失敗: {e}")
            return {
                'success': False,
                'message': '抱歉，我現在無法回應，請稍後再試。',
                'error': str(e)
            }
    
    def explain_wordcloud_word(self, word: str, legislator_name: str = None) -> Dict[str, Any]:
        """解釋文字雲中的詞彙"""
        try:
            if not self.model:
                return self._generate_mock_word_explanation(word, legislator_name)
            
            # 構建詞彙解釋提示詞
            prompt = self._build_word_explanation_prompt(word, legislator_name)
            
            # 調用 Gemini API
            response = self.model.generate_content(prompt)
            
            return {
                'success': True,
                'word': word,
                'legislator': legislator_name,
                'explanation': response.text.strip()
            }
            
        except Exception as e:
            logger.error(f"詞彙解釋失敗: {e}")
            return {
                'success': False,
                'word': word,
                'explanation': f'抱歉，無法解釋詞彙 "{word}"，請稍後再試。',
                'error': str(e)
            }
    
    def _build_chat_prompt(self, message: str) -> str:
        """構建聊天提示詞"""
        return f"""
你是一位專業的台灣政治數據分析助手，專門協助用戶了解立委罷免數據分析平台的功能和數據。

網站資訊：
- 網站名稱：{self.website_knowledge['website_name']}
- 網站描述：{self.website_knowledge['description']}

主要功能：
{chr(10).join([f"- {feature['name']}: {feature['description']}" for feature in self.website_knowledge['features']])}

數據來源：
{chr(10).join([f"- {source}" for source in self.website_knowledge['data_sources']])}

立委列表：
{chr(10).join([f"- {legislator}" for legislator in self.legislator_data['legislators']])}

請根據用戶的問題，提供專業、友善且有用的回答。回答時請：
1. 保持客觀中立
2. 提供具體的數據分析見解
3. 使用繁體中文
4. 如果涉及政治敏感話題，保持理性分析
5. 鼓勵用戶探索網站功能

用戶問題：{message}

請回答：
"""
    
    def _build_word_explanation_prompt(self, word: str, legislator_name: str = None) -> str:
        """構建詞彙解釋提示詞"""
        context = ""
        if legislator_name:
            context = f"這個詞彙出現在立委 {legislator_name} 的文字雲分析中。"
        
        return f"""
你是一位專業的台灣政治數據分析師，需要解釋文字雲中出現的詞彙。

詞彙：{word}
{context}

請分析這個詞彙在政治討論中可能出現的原因和含義：

1. 詞彙的基本含義
2. 在政治討論中的特殊含義
3. 為什麼這個詞彙會在立委討論中出現
4. 可能反映的社會現象或政治議題
5. 對立委聲量的影響

請提供客觀、專業的分析，使用繁體中文回答。
"""
    
    def _generate_mock_response(self, message: str) -> Dict[str, Any]:
        """生成模擬回應（當API不可用時）"""
        # 構建功能列表
        features_text = '\n'.join([f"- {feature['name']}: {feature['description']}" for feature in self.website_knowledge["features"]])
        legislators_text = '\n'.join([f"- {legislator}" for legislator in self.legislator_data["legislators"][:10]])
        
        mock_responses = {
            '你好': '你好！我是立委罷免數據分析平台的AI助手，可以協助你了解網站功能和數據分析。',
            '功能': f'本平台主要功能包括：\n{features_text}',
            '立委': f'目前分析的立委包括：\n{legislators_text}',
            '數據': '我們的數據來源包括PTT、YouTube、Threads等平台的討論內容，經過情感分析和關鍵詞提取處理。'
        }
        
        for key, response in mock_responses.items():
            if key in message:
                return {
                    'success': True,
                    'message': response,
                    'mock': True
                }
        
        return {
            'success': True,
            'message': '抱歉，我現在無法提供完整服務，請稍後再試。',
            'mock': True
        }
    
    def _generate_mock_word_explanation(self, word: str, legislator_name: str = None) -> Dict[str, Any]:
        """生成模擬詞彙解釋"""
        context = f"在立委 {legislator_name} 的討論中" if legislator_name else "在政治討論中"
        
        return {
            'success': True,
            'word': word,
            'legislator': legislator_name,
            'explanation': f'詞彙 "{word}" {context}出現，可能反映相關的政治議題或社會關注焦點。由於API暫時不可用，無法提供詳細分析。',
            'mock': True
        }

# 創建助手實例
ai_assistant = AiChatAssistant()

# API 路由
@ai_chat_bp.route('/chat', methods=['POST'])
def chat():
    """聊天對話API"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        user_id = data.get('user_id')
        
        if not message:
            return jsonify({
                'success': False,
                'message': '請提供聊天訊息'
            }), 400
        
        result = ai_assistant.chat(message, user_id)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"聊天API錯誤: {e}")
        return jsonify({
            'success': False,
            'message': '處理請求時發生錯誤',
            'error': str(e)
        }), 500

@ai_chat_bp.route('/explain-word', methods=['POST'])
def explain_word():
    """解釋文字雲詞彙API"""
    try:
        data = request.get_json()
        word = data.get('word', '').strip()
        legislator_name = data.get('legislator_name', '').strip()
        
        if not word:
            return jsonify({
                'success': False,
                'message': '請提供要解釋的詞彙'
            }), 400
        
        result = ai_assistant.explain_wordcloud_word(word, legislator_name)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"詞彙解釋API錯誤: {e}")
        return jsonify({
            'success': False,
            'message': '處理請求時發生錯誤',
            'error': str(e)
        }), 500

@ai_chat_bp.route('/health', methods=['GET'])
def health():
    """健康檢查API"""
    return jsonify({
        'status': 'healthy',
        'api_configured': bool(ai_assistant.api_key),
        'model_ready': bool(ai_assistant.model)
    }) 