#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
資料統計報告生成器
分析抓取資料的數量差異
支援正常模式和備份模式
"""

import os
import json
from pathlib import Path
from datetime import datetime, timedelta
import logging
import argparse

logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class DataStatisticsReporter:
    def __init__(self, base_dir: str = "backend", use_backup: bool = False):
        # 修正路徑解析
        if base_dir == "backend":
            # 如果當前目錄已經是backend，直接使用當前目錄
            self.base_dir = Path.cwd()
        else:
            self.base_dir = Path(base_dir)
        
        self.use_backup = use_backup
        
        # 正常目錄
        self.crawler_dir = self.base_dir / "crawler"
        self.data_dir = self.crawler_dir / "data"
        self.href_dir = self.crawler_dir / "href"
        
        # 備份目錄 - 修正路徑
        self.backup_dir = self.base_dir / "backups"
        self.backup_data_dir = self.backup_dir / "CRAWLER_HISTORY" / "data_history"
        self.backup_href_dir = self.backup_dir / "CRAWLER_HISTORY" / "href_history"
        
        self.end_date = datetime.now()
        self.start_date = self.end_date - timedelta(days=400)
        
        mode = "備份模式" if use_backup else "正常模式"
        logger.info(f"📊 資料統計報告初始化 - {mode}")
        logger.info(f"📁 基礎目錄: {self.base_dir}")
        
        if use_backup:
            logger.info(f"📁 備份資料目錄: {self.backup_data_dir}")
            logger.info(f"📁 備份目錄是否存在: {self.backup_data_dir.exists()}")
        else:
            logger.info(f"📁 正常資料目錄: {self.data_dir}")
    
    def get_legislators_list(self) -> list:
        legislators = set()
        
        if self.use_backup:
            # 備份模式：從備份目錄讀取
            legislators = self._get_legislators_from_backup()
        else:
            # 正常模式：從正常目錄讀取
            legislators = self._get_legislators_from_normal()
        
        legislators_list = sorted(list(legislators))
        logger.info(f"📋 找到 {len(legislators_list)} 位立委")
        return legislators_list
    
    def _get_legislators_from_normal(self) -> set:
        """從正常目錄獲取立委列表"""
        legislators = set()
        
        if self.data_dir.exists():
            for platform_dir in self.data_dir.iterdir():
                if platform_dir.is_dir():
                    for json_file in platform_dir.glob("*.json"):
                        legislators.add(json_file.stem)
        
        if self.href_dir.exists():
            for platform_dir in self.href_dir.iterdir():
                if platform_dir.is_dir():
                    for json_file in platform_dir.glob("*.json"):
                        legislators.add(json_file.stem)
        
        return legislators
    
    def _get_legislators_from_backup(self) -> set:
        """從備份目錄獲取立委列表"""
        legislators = set()
        
        logger.info(f"檢查備份資料目錄: {self.backup_data_dir}")
        if self.backup_data_dir.exists():
            backup_files = list(self.backup_data_dir.glob("*.json"))
            logger.info(f"找到 {len(backup_files)} 個備份檔案")
            
            for backup_file in backup_files:
                # 解析備份檔案名：platform_YYYYMMDD_legislator.json
                filename = backup_file.stem
                parts = filename.split("_")
                if len(parts) >= 3:
                    # 取最後一部分作為立委名（可能包含多個下劃線）
                    legislator_name = "_".join(parts[2:])
                    legislators.add(legislator_name)
                    logger.debug(f"從備份資料找到立委: {legislator_name}")
        else:
            logger.warning(f"備份資料目錄不存在: {self.backup_data_dir}")
        
        logger.info(f"檢查備份href目錄: {self.backup_href_dir}")
        if self.backup_href_dir.exists():
            backup_files = list(self.backup_href_dir.glob("*.json"))
            logger.info(f"找到 {len(backup_files)} 個備份href檔案")
            
            for backup_file in backup_files:
                filename = backup_file.stem
                parts = filename.split("_")
                if len(parts) >= 3:
                    legislator_name = "_".join(parts[2:])
                    legislators.add(legislator_name)
                    logger.debug(f"從備份href找到立委: {legislator_name}")
        else:
            logger.warning(f"備份href目錄不存在: {self.backup_href_dir}")
        
        logger.info(f"備份模式找到 {len(legislators)} 位立委")
        return legislators
    
    def count_urls_in_href(self, legislator_name: str) -> dict:
        url_counts = {}
        
        if self.use_backup:
            # 備份模式
            url_counts = self._count_urls_from_backup(legislator_name)
        else:
            # 正常模式
            url_counts = self._count_urls_from_normal(legislator_name)
        
        return url_counts
    
    def _count_urls_from_normal(self, legislator_name: str) -> dict:
        """從正常目錄計算URL數量"""
        url_counts = {}
        
        if self.href_dir.exists():
            for platform_dir in self.href_dir.iterdir():
                if platform_dir.is_dir():
                    platform_name = platform_dir.name
                    href_file = platform_dir / f"{legislator_name}.json"
                    
                    if href_file.exists():
                        try:
                            with open(href_file, 'r', encoding='utf-8') as f:
                                href_data = json.load(f)
                            
                            valid_urls = self._count_valid_urls(href_data)
                            url_counts[platform_name] = valid_urls
                            
                        except Exception as e:
                            logger.warning(f"讀取 {platform_name}/{legislator_name}.json 失敗: {e}")
        
        return url_counts
    
    def _count_urls_from_backup(self, legislator_name: str) -> dict:
        """從備份目錄計算URL數量"""
        url_counts = {}
        
        if self.backup_href_dir.exists():
            for backup_file in self.backup_href_dir.glob(f"*_{legislator_name}.json"):
                try:
                    filename = backup_file.stem
                    parts = filename.split("_")
                    if len(parts) >= 2:
                        platform_name = parts[0]
                        
                        with open(backup_file, 'r', encoding='utf-8') as f:
                            href_data = json.load(f)
                        
                        valid_urls = self._count_valid_urls(href_data)
                        url_counts[platform_name] = valid_urls
                        
                except Exception as e:
                    logger.warning(f"讀取備份檔案 {backup_file.name} 失敗: {e}")
        
        return url_counts
    
    def count_videos_in_data(self, legislator_name: str) -> dict:
        video_counts = {}
        
        if self.use_backup:
            # 備份模式
            video_counts = self._count_videos_from_backup(legislator_name)
        else:
            # 正常模式
            video_counts = self._count_videos_from_normal(legislator_name)
        
        return video_counts
    
    def _count_videos_from_normal(self, legislator_name: str) -> dict:
        """從正常目錄計算影片數量"""
        video_counts = {}
        
        if self.data_dir.exists():
            for platform_dir in self.data_dir.iterdir():
                if platform_dir.is_dir():
                    platform_name = platform_dir.name
                    data_file = platform_dir / f"{legislator_name}.json"
                    
                    if data_file.exists():
                        try:
                            with open(data_file, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            
                            if isinstance(data, list):
                                video_counts[platform_name] = len(data)
                            else:
                                video_counts[platform_name] = 0
                            
                        except Exception as e:
                            logger.warning(f"讀取 {platform_name}/{legislator_name}.json 失敗: {e}")
        
        return video_counts
    
    def _count_videos_from_backup(self, legislator_name: str) -> dict:
        """從備份目錄計算影片數量"""
        video_counts = {}
        
        if self.backup_data_dir.exists():
            for backup_file in self.backup_data_dir.glob(f"*_{legislator_name}.json"):
                try:
                    filename = backup_file.stem
                    parts = filename.split("_")
                    if len(parts) >= 2:
                        platform_name = parts[0]
                        
                        with open(backup_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        video_count = len(data) if isinstance(data, list) else 0
                        video_counts[platform_name] = video_count
                        
                except Exception as e:
                    logger.warning(f"讀取備份檔案 {backup_file.name} 失敗: {e}")
        
        return video_counts
    
    def _count_valid_urls(self, href_data) -> int:
        """計算400天內的有效URL數量"""
        valid_urls = 0
        
        if isinstance(href_data, list):
            for item in href_data:
                if isinstance(item, dict):
                    # 嘗試多種時間戳欄位名稱
                    timestamp = item.get('timestamp') or item.get('時間戳') or item.get('date') or item.get('post_time')
                    if timestamp:
                        try:
                            if isinstance(timestamp, str):
                                for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y%m%d']:
                                    try:
                                        url_date = datetime.strptime(timestamp, fmt)
                                        break
                                    except ValueError:
                                        continue
                                else:
                                    continue
                            else:
                                url_date = datetime.fromtimestamp(timestamp)
                            
                            if self.start_date <= url_date <= self.end_date:
                                valid_urls += 1
                        except Exception as e:
                            logger.debug(f"解析時間戳失敗: {timestamp}, 錯誤: {e}")
                            continue
        
        return valid_urls
    
    def analyze_legislator_data(self, legislator_name: str) -> dict:
        logger.info(f"📋 分析立委: {legislator_name}")
        
        url_counts = self.count_urls_in_href(legislator_name)
        video_counts = self.count_videos_in_data(legislator_name)
        
        analysis_result = {
            'legislator_name': legislator_name,
            'url_counts': url_counts,
            'video_counts': video_counts,
            'differences': {},
            'total_urls': sum(url_counts.values()),
            'total_videos': sum(video_counts.values()),
            'total_difference': 0
        }
        
        all_platforms = set(url_counts.keys()) | set(video_counts.keys())
        for platform in all_platforms:
            url_count = url_counts.get(platform, 0)
            video_count = video_counts.get(platform, 0)
            difference = url_count - video_count
            
            analysis_result['differences'][platform] = {
                'urls': url_count,
                'videos': video_count,
                'difference': difference
            }
        
        analysis_result['total_difference'] = analysis_result['total_urls'] - analysis_result['total_videos']
        
        return analysis_result
    
    def generate_report(self) -> dict:
        logger.info("🚀 開始生成資料統計報告...")
        
        legislators = self.get_legislators_list()
        logger.info(f"📋 找到 {len(legislators)} 位立委")
        
        all_results = []
        significant_differences = []
        
        for legislator in legislators:
            result = self.analyze_legislator_data(legislator)
            all_results.append(result)
            
            if result['total_difference'] > 10:
                significant_differences.append(result)
        
        report = {
            'generated_at': datetime.now().isoformat(),
            'mode': 'backup' if self.use_backup else 'normal',
            'time_range': {
                'start_date': self.start_date.isoformat(),
                'end_date': self.end_date.isoformat(),
                'days': 400
            },
            'summary': {
                'total_legislators': len(legislators),
                'legislators_with_significant_differences': len(significant_differences),
                'threshold': 10
            },
            'significant_differences': significant_differences,
            'all_results': all_results
        }
        
        return report
    
    def save_report(self, report: dict, output_file: str = None):
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            mode = "backup" if self.use_backup else "normal"
            output_file = f"data_statistics_report_{mode}_{timestamp}.json"
        
        output_path = self.base_dir / output_file
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 報告已儲存: {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"❌ 儲存報告失敗: {e}")
            return None
    
    def print_summary(self, report: dict):
        print("\n" + "="*80)
        print("📊 資料統計報告摘要")
        print("="*80)
        
        mode = "備份模式" if self.use_backup else "正常模式"
        print(f"🔧 讀取模式: {mode}")
        
        summary = report['summary']
        print(f"📋 總立委數量: {summary['total_legislators']}")
        print(f"⚠️  差異超過10的立委數量: {summary['legislators_with_significant_differences']}")
        print(f"📅 時間範圍: {report['time_range']['start_date'][:10]} 到 {report['time_range']['end_date'][:10]}")
        print(f"🔍 差異閾值: {summary['threshold']}")
        
        if report['significant_differences']:
            print("\n🚨 差異超過10的立委列表:")
            print("-" * 80)
            print(f"{'立委姓名':<15} {'總URL數':<10} {'總影片數':<10} {'差異':<10} {'詳細差異'}")
            print("-" * 80)
            
            for result in report['significant_differences']:
                name = result['legislator_name']
                total_urls = result['total_urls']
                total_videos = result['total_videos']
                difference = result['total_difference']
                
                details = []
                for platform, diff_info in result['differences'].items():
                    if diff_info['difference'] > 0:
                        details.append(f"{platform}:{diff_info['difference']}")
                
                details_str = ", ".join(details) if details else "無"
                
                print(f"{name:<15} {total_urls:<10} {total_videos:<10} {difference:<10} {details_str}")
        else:
            print("\n✅ 沒有發現差異超過10的立委")
        
        print("="*80)

    def identify_missing_data(self, report: dict) -> dict:
        """識別缺少資料的立委"""
        missing_data = {
            'legislators_with_missing_urls': [],
            'legislators_with_missing_videos': [],
            'legislators_needing_rerun': [],
            'summary': {
                'total_missing_urls': 0,
                'total_missing_videos': 0,
                'total_needing_rerun': 0
            }
        }
        
        for result in report['all_results']:
            legislator_name = result['legislator_name']
            url_counts = result['url_counts']
            video_counts = result['video_counts']
            
            # 檢查缺少URL的立委
            missing_urls = {}
            for platform, url_count in url_counts.items():
                if url_count == 0 and video_counts.get(platform, 0) > 0:
                    missing_urls[platform] = video_counts[platform]
            
            if missing_urls:
                missing_data['legislators_with_missing_urls'].append({
                    'legislator_name': legislator_name,
                    'missing_urls': missing_urls,
                    'total_missing': sum(missing_urls.values())
                })
                missing_data['summary']['total_missing_urls'] += sum(missing_urls.values())
            
            # 檢查缺少影片的立委
            missing_videos = {}
            for platform, video_count in video_counts.items():
                if video_count == 0 and url_counts.get(platform, 0) > 0:
                    missing_videos[platform] = url_counts[platform]
            
            if missing_videos:
                missing_data['legislators_with_missing_videos'].append({
                    'legislator_name': legislator_name,
                    'missing_videos': missing_videos,
                    'total_missing': sum(missing_videos.values())
                })
                missing_data['summary']['total_missing_videos'] += sum(missing_videos.values())
            
            # 檢查需要重新抓取的立委（URL和影片都為0）
            platforms_with_no_data = []
            for platform in set(url_counts.keys()) | set(video_counts.keys()):
                if url_counts.get(platform, 0) == 0 and video_counts.get(platform, 0) == 0:
                    platforms_with_no_data.append(platform)
            
            if platforms_with_no_data:
                missing_data['legislators_needing_rerun'].append({
                    'legislator_name': legislator_name,
                    'platforms': platforms_with_no_data
                })
                missing_data['summary']['total_needing_rerun'] += 1
        
        return missing_data
    
    def print_missing_data_analysis(self, missing_data: dict):
        """輸出缺少資料分析"""
        print("\n" + "="*80)
        print("🔍 缺少資料分析")
        print("="*80)
        
        summary = missing_data['summary']
        print(f"📊 缺少URL總數: {summary['total_missing_urls']}")
        print(f"📊 缺少影片總數: {summary['total_missing_videos']}")
        print(f"📊 需要重新抓取的立委數: {summary['total_needing_rerun']}")
        
        if missing_data['legislators_with_missing_urls']:
            print("\n🚨 缺少URL的立委:")
            print("-" * 60)
            for item in missing_data['legislators_with_missing_urls']:
                name = item['legislator_name']
                missing = item['missing_urls']
                total = item['total_missing']
                details = ", ".join([f"{platform}:{count}" for platform, count in missing.items()])
                print(f"{name:<15} 缺少 {total} 個URL ({details})")
        
        if missing_data['legislators_with_missing_videos']:
            print("\n🚨 缺少影片的立委:")
            print("-" * 60)
            for item in missing_data['legislators_with_missing_videos']:
                name = item['legislator_name']
                missing = item['missing_videos']
                total = item['total_missing']
                details = ", ".join([f"{platform}:{count}" for platform, count in missing.items()])
                print(f"{name:<15} 缺少 {total} 個影片 ({details})")
        
        if missing_data['legislators_needing_rerun']:
            print("\n🔄 需要重新抓取的立委:")
            print("-" * 60)
            for item in missing_data['legislators_needing_rerun']:
                name = item['legislator_name']
                platforms = ", ".join(item['platforms'])
                print(f"{name:<15} 平台: {platforms}")
        
        print("="*80)
        
        # 生成重抓建議
        if missing_data['legislators_needing_rerun']:
            print("\n💡 重抓建議:")
            print("-" * 60)
            print("以下立委建議重新抓取（URL和影片都為0）:")
            for item in missing_data['legislators_needing_rerun']:
                name = item['legislator_name']
                platforms = item['platforms']
                print(f"  - {name}: {', '.join(platforms)}")
            
            print("\n重抓命令範例:")
            print("cd backend && python main.py --legislator 立委姓名 --platforms youtube,ptt")
        
        if missing_data['legislators_with_missing_videos']:
            print("\n💡 影片抓取建議:")
            print("-" * 60)
            print("以下立委有URL但缺少影片，建議重新抓取影片內容:")
            for item in missing_data['legislators_with_missing_videos']:
                name = item['legislator_name']
                missing = item['missing_videos']
                platforms = list(missing.keys())
                print(f"  - {name}: {', '.join(platforms)}")
        
        print("="*80)

def main():
    parser = argparse.ArgumentParser(description='資料統計報告生成器')
    parser.add_argument('--backup', action='store_true', help='使用備份模式讀取資料')
    parser.add_argument('--output', type=str, help='指定輸出檔案名')
    parser.add_argument('--analyze-missing', action='store_true', help='分析缺少的資料並提供重抓建議')
    args = parser.parse_args()
    
    try:
        reporter = DataStatisticsReporter(use_backup=args.backup)
        report = reporter.generate_report()
        output_file = reporter.save_report(report, args.output)
        reporter.print_summary(report)
        
        # 分析缺少的資料
        if args.analyze_missing:
            missing_data = reporter.identify_missing_data(report)
            reporter.print_missing_data_analysis(missing_data)
        
        if output_file:
            print(f"\n📄 完整報告已儲存至: {output_file}")
        
    except Exception as e:
        logger.error(f"❌ 生成報告失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 