#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
數據導入腳本
只導入有罷免資料的立委數據到 MongoDB，並計算罷免狀態
"""

import json
import os
from pymongo import MongoClient
from config import get_mongo_config

def init_mongodb():
    """初始化 MongoDB 連接"""
    try:
        # 嘗試使用本地 MongoDB
        client = MongoClient('mongodb://localhost:27017', 
                           connectTimeoutMS=30000,
                           socketTimeoutMS=45000,
                           serverSelectionTimeoutMS=30000)
        db = client['legislator_recall']
        client.admin.command('ping')
        print("✅ 成功連接到本地 MongoDB")
        return client, db
    except Exception as e:
        print(f"❌ 本地 MongoDB 連接失敗: {e}")
        try:
            # 嘗試使用 Atlas
            mongo_conf = get_mongo_config()
            client = MongoClient(mongo_conf['MONGODB_URI'], **mongo_conf.get('MONGODB_OPTIONS', {}))
            db = client[mongo_conf['MONGODB_DBNAME']]
            client.admin.command('ping')
            print("✅ 成功連接到 MongoDB Atlas")
            return client, db
        except Exception as e2:
            print(f"❌ MongoDB Atlas 連接也失敗: {e2}")
            return None, None

def calculate_recall_status(recall_item):
    """根據罷免資料計算罷免狀態"""
    status = recall_item.get('狀態', '')
    progress = recall_item.get('進度', '0%')
    
    # 解析進度百分比
    try:
        progress_value = float(progress.replace('%', ''))
    except:
        progress_value = 0
    
    # 根據狀態和進度計算罷免狀態
    if '持續收件中' in status:
        if progress_value >= 100:
            return '二階連署進行中 (已達門檻)'
        else:
            return '二階連署進行中'
    elif '二階失敗' in status or '失敗' in status:
        return '二階失敗'
    elif '三階' in status:
        if '成功' in status:
            return '三階罷免成功'
        elif '失敗' in status:
            return '三階罷免失敗'
        else:
            return '三階投票進行中'
    elif '補件' in status:
        return '二階補件中'
    else:
        return '網路聲量調查'

def import_recall_legislators(db):
    """只導入有罷免資料的立委"""
    try:
        # 讀取罷免資料
        with open('data/recall_data_selenium.json', 'r', encoding='utf-8') as f:
            recall_data = json.load(f)
        
        # 清理和轉換數據
        legislators_to_insert = []
        
        for recall_item in recall_data:
            name = recall_item['姓名']
            constituency = recall_item['行政區']
            
            # 從立委基本資料中查找對應的 image_url 和黨籍
            image_url = ""
            party = "未知"
            
            # 讀取立委基本資料來獲取 image_url
            try:
                with open('data/now_legislator_info.json', 'r', encoding='utf-8') as f:
                    all_legislators = json.load(f)
                
                # 查找對應的立委
                for leg in all_legislators:
                    if leg['name'] == name:
                        image_url = leg.get('image_url', '')
                        # 提取黨籍信息
                        for item in leg.get('constituency', []):
                            if '黨籍：' in item:
                                party = item.replace('黨籍：', '')
                                break
                        break
            except Exception as e:
                print(f"⚠️ 無法讀取立委基本資料: {e}")
            
            # 計算罷免狀態
            recall_status = calculate_recall_status(recall_item)
            
            # 創建立委記錄
            legislator_record = {
                'name': name,
                'constituency': constituency,
                'party': party,
                'image_url': image_url,
                'recall_data': recall_item,
                'status': recall_status  # 添加計算出的罷免狀態
            }
            
            legislators_to_insert.append(legislator_record)
            print(f"✅ 準備導入立委: {name} ({constituency}) - {party} - {recall_status}")
        
        # 導入到數據庫
        legislators_col = db['legislators']
        
        # 清空現有數據
        print("🧹 清空現有數據...")
        legislators_col.delete_many({})
        
        # 插入新數據
        if legislators_to_insert:
            result = legislators_col.insert_many(legislators_to_insert)
            print(f"✅ 成功導入 {len(result.inserted_ids)} 筆有罷免資料的立委")
        else:
            print("⚠️ 沒有找到任何罷免資料")
        
    except Exception as e:
        print(f"❌ 導入罷免立委資料失敗: {e}")

def main():
    """主函數"""
    print("🚀 開始導入有罷免資料的立委...")
    
    # 初始化數據庫連接
    client, db = init_mongodb()
    if db is None:
        print("❌ 無法連接到數據庫")
        return
    
    try:
        # 導入有罷免資料的立委
        print("📊 導入罷免立委資料...")
        import_recall_legislators(db)
        
        print("✅ 數據導入完成！")
        
        # 顯示統計信息
        total_legislators = db['legislators'].count_documents({})
        legislators_with_recall = db['legislators'].count_documents({'recall_data': {'$exists': True}})
        
        print(f"📈 統計信息:")
        print(f"   - 總立委數量: {total_legislators}")
        print(f"   - 有罷免資料的立委: {legislators_with_recall}")
        
        # 顯示各狀態的統計
        status_counts = db['legislators'].aggregate([
            {'$group': {'_id': '$status', 'count': {'$sum': 1}}}
        ])
        
        print(f"📊 罷免狀態統計:")
        for status_count in status_counts:
            print(f"   - {status_count['_id']}: {status_count['count']} 人")
        
    except Exception as e:
        print(f"❌ 數據導入過程中發生錯誤: {e}")
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    main() 