#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL收集器模块
提供统一的URL收集接口，支持YouTube、PTT、Threads等平台
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入各平台的URL收集函数
from yt_crawler import search_youtube_videos
from ptt_crawler import fetch_ptt_links, setup_driver
from thread_crawler import crawl_threads_all_in_one

logger = logging.getLogger(__name__)

def collect_urls_for_legislator(legislator: str, platform: str, days: int) -> int:
    """
    为指定立委和平台收集URL
    
    Args:
        legislator: 立委姓名
        platform: 平台名称 (youtube, ptt, threads)
        days: 收集天数
        
    Returns:
        收集到的URL数量
    """
    try:
        logger.info(f"📡 开始收集 {legislator}-{platform} 的URL，天数: {days}")
        
        if platform == 'youtube':
            return collect_youtube_urls(legislator, days)
        elif platform == 'ptt':
            return collect_ptt_urls(legislator, days)
        elif platform == 'threads':
            return collect_threads_urls(legislator, days)
        else:
            logger.warning(f"⚠️ 不支持的平台: {platform}")
            return 0
            
    except Exception as e:
        logger.error(f"❌ 收集URL失败 {legislator}-{platform}: {e}")
        return 0

def collect_youtube_urls(legislator: str, days: int) -> int:
    """收集YouTube URL"""
    try:
        # 计算截止日期
        cutoff_date = None
        if days > 0:
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            logger.info(f"📅 YouTube URL收集: {legislator}, 截止日期: {cutoff_date}")
        
        # 调用YouTube搜索函数
        result = search_youtube_videos(legislator, headless=True, cutoff_date=cutoff_date)
        
        if isinstance(result, tuple) and len(result) == 2:
            filename, video_data = result
            url_count = len(video_data) if video_data else 0
            logger.info(f"✅ YouTube URL收集完成: {legislator}, 收集到 {url_count} 个URL")
            return url_count
        else:
            logger.warning(f"⚠️ YouTube URL收集异常: {legislator}")
            return 0
            
    except Exception as e:
        logger.error(f"❌ YouTube URL收集失败: {legislator} - {e}")
        return 0

def collect_ptt_urls(legislator: str, days: int) -> int:
    """收集PTT URL"""
    try:
        logger.info(f"📰 PTT URL收集: {legislator}")
        
        # 设置Chrome驱动
        driver = setup_driver(headless=True)
        
        try:
            # 调用PTT链接获取函数
            result = fetch_ptt_links(driver, legislator)
            
            if isinstance(result, tuple):
                url_count = result[0]  # 第一个元素是URL数量
            else:
                url_count = result if result else 0
                
            logger.info(f"✅ PTT URL收集完成: {legislator}, 收集到 {url_count} 个URL")
            return url_count
            
        finally:
            try:
                driver.quit()
            except:
                pass
                
    except Exception as e:
        logger.error(f"❌ PTT URL收集失败: {legislator} - {e}")
        return 0

def collect_threads_urls(legislator: str, days: int) -> int:
    """收集Threads URL"""
    try:
        logger.info(f"🧵 Threads URL收集: {legislator}")
        cutoff_date = None
        if days > 0:
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            logger.info(f"📅 Threads URL收集: {legislator}, 截止日期: {cutoff_date}")
        # Threads使用完整爬蟲，傳遞 cutoff_date 給 last_crawled_time
        result = crawl_threads_all_in_one(legislator, headless=True, last_crawled_time=cutoff_date)
        url_count = 0
        if isinstance(result, dict):
            url_count = result.get('url_count', 0)
        elif isinstance(result, int):
            url_count = result
        logger.info(f"✅ Threads URL收集完成: {legislator}, 收集到 {url_count} 个URL")
        return url_count
    except Exception as e:
        logger.error(f"❌ Threads URL收集失败: {legislator} - {e}")
        return 0

def get_available_platforms():
    """获取支持的平台列表"""
    return ['youtube', 'ptt', 'threads']

def validate_platform(platform: str) -> bool:
    """验证平台是否支持"""
    return platform in get_available_platforms()

if __name__ == "__main__":
    # 测试用例
    test_legislator = "高虹安"
    test_days = 30
    
    print("🧪 测试URL收集器...")
    
    for platform in get_available_platforms():
        print(f"\n测试 {platform} 平台...")
        count = collect_urls_for_legislator(test_legislator, platform, test_days)
        print(f"结果: {count} 个URL") 